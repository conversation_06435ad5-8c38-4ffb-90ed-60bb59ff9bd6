/**
 * axios相关api，请查阅官网：https://axios-http.com/
 *
 * 项目的所有接口的返回的基本数据为： { code: number; data: ResponseData; msg: string };
 *
 * 说明：接口响应成功时，code为0或showErrorMsg为true(默认为true)时返回接口响应数据对象的data属性
 *      其余返回接口响应数据。当使用post时，封装了 shadowExcludeEmpty(postData) 处理，调用接口无需再处理。
 *      get<ResponseData>, post<ResponseData>定义了响应数据的data泛型，在接口定义时传入，使用接口时ts能
 *      够很好的类型推导。
 *
 * 使用demo：
 * xx/api.ts const getXXData = (params: TypeOfParams) => get<TypeOfResData>('url', params);
 *           const doSomething = (data: TypeOfPostData) => post<TypeOfResData>('url', data);
 * */
import { defaultLang } from '@/langs';
import { getWXOauthUrl } from '@/layouts/components/App';
import { setTokenInCookie } from '@/pages/access-control/utils';
import { removeCookieForBsmsDriverToken } from '@/pages/bus-driver/_/utils';
import { setCookieForBsmsCarTeamToken } from '@/pages/bus-fleet/login/utils';
import { removeCookieForEmpSupplierToken } from '@/pages/employment/_/utils/token';
import { COOKIE_KEY_OBJ, removeCookie } from '@/utils';
import SSOSdk, { AuthError } from '@shein-components/sso-sdk';
import { t } from '@shein-bbl/react';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { sendError } from 'sheinq';
import { Dialog, Toast } from 'shineout-mobile';
import { formatDate } from './format';
import { getUlpAddress, isInSheinIm, shadowExcludeEmpty } from './share';
import { getInitialLang } from './store';

export interface IAxiosRequestConfig extends AxiosRequestConfig {
  /**
   * 是否将错误信息以IMessage提示，默认为true
   * 只返回服务器响应体({ code: number; data: any; msg: string })中的data
   * 为false时，返回完整的服务器响应体，此时可以自定义错误处理展示逻辑，比如确认弹窗
   * */
  showErrorMsg?: boolean;
  /**
   * 是否过滤post请求data数据中的空值（浅层排除对象中的空字符串、空对象、null值，并
   * 对字符串类型进行trim处理），默认为true
   * 对于某些编辑接口，非必填字段不传空给接口，接口不处理更新该字段，所以含有非必填字
   * 段的编辑接口需要提供 excludeEmpty 为 false
   * */
  excludeEmpty?: boolean;
}

const API_PREFIX = '/lpmpm/front';
const sso = new SSOSdk({
  ulpOrigin: getUlpAddress(),
  publicLoginEnabled: true,
});
const isInSheinChat = isInSheinIm();

const ErrorCodes = {
  REQUEST_SUCCESS: '0',
  INVALID_LOGIN_STATUS: '400106',
  DOOR_ACCESS_DENIED: '400108',
  DOOR_ACCESS_NO_AUTH: '400109',
  EMP_SUPPLIER_DENIED: '090050003',
  DOOR_BUS_FLEET_DENIED: '0900001010',
  DOOR_BUS_DRIVER_DENIED: '0900001009',
  BLACK_LIST_EMP_SUPPLIER: '090050001',
  INVALID_LOGIN_CODE2: '400107',
} as const;

/**
 * @description createAxiosInstance
 * @returns {unknown} desc
 */
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_PREFIX,
    headers: {
      'x-lt-language': getInitialLang() || defaultLang,
    },
    showErrorMsg: true,
  } as IAxiosRequestConfig);

  instance.interceptors.request.use(
    (config) => config,
    (error) => {
      Toast.fail(t('接口请求配置错误，请检查。'));
      console.error(error);
    },
  );

  instance.interceptors.response.use(handleResponse, handleErrorResponse);

  return instance;
};

const responseHandlers = {
  [ErrorCodes.REQUEST_SUCCESS]: handleSuccessResponse,
  [ErrorCodes.INVALID_LOGIN_STATUS]: isInSheinChat ? handleInvalidLogin : handleInvalidLoginStatus,
  [ErrorCodes.DOOR_ACCESS_DENIED]: handleDoorAccessDenied,
  [ErrorCodes.DOOR_ACCESS_NO_AUTH]: handleDoorAccessNoAuth,
  [ErrorCodes.EMP_SUPPLIER_DENIED]: handleEmpSupplierDenied,
  [ErrorCodes.DOOR_BUS_FLEET_DENIED]: handleDoorBusFleetDenied,
  [ErrorCodes.DOOR_BUS_DRIVER_DENIED]: handleDoorBusDriverDenied,
  [ErrorCodes.BLACK_LIST_EMP_SUPPLIER]: handleBlackListEmpSupplier,
  [ErrorCodes.INVALID_LOGIN_CODE2]: isInSheinChat ? handleInvalidLogin : handleInvalidLoginStatus,
  default: handleDefaultError,
};

/**
 * @description handleResponse
 * @param {unknown} response desc
 * @returns {unknown} desc
 */
const handleResponse = (response: AxiosResponse) => {
  const { data: serverRes, config } = response;
  const handler =
    responseHandlers[serverRes.code as keyof typeof responseHandlers] || responseHandlers.default;
  return handler(serverRes, config as IAxiosRequestConfig);
};

const handleErrorResponse = (error: any): Promise<never> => {
  // 接口请求成功，但http状态码不为200
  if (error.response) {
    const { status } = error.response;
    if (status === 401) {
      Toast.fail(t('您当前未登录或登录已过期，请重新登录。'));
      location.replace('/');
    } else if (status === 404) {
      Toast.fail(t('请求的接口未找到，请确认接口地址是否正确。'));
    } else if (status === 429) {
      Toast.fail(t('请求过于频繁，请稍后再试。'));
    } else if (status >= 500) {
      Toast.fail(`${t('请求失败，服务器错误(')}${error.message})。`);
    } else if (error.code === 'ERR_NETWORK') {
      Toast.info(t('网络开小差了哦～'));
    } else {
      Toast.fail(`${t('请求失败，网络错误(')}${error.message})。`);
    }
  } else if (error.request) {
    Toast.fail(t('请求失败，网络错误。'));
  } else {
    Toast.fail(t('请求失败，请检查配置。'));
  }
  console.error(error);
  return Promise.reject(error?.response?.data || null);
};

/**
 * @description handleSuccessResponse
 * @param {unknown} serverRes desc
 * @returns {unknown} desc
 */
function handleSuccessResponse(serverRes: any) {
  return serverRes.data;
}

/**
 * @description handleInvalidLoginStatus
 * @param {unknown} serverRes desc
 * @returns {unknown} desc
 */
function handleInvalidLoginStatus(serverRes: any) {
  if (window.location.hash.indexOf('#/chrome-debug') !== -1) {
    return Promise.reject(serverRes.data);
  } else {
    window.sessionStorage.removeItem('userInfo');
    location.replace(getWXOauthUrl());
    return Promise.reject(serverRes.data);
  }
}

/**
 * @description handleDoorAccessDenied
 * @param {unknown} serverRes desc
 * @returns {unknown} desc
 */
function handleDoorAccessDenied(serverRes: any) {
  setTokenInCookie('');
  const path = location.hash.replace('#/access-control/', '');
  if (path === 'appointment') {
    location.replace(`/#/access-control/appointment`);
  } else {
    location.replace(`/#/access-control/login?redirect=${path}`);
  }
  return Promise.reject(serverRes.data);
}

/**
 * @description handleDoorAccessNoAuth
 * @param {unknown} serverRes desc
 * @param {unknown} config desc
 * @returns {unknown} desc
 */
function handleDoorAccessNoAuth(serverRes: any, config: IAxiosRequestConfig) {
  if (config.showErrorMsg) {
    Toast.fail(t('您没有lpmp相关权限，请联系系统管理员处理'));
  }
  logNoAccessError(serverRes, config);
  return Promise.reject(serverRes);
}

/**
 * @description handleEmpSupplierDenied
 * @param {unknown} serverRes desc
 * @returns {unknown} desc
 */
function handleEmpSupplierDenied(serverRes: any) {
  Toast.fail(t('登录失效'));
  removeCookieForEmpSupplierToken();
  location.replace(`${location.pathname}#/employment/login`);
  return Promise.reject(serverRes.data);
}

/**
 * @description handleDoorBusFleetDenied
 * @param {unknown} serverRes desc
 * @returns {unknown} desc
 */
function handleDoorBusFleetDenied(serverRes: any) {
  setCookieForBsmsCarTeamToken('');
  removeCookie(COOKIE_KEY_OBJ.siamToken);
  location.replace(`${location.pathname}#/bus-fleet/login`);
  return Promise.reject(serverRes.data);
}

/**
 * @description handleDoorBusDriverDenied
 * @param {unknown} serverRes desc
 * @returns {unknown} desc
 */
function handleDoorBusDriverDenied(serverRes: any) {
  removeCookieForBsmsDriverToken();
  removeCookie(COOKIE_KEY_OBJ.siamToken);
  location.replace(`${location.pathname}#/bus-driver/login`);
  return Promise.reject(serverRes.data);
}

let empAbnormalTipShow = false;
/**
 * @description handleBlackListEmpSupplier
 * @param {unknown} serverRes desc
 * @param {unknown} config desc
 * @returns {unknown} desc
 */
function handleBlackListEmpSupplier(serverRes: any, config: IAxiosRequestConfig) {
  if (!empAbnormalTipShow && config.url !== '/emp/h5/login/createAuthToken') {
    /**
     * @description afterClose
     * @returns {unknown} desc
     */
    const afterClose = () => {
      removeCookieForEmpSupplierToken();
      location.replace(`${location.pathname}#/employment/login`);
    };

    Dialog.alert({
      title: t('提示'),
      message: serverRes?.msg || t('访问系统发生异常，请联系工作人员'),
      coveringClose: false,
      onOk() {
        afterClose();
      },
      onCancel() {
        afterClose();
      },
    });
    empAbnormalTipShow = true;
  } else if (config.url === '/emp/h5/login/createAuthToken') {
    Toast.fail(serverRes.msg, 3);
  }
  return Promise.reject(serverRes.data);
}

const NOT_FAIL_ICON_URL_PREFIX_MAP = ['/ams/'];
/**
 * @description handleDefaultError
 * @param {unknown} serverRes desc
 * @param {unknown} config desc
 * @returns {unknown} desc
 */
function handleDefaultError(serverRes: any, config: IAxiosRequestConfig) {
  if (config.showErrorMsg) {
    if (NOT_FAIL_ICON_URL_PREFIX_MAP.some((item) => config.url.indexOf(item) !== -1)) {
      Toast.info(serverRes.msg, 3);
    } else {
      Toast.fail(serverRes.msg, 3);
    }
    return Promise.reject(serverRes.data);
  }
  return Promise.reject(serverRes);
}

function logNoAccessError(serverRes: any, config: IAxiosRequestConfig) {
  let userInfo: any = {};
  try {
    userInfo = JSON.parse(window.sessionStorage.getItem('userInfo') || '{}');
  } catch {
    console.error('no user info');
  }
  const logContent = `${t('「LPMPM前端告警」\r\n异常页面：')}${
    window.location.hash.split('#')?.[1] || window.location.pathname
  }${t('\r\n异常接口：')}${config.url}${t('\r\n异常信息：未正确配置角色接口权限【')}${
    serverRes?.msg
  }${t('】\r\n异常时间：')}${formatDate(+new Date(), true)}${t('\r\n异常人：')}${userInfo.enName}(${
    userInfo.workNum
  })`;
  // 仅生产环境才发送埋点
  if (process.env.BUILD_TYPE === 'prod') {
    sendError(new Error(logContent), { logCenter: true });
  }
}

/**
 * @description handleInvalidLogin
 * @returns {unknown} desc
 */
function handleInvalidLogin() {
  throw new AuthError();
}

const instance = createAxiosInstance();

/**
 * @description _get
 * @param {unknown} url desc
 * @param {unknown} params desc
 * @param {unknown} config desc
 * @returns {unknown} desc
 */
const _get = <ResponseData>(
  url: string,
  params?: AxiosRequestConfig['params'],
  config?: Omit<IAxiosRequestConfig, 'url' | 'params'>,
): Promise<ResponseData> => {
  return instance.get(url, { params, ...(config || {}) });
};

export const get = isInSheinChat ? (sso.wrapRequest(_get) as typeof _get) : _get;

/**
 * @description _post
 * @param {unknown} url desc
 * @param {unknown} data desc
 * @param {unknown} config desc
 * @returns {unknown} desc
 */
const _post = <ResponseData>(
  url: string,
  data?: AxiosRequestConfig['data'],
  config?: Omit<IAxiosRequestConfig, 'url' | 'data'>,
): Promise<ResponseData> => {
  const filterData = shouldFilterData(config) ? filterEmptyValues(data) : data;
  return instance.post(url, filterData, { ...(config || {}) });
};

export const post = isInSheinChat ? (sso.wrapRequest(_post) as typeof _post) : _post;

/**
 * @description shouldFilterData
 * @param {unknown} config desc
 * @returns {unknown} desc
 */
const shouldFilterData = (config?: Omit<IAxiosRequestConfig, 'url' | 'data'>): boolean => {
  return typeof config?.excludeEmpty === 'undefined' || config?.excludeEmpty;
};

/**
 * @description filterEmptyValues
 * @param {unknown} data desc
 * @returns {unknown} desc
 */
const filterEmptyValues = (data: any): any => {
  return isObject(data) ? shadowExcludeEmpty(data) : data;
};

/**
 * @description isObject
 * @param {unknown} data desc
 * @returns {unknown} desc
 */
const isObject = (data: any): boolean => Object.prototype.toString.call(data) === '[object Object]';

export { API_PREFIX, instance as fetch };
