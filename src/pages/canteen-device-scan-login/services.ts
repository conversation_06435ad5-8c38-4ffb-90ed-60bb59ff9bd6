import { post } from '@/utils';

export interface IConfirmLoginParams {
  /** 微信登录授权码 */
  code: string;
  /** 二维码id */
  loginCodeId: string;
}

/**
 * @description 管理员/负责人登录设备
 * https://soapi.sheincorp.cn/application/3694/routes/245477/doc
 * @param {IConfirmLoginParams} params
 * @returns {Promise<IConfirmLoginResponse>} 返回值
 */
export const confirmDeviceLogin = (params: IConfirmLoginParams) => {
  return post<string>('/canteen/staff/login/authorization', params, {
    showErrorMsg: false,
  });
};
