import React, { useCallback, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { Toast } from 'shineout-mobile';
import { confirmDeviceLogin } from './services';

/**
 * 食堂设备扫码登录页面
 */
const CanteenDeviceScanLogin: React.FC = () => {
  usePageTitle(t('扫一扫'));
  const location = useLocation();

  const handleBack = () => {
    setTimeout(() => {
      // 使用 window.location.href 替换整个 URL，清除所有查询参数
      const baseUrl = window.location.origin;
      window.location.href = `${baseUrl}/#/my`;
    }, 1500);
  };

  /**
   * 处理登录确认
   */
  const handleConfirmLogin = useCallback(async (code: string, loginCodeId: string) => {
    confirmDeviceLogin({
      loginCodeId,
      code,
    })
      .then((res) => {
        console.log(res);
        if (res) {
          Toast.success(t('扫码成功'));
        }
      })
      .catch((e) => {
        console.error(e);
        Toast.fail(e?.msg || t('登录失败，请重试'));
      })
      .finally(() => {
        handleBack();
      });
  }, []);

  useEffect(() => {
    // 获取hash前的参数
    const mainUrlParams = new URLSearchParams(
      window.location.href.split('#')[0].split('?')[1] || '',
    );
    const code = mainUrlParams.get('code');

    // 获取hash后的参数
    const searchParams = new URLSearchParams(location.search);
    const loginCodeId = searchParams.get('loginCodeId');

    if (loginCodeId && code) {
      try {
        handleConfirmLogin(code, loginCodeId);
      } catch (error) {
        console.error(t('解析登录信息失败'), error);
        Toast.fail(t('无效的登录信息'));
        handleBack();
      }
    } else {
      Toast.fail(t('无效的登录信息'));
      handleBack();
    }
  }, [location.search, handleConfirmLogin]);

  return null;

  // // 获取hash前的参数
  // const mainUrlParams = new URLSearchParams(window.location.href.split('#')[0].split('?')[1] || '');
  // const codeFromMainUrl = mainUrlParams.get('code');

  // return (
  //   <div style={{ padding: '20px' }}>
  //     <h3>{t('完整URL信息：')}</h3>
  //     <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>{window.location.href}</pre>
  //     <h3>{t('Hash前的参数：')}</h3>
  //     <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>code: {codeFromMainUrl}</pre>
  //     <h3>{t('Hash后的参数：')}</h3>
  //     <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>{location.search}</pre>
  //   </div>
  // );
};

export default CanteenDeviceScanLogin;
