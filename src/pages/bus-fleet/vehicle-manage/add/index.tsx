import { useCallback, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import { Button, Cell, Field, Stepper, Toast } from 'shineout-mobile';
import DriverCell from '../components/DriverCell';
import LineCell from '../components/LineCell';
import { IAddVehicleParams } from '../interfaces';
import { addVehicle } from '../services';

/**
 * 1个汉字+1个字母+5/6个数字或字母（车牌号通用格式）
 */
export const carPalateCheck = (value) => {
  const regex = /^[\u4e00-\u9fa5][A-Za-z]{1}[A-Za-z0-9]{5,6}$/;
  if (value) {
    if (value.match(regex)) return true;
    else return false;
  } else {
    return false;
  }
};

const AddVehiclePage = () => {
  usePageTitle('添加车辆');
  const navigate = useNavigate();
  const [data, setData] = useState<IAddVehicleParams>({});

  const isCanSubmit = useMemo(() => {
    return (
      data?.licensePlate &&
      data?.seatNumber &&
      data?.seatNumber > 0 &&
      data?.lineIdList &&
      data?.lineIdList?.length > 0 &&
      (data?.driverId === undefined || data?.driverId !== undefined)
    );
  }, [data?.driverId, data?.licensePlate, data?.lineIdList, data?.seatNumber]);

  const handleSubmit = useCallback(() => {
    if (carPalateCheck(data?.licensePlate) === false) {
      Toast.fail('请输入正确的车牌号');
    } else {
      addVehicle(data).then(() => {
        Toast.success('添加成功');
        setData({});
        // 替换路由跳转
        navigate('/bus-fleet/vehicle-manage', {
          replace: true,
        });
      });
    }
  }, [data, navigate]);

  return (
    <div>
      <Field
        label={'车牌号'}
        value={data?.licensePlate}
        placeholder={'请输入车牌号'}
        maxLength={8}
        align="right"
        onChange={(e) =>
          setData({
            ...data,
            licensePlate: e.target.value,
          })
        }
      />
      <Cell
        label={'座次'}
        value={
          <Stepper
            value={data?.seatNumber}
            max={1000}
            min={0}
            onChange={(d) => {
              setData({
                ...data,
                seatNumber: d,
              });
            }}
          />
        }
        valueAlign="right"
      />
      <LineCell
        value={data?.lineIdList}
        onChange={(v) => {
          setData({
            ...data,
            lineIdList: v,
          });
        }}
      />
      <DriverCell
        value={data?.driverId}
        onChange={(v) =>
          setData({
            ...data,
            driverId: v,
          })
        }
      />

      <div
        style={{
          width: '100%',
          marginTop: 30,
          padding: 12,
        }}
      >
        <Button
          style={{
            width: '100%',
          }}
          disabled={!isCanSubmit}
          type="primary"
          onClick={() => handleSubmit()}
        >
          {'确认添加'}
        </Button>
      </div>
    </div>
  );
};
export default AddVehiclePage;
