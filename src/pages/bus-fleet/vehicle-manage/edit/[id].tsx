import { useMemo, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import { usePersistFn } from '@shein-lego/use';
import { useMount } from 'ahooks';
import { Button, Toast } from 'shineout-mobile';
import DriverCell from '../components/DriverCell';
import LineCell from '../components/LineCell';
import { IVehicleDetail } from '../interfaces';
import { editVehicle, getVehicleDetail } from '../services';

/**
 * @description EditVehiclePage
 * @returns {unknown} desc
 */
const EditVehiclePage = () => {
  usePageTitle('编辑车辆信息');

  const { id } = useParams<{ id: string }>();
  const [details, setDetails] = useState<IVehicleDetail>(null);
  const originalDetails = useRef<{
    driverId: number;
    lineIdList: number[];
  }>(null);
  const navigate = useNavigate();

  useMount(() => {
    getVehicleDetail(Number(id)).then((res) => {
      originalDetails.current = {
        driverId: res.driverId,
        lineIdList: res.areaLineVoList.map((item) => item.lineId),
      };
      setDetails(res);
    });
  });

  const isCanSubmit = useMemo(() => {
    if (
      details?.driverId === originalDetails.current?.driverId &&
      details?.areaLineVoList?.length === 0
    ) {
      return false;
    } else {
      return true;
    }
  }, [details?.areaLineVoList, details?.driverId]);

  const handleSubmit = usePersistFn(() => {
    editVehicle({
      id: Number(id),
      driverId: details.driverId,
      lineIdList: details.areaLineVoList.map((item) => item.lineId),
    }).then(() => {
      Toast.success('修改成功');
      navigate('/bus-fleet/vehicle-manage');
    });
  });

  return (
    <div>
      <div
        style={{
          display: 'flex',
          marginBottom: 8,
          backgroundColor: 'white',
          flexDirection: 'column',
          gap: '8px',
          padding: '10px',
        }}
      >
        <div style={{ color: '#666C7C' }}>
          {'车辆系统编码: '}
          <span style={{ color: '#141737', display: 'inline-block', marginLeft: 5 }}>
            {details?.vehicleCode}
          </span>
        </div>
        <div style={{ color: '#666C7C' }}>
          {'车载设备编号: '}
          <span style={{ color: '#141737', display: 'inline-block', marginLeft: 5 }}>
            {details?.deviceCode !== undefined &&
            details?.deviceCode !== null &&
            details?.deviceCode !== ''
              ? details.deviceCode?.slice(-8)
              : '-'}
          </span>
        </div>
      </div>

      <LineCell
        value={details?.areaLineVoList.map((item) => item.lineId)}
        onChange={(v) => {
          setDetails({
            ...details,
            areaLineVoList: v.map((item) => ({
              lineId: item,
            })),
          });
        }}
      />
      <DriverCell
        value={details?.driverId}
        onChange={(v) =>
          setDetails({
            ...details,
            driverId: v,
          })
        }
      />

      <div style={{ color: '#F59A23', fontSize: 12, padding: 12 }}>
        注意：当车辆更换驾驶员时，已分配给该车辆的任务将会转移至新驾驶员
      </div>
      <div style={{ marginTop: 0, width: '100%', padding: 12 }}>
        <Button
          type="primary"
          style={{ width: '100%' }}
          onClick={handleSubmit}
          disabled={!isCanSubmit}
        >
          {'确认修改'}
        </Button>
      </div>
    </div>
  );
};
export default EditVehiclePage;
