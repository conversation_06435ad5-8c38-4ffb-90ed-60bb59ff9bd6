import { ReactNode } from 'react';
import { IVehicleDetail, IVehicleListItem, VehicleStatusEnum } from '../../interfaces';

export interface IVehicleItemProps {
  item: IVehicleListItem;
  index: number;
  opt: IOptType[];
  addDetailInfo: (data: IVehicleDetail) => void;
  onToggleStatus: (state: boolean) => void;
}

export type IOptType = {
  title: string;
  callback: (id?: number) => void;
  type: 'primary' | 'default' | 'danger';
  icon: string;
};

export type IFooterProps = {
  id: number;
  status: VehicleStatusEnum;
  onToggle: (status: boolean) => void;
  opt: IOptType[];
  index: number;
};

export type ItemProps = {
  label: string;
  value: string | ReactNode;
};
