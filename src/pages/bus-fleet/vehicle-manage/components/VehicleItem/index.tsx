import { useState } from 'react';
import AutoGapLabel from '@/_/components/AutoGapLabel';
import CarPlateAndSeatItem from '@/pages/bus-fleet/_/components/CarPlateAndSeatItem';
import Icon from '@shein-components/Icon';
import { Button, Popover, Switch, Tag } from 'shineout-mobile';
import { VehicleStatusEnum } from '../../interfaces';
import { getVehicleDetail } from '../../services';
import styles from './index.less';
import { IFooterProps, ItemProps, IVehicleItemProps } from './interfaces';

const Item: React.FC<ItemProps> = (props) => {
  const { label, value } = props;
  return (
    <div className={styles.item}>
      {/* <div className={styles.label}>{label}</div> */}
      <AutoGapLabel className={styles.label} label={label} />
      <div className={styles.value}>{value}</div>
    </div>
  );
};

/**
 * @description Footer
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Footer: React.FC<IFooterProps> = (props) => {
  const { status, onToggle, id, opt, index } = props;

  return (
    <div className={styles.footer}>
      <div className={styles.more}>
        {'更多'}
        <Popover.PopAction
          position={index === 0 ? 'bottom-left' : 'top-left'}
          actionItemStyle={{
            textAlign: 'left',
          }}
          renderItem={(item) => (
            <Button
              text
              type={item.type}
              onClick={() => {
                item.callback(id);
              }}
            >
              <Icon style={{ fontSize: 16, marginRight: 4 }} name={item.icon} />
              <span>{item.title}</span>
            </Button>
          )}
          data={opt}
        />
      </div>
      <div className={styles.toggleStatus}>
        <span className={styles.text}>
          {'切换为'}
          {status === VehicleStatusEnum.DISABLED ? '启用' : '停用'}
        </span>
        <Switch
          onChange={onToggle}
          content={['启用', '停用']}
          value={status === VehicleStatusEnum.ENABLED}
          size={20}
        />
      </div>
    </div>
  );
};

/**
 * @description VehicleItem
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const VehicleItem: React.FC<IVehicleItemProps> = (props) => {
  const { item, opt, addDetailInfo, onToggleStatus, index } = props;
  const [toggle, setToggle] = useState(false);
  const [loading, setLoading] = useState(false);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.carPlate}>
          <CarPlateAndSeatItem
            smallSize
            item={{
              licensePlate: item.licensePlate,
              seatNumber: item.seatNumber,
            }}
          />
          {item.vehicleStatus === VehicleStatusEnum.DISABLED ? (
            <Tag className={styles.tag} color="#FAE9E9" textColor="#F96156">
              {'停用'}
            </Tag>
          ) : (
            <Tag className={styles.tag} color="#E3EDFA" textColor="#197AFA">
              {'启用'}
            </Tag>
          )}
        </div>
        <div className={styles.toggle}>
          {toggle ? (
            <Button type="primary" loading={loading} text onClick={() => setToggle(!toggle)}>
              {'收起'}
            </Button>
          ) : (
            <Button
              type="primary"
              text
              loading={loading}
              onClick={() => {
                if (!item.$openedDetail) {
                  setLoading(true);
                  getVehicleDetail(item.id)
                    .then((res) => {
                      addDetailInfo(res);
                      setToggle(true);
                    })
                    .finally(() => {
                      setLoading(false);
                    });
                } else {
                  setToggle(true);
                }
              }}
            >
              {'展开'}
            </Button>
          )}
        </div>
      </div>
      {toggle && (
        <div>
          <Item label={'车辆编码'} value={item.vehicleCode} />
          <Item
            label={'驾驶人员'}
            value={item.driverName ? `${item.driverName}(${item.driverPhone})` : '-'}
          />
          <Item
            label={'设备编号'}
            value={
              item.deviceCode !== null && item.deviceCode !== '' && item?.deviceCode !== undefined
                ? item.deviceCode?.slice(-8)
                : '-'
            }
          />
          <Item
            label={'绑定线路'}
            value={
              item?.areaLineVoList === null ||
              (Array.isArray(item?.areaLineVoList) && item?.areaLineVoList.length === 0)
                ? '-'
                : item?.areaLineVoList?.map((item) => (
                    <Tag
                      color="#F4F5F8"
                      textColor="#35383D"
                      style={{ marginRight: 8 }}
                      key={item.id}
                    >
                      {item.lineName}
                    </Tag>
                  ))
            }
          />
        </div>
      )}
      <Footer
        id={item.id}
        index={index}
        opt={opt}
        status={item.vehicleStatus}
        onToggle={(v) => onToggleStatus(v)}
      />
    </div>
  );
};

export default VehicleItem;
