import { useState } from 'react';
import { usePersistFn } from '@shein-lego/use';
import { useMount } from 'ahooks';
import { Cell, Drawer, Tag, Toast, TreeSelect } from 'shineout-mobile';
import { IAreaLineTreeItem } from '../../interfaces';
import { getAreaLineTree } from '../../services';
import styles from './index.less';

/**
 * @description LineCell
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const LineCell: React.FC<{
  value: number[];
  onChange: (v: number[]) => void;
}> = ({ value, onChange }) => {
  const [areaLineVisible, setAreaLineVisible] = useState(false);
  const [tempAreaLine, setTempAreaLine] = useState<number[]>([]);
  const [areaLineTree, setAreaLineTree] = useState<IAreaLineTreeItem[]>([]);

  const getLineNameById = usePersistFn((id: number) => {
    const child = areaLineTree.flatMap((item) => item.children);
    return child.find((item) => item.id === id)?.name;
  });

  useMount(() => {
    getAreaLineTree().then((res) => {
      setAreaLineTree(res);
    });
  });

  return (
    <>
      <Cell
        label={'绑定线路'}
        isLink
        valueAlign="right"
        onClick={() => {
          setAreaLineVisible(true);
          setTempAreaLine(value || []);
        }}
        value={
          value?.length ? (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-end',
                gap: 4,
              }}
            >
              {value?.map((item) => (
                <Tag
                  key={item}
                  color="#F4F5F8"
                  textColor="#35383D"
                  style={{ width: 'fit-content', textAlign: 'left' }}
                >
                  {getLineNameById(item)}
                </Tag>
              ))}
            </div>
          ) : (
            '请选择绑定线路'
          )
        }
      />
      <Drawer
        visible={areaLineVisible}
        onClose={() => {
          setAreaLineVisible(false);
          setTempAreaLine([]);
        }}
        position="bottom"
      >
        <TreeSelect
          className={styles.TreeSelect}
          title=""
          multiple
          data={areaLineTree}
          format="id"
          renderItem="name"
          disabled={(d) => d?.status === 0}
          keygen="id"
          value={tempAreaLine}
          onCancel={() => {
            setAreaLineVisible(false);
            setTempAreaLine([]);
          }}
          onChange={(val) => {
            if (val?.length > 10) {
              Toast.info('最多选择10条线路');
              return;
            }
            setTempAreaLine(val);
          }}
          onOk={() => {
            const arrFlat = areaLineTree.flatMap((item) =>
              item.children.map((c) => {
                return {
                  ...c,
                  parentId: item.id,
                };
              }),
            );
            const group = new Set();
            tempAreaLine.forEach((item) => {
              const parent = arrFlat.find((c) => c.id === item)?.parentId;
              group.add(parent);
            });
            if (group.size > 1) {
              Toast.info('请选择同一区域的线路');
              return;
            }
            setAreaLineVisible(false);
            onChange(tempAreaLine);
            setTempAreaLine([]);
          }}
        />
      </Drawer>
    </>
  );
};

export default LineCell;
