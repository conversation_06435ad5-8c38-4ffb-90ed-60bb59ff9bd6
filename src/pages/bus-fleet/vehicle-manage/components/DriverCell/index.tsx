import { useState } from 'react';
import Icon from '@shein-components/Icon';
import { useMount } from 'ahooks';
import { Cell, Drawer, NPicker, Toast } from 'shineout-mobile';
import { IDriverItem } from '../../interfaces';
import { getDriverList } from '../../services';

/**
 * @description DriverCell
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const DriverCell: React.FC<{
  value: number;
  onChange: (v: number) => void;
}> = ({ value, onChange }) => {
  const [driverVisible, setDriverVisible] = useState(false);
  const [driverList, setDriverList] = useState<IDriverItem[]>([]);

  useMount(() => {
    getDriverList().then((res) => {
      setDriverList(res);
    });
  });

  return (
    <>
      <Cell
        label={'驾驶员'}
        valueAlign="right"
        isLink
        onClick={() => {
          setDriverVisible(true);
        }}
        value={
          value ? (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-end',
                height: 20,
              }}
            >
              {driverList.find((item) => item.id === value)?.driverName}
              <Icon
                name="close - multicolor"
                style={{ marginLeft: 8 }}
                onClick={(e) => {
                  onChange(null);
                  e.stopPropagation();
                }}
              />
            </div>
          ) : (
            '请选择驾驶员'
          )
        }
      />
      <Drawer
        height={350}
        visible={driverVisible}
        onClose={() => setDriverVisible(false)}
        position="bottom"
      >
        <NPicker
          data={driverList}
          disabled={(d) => !d.checkStatus}
          renderItem={(d) => {
            return `${d.driverName}(${d.driverPhone})`;
          }}
          onCancel={() => {
            setDriverVisible(false);
          }}
          onFilter={(text) => {
            return (data) => data.driverName?.indexOf(text) >= 0;
          }}
          onOk={(v) => {
            if (v?.checkStatus === true) {
              setDriverVisible(false);
              onChange(v?.id);
            } else {
              if (
                driverList.every((item) => item.checkStatus === false) ||
                v === undefined ||
                v === null ||
                v?.length === 0
              )
                return setDriverVisible(false);
              Toast.fail('该驾驶员已被绑定');
            }
          }}
        />
      </Drawer>
    </>
  );
};

export default DriverCell;
