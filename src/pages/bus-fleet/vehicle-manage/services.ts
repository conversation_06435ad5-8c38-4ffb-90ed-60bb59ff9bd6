import { get, post } from '@/utils';
import {
  IAddVehicleParams,
  IAreaLineTreeItem,
  IDriverItem,
  IEditVehicleParams,
  IVehicleDetail,
  IVehicleListItem,
  VehicleStatusEnum,
} from './interfaces';

/**
 * 车辆管理列表
 * https://soapi.sheincorp.cn/application/3694/routes/184982/doc
 * @returns
 */
export const getVehicleList = () => {
  return get<IVehicleListItem[]>('/bsms/carTeam/vehicle/list');
};

/**
 * 区域-线路下拉框
 * https://soapi.sheincorp.cn/application/3694/routes/184984/doc
 * @returns
 */
export const getAreaLineTree = () => {
  return get<IAreaLineTreeItem[]>('/bsms/carTeam/vehicle/areaLineTree');
};

/**
 * 车辆管理详情
 * https://soapi.sheincorp.cn/application/3694/routes/184985/doc
 * @param id
 * @returns
 */
export const getVehicleDetail = (id: number) => {
  return get<IVehicleDetail>('/bsms/carTeam/vehicle/detail', {
    id,
  });
};

/**
 * 车辆切换状态
 * https://soapi.sheincorp.cn/application/3694/routes/184986/doc
 * @param params
 * @returns
 */
export const switchVehicleStatus = (params: {
  vehicleId: number;
  workStatus: VehicleStatusEnum;
}) => {
  return post<boolean>('/bsms/carTeam/vehicle/switchVehicleStatus', params);
};

/**
 * 新增车辆
 * https://soapi.sheincorp.cn/application/3694/routes/184987/doc
 * @param params
 * @returns
 */
export const addVehicle = (params: IAddVehicleParams) => {
  return post<boolean>('/bsms/carTeam/vehicle/addVehicle', params);
};

/**
 * 编辑车辆信息
 * https://soapi.sheincorp.cn/application/3694/routes/184988/doc
 * @param params
 * @returns
 */
export const editVehicle = (params: IEditVehicleParams) => {
  return post<boolean>('/bsms/carTeam/vehicle/editVehicle', params);
};

/**
 * 删除车辆
 * https://soapi.sheincorp.cn/application/3694/routes/184989/doc
 * @param id
 * @returns
 */
export const deleteVehicle = (id: number) => {
  return post<boolean>('/bsms/carTeam/vehicle/deletedVehicle', {
    id,
  });
};

/**
 * 解绑司机
 * https://soapi.sheincorp.cn/application/3694/routes/184990/doc
 * @param id
 * @returns
 */
export const unbindDriver = (id: number) => {
  return post<boolean>('/bsms/carTeam/vehicle/unbindDriver', {
    id,
  });
};

/**
 * 解绑设备
 * https://soapi.sheincorp.cn/application/3694/routes/184991/doc
 * @param id
 * @returns
 */
export const unbindDevice = (id: number) => {
  return post<boolean>('/bsms/carTeam/vehicle/unbindDevice', {
    id,
  });
};

/**
 * 司机下拉列表
 * https://soapi.sheincorp.cn/application/3694/routes/184992/doc
 * @returns
 */
export const getDriverList = () => {
  return get<IDriverItem[]>('/bsms/carTeam/vehicle/driverList');
};
