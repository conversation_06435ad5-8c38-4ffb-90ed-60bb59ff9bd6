import { useCallback, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import EmptyBus from '@/pages/bus-employee/components/_/EmptyBus';
import LoadingBus from '@/pages/bus-employee/components/_/LoadingBus';
import { usePersistFn } from '@shein-lego/use';
import { useMount } from 'ahooks';
import { Button, Dialog, DropdownMenu, SearchBar, Toast } from 'shineout-mobile';
import VehicleItem from './components/VehicleItem';
import styles from './index.less';
import { IVehicleListItem, VehicleStatusEnum } from './interfaces';
import {
  deleteVehicle,
  getVehicleList,
  switchVehicleStatus,
  unbindDevice,
  unbindDriver,
} from './services';

const VehicleManagePage = () => {
  usePageTitle('车辆管理');

  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState<string>(null);
  const [statusType, setStatusType] = useState<VehicleStatusEnum>(null);
  const [list, setList] = useState<IVehicleListItem[]>([]);

  const getList = usePersistFn(() => {
    setLoading(true);
    getVehicleList()
      .then((res) => {
        setList(res);
      })
      .finally(() => {
        setLoading(false);
      });
  });

  const searchedList = useMemo(() => {
    return list
      .filter((item) => {
        if (searchValue === null) return true;
        return item.licensePlate.indexOf(searchValue) !== -1;
      })
      .filter((item) => {
        if (statusType === null) return true;
        return item.vehicleStatus === statusType;
      });
  }, [list, searchValue, statusType]);

  const vehicleNum = useMemo(() => {
    return searchedList.length;
  }, [searchedList.length]);

  const vehicleWorkNum = useMemo(() => {
    return searchedList.filter((item) => item.vehicleStatus === VehicleStatusEnum.ENABLED).length;
  }, [searchedList]);

  useMount(() => {
    getList();
  });

  const handleAddDetailInfo = usePersistFn((data) => {
    const newList = list.map((item) => {
      if (item.id === data.id) {
        return {
          ...item,
          ...data,
          $openedDetail: true,
        };
      }
      return item;
    });
    setList(newList);
  });

  const handleUnbindDriver = useCallback(
    (id) => {
      unbindDriver(id).then(() => {
        Toast.success('解绑成功');
        getList();
      });
    },
    [getList],
  );

  const handleUnbindDevice = useCallback(
    (id) => {
      unbindDevice(id).then(() => {
        Toast.success('解绑成功');
        getList();
      });
    },
    [getList],
  );

  const handleDeleteVehicle = useCallback(
    (id) => {
      Dialog.confirm({
        title: '删除车辆',
        message: '是否删除该车辆',
        onOk: () => {
          deleteVehicle(id).then(() => {
            Toast.success('操作成功');
            getList();
          });
        },
      });
    },
    [getList],
  );

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <SearchBar
          // autoFocus
          clearable={false}
          placeholder={'请输入车牌号进行搜索'}
          onSearch={(v) => setSearchValue(v)}
          onCancel={() => setSearchValue(null)}
        />

        <DropdownMenu>
          <div className={styles.item}>
            {'车辆数量：'}
            <span>{vehicleNum}</span>
          </div>
          <div className={styles.item}>
            {'启用数量：'}
            <span>{vehicleWorkNum}</span>
          </div>
          <DropdownMenu.Item
            onChange={(v) => {
              setStatusType(v);
            }}
            className={styles.drawerDown}
            title={
              statusType === null
                ? '全部'
                : statusType === VehicleStatusEnum.ENABLED
                ? '启用'
                : '停用'
            }
            data={[
              {
                value: null,
                label: '全部',
              },
              {
                value: VehicleStatusEnum.DISABLED,
                label: '停用',
              },
              {
                value: VehicleStatusEnum.ENABLED,
                label: '启用',
              },
            ]}
            format="value"
            renderItem={(item) => item.label}
          />
        </DropdownMenu>
      </div>

      <div className={styles.content}>
        {!loading &&
          searchedList.map((item, index) => (
            <VehicleItem
              key={item.id}
              index={index}
              item={item}
              addDetailInfo={handleAddDetailInfo}
              onToggleStatus={(state) => {
                switchVehicleStatus({
                  vehicleId: item.id,
                  workStatus: state ? VehicleStatusEnum.ENABLED : VehicleStatusEnum.DISABLED,
                }).then(() => {
                  Toast.success('切换成功');
                  setList(
                    list.map((i) => {
                      return i.id === item.id
                        ? {
                            ...i,
                            vehicleStatus: state
                              ? VehicleStatusEnum.ENABLED
                              : VehicleStatusEnum.DISABLED,
                          }
                        : i;
                    }),
                  );
                });
              }}
              opt={[
                {
                  title: '编辑信息',
                  icon: 'editor',
                  type: 'primary',
                  callback: (id) => navigate(`/bus-fleet/vehicle-manage/edit/${id}`),
                },
                {
                  title: '解绑司机',
                  icon: 'unlock',
                  type: 'primary',
                  callback: (id) => {
                    handleUnbindDriver(id);
                  },
                },
                {
                  title: '解绑车载设备',
                  icon: 'unlock',
                  type: 'primary',
                  callback: (id) => {
                    handleUnbindDevice(id);
                  },
                },
                {
                  title: '删除车辆',
                  icon: 'delete',
                  type: 'danger',
                  callback: (id) => {
                    handleDeleteVehicle(id);
                  },
                },
              ]}
            />
          ))}
        {!loading && searchedList.length === 0 && <EmptyBus text={'暂无数据'} />}
        {loading && <LoadingBus />}
      </div>

      <div className={styles.add}>
        <Button
          style={{
            width: '100%',
          }}
          type="primary"
          onClick={() => navigate('/bus-fleet/vehicle-manage/add')}
        >
          {'添加车辆'}
        </Button>
      </div>
    </div>
  );
};
export default VehicleManagePage;
