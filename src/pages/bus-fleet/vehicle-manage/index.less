.container {
  position: relative;
  padding-bottom: 70px;

  .header {
    position: relative;

    .item {
      margin-left: 16px;
      color: #666c7c;

      span {
        color: #141737;
      }
    }

    .drawerDown {
      &:global(.sm-dropdown-menu-item) {
        padding-right: 12px;
        text-align: right;
      }
    }
  }

  .content {
    padding: 12px;
  }

  .add {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 70px;
    max-width: 750px;
    padding: 12px;
    background-color: white;
  }
}
