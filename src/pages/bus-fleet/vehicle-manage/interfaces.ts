// export type ICarItemType = {
//   id: number;
//   carPlate: string;
//   seat: number;
//   no: string;
//   driver: {
//     workNum: string;
//     name: string;
//   };
//   deviceNo: string;
//   routeLine: {
//     departure: string;
//     destination: string;
//   }[];
//   status: CarStatusEnum;
// };

export type IVehicleListItem = {
  /** id */
  id?: number;
  /** 车牌号 */
  licensePlate: string;
  /** 座位数量 */
  seatNumber: number;
  /** 车辆状态：0-禁用，1-启用 */
  vehicleStatus: VehicleStatusEnum;

  /** 司机名称 */
  driverName?: string;
  /** 手机号（解密） */
  driverPhone?: string;
  /** 车辆编码 */
  vehicleCode?: string;
  /** 车载设备编号 */
  deviceCode?: string;
  /** 线路列表 */
  areaLineVoList?: IAreaLineVoListItem[];

  $openedDetail?: boolean;
};

export enum VehicleStatusEnum {
  DISABLED = 0,
  ENABLED = 1,
}

export interface IAreaLineTreeItem {
  /** 区域/线路id */
  id?: number;
  /** 区域/线路name */
  name?: string;
  /** 区域/线路status */
  status?: number;
  /** 子节点 */
  children: Omit<IAreaLineTreeItem, 'children'>[];
}

export interface IAreaLineVoListItem {
  /** 区域id */
  id?: number;
  /** 区域name */
  name?: string;
  /** 区域status */
  status?: number;
  /** 线路id */
  lineId?: number;
  /** 线路name */
  lineName?: string;
  /** 线路status */
  lineStatus?: number;
}

export interface IVehicleDetail {
  /** id */
  id?: number;
  /** 司机名称 */
  driverName?: string;
  /** 手机号（解密） */
  driverPhone?: string;
  /** 司机id */
  driverId?: number;
  /** 车辆编码 */
  vehicleCode?: string;
  /** 车载设备编号 */
  deviceCode?: string;
  /** 座位数 */
  seatNumber?: number;
  /** 线路列表 */
  areaLineVoList: IAreaLineVoListItem[];
}

export interface IAddVehicleParams {
  /** 车牌号 */
  licensePlate?: string;
  /** 座位数量 */
  seatNumber?: number;
  /** 司机id */
  driverId?: number;
  /** 线路id列表 */
  lineIdList?: number[];
}

export interface IEditVehicleParams {
  /**  */
  id?: number;
  /** 司机id */
  driverId?: number;
  /** 线路id列表 */
  lineIdList?: number[];
}

export interface IDriverItem {
  /** id */
  id?: number;
  /** 司机名称 */
  driverName?: string;
  /** 司机电话 */
  driverPhone?: string;
  /** 可选：true可选 */
  checkStatus?: boolean;
}
