/* stylelint-disable max-nesting-depth */
.container {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: white;

  .headerImg {
    width: 100%;
    background-size: contain;
    background-repeat: no-repeat;
  }

  .title {
    position: absolute;
    top: 5rem;
    left: 1.5rem;
    font-size: 2.5rem;
    font-weight: 500;
    color: white;
  }

  .formContainer {
    position: relative;
    padding: 0 15px;
    background-color: white;

    .formBox {
      position: relative;

      /* stylelint-disable-next-line selector-id-pattern */
      #captchaBox {
        position: relative;
        margin: 0 auto;
        margin-top: 1rem;
        margin-bottom: 1rem;
        text-align: center;
      }

      .icon {
        position: relative;
        top: 1px;
        margin-right: 5px;
      }

      .timeButton {
        position: absolute;
        top: 6px;
        right: 12px;
        width: 82px;
        color: #197afa;
        background-color: #e3edfa;
        border-color: #e3edfa;
      }

      .codeButton {
        position: absolute;
        top: 6px;
        right: 12px;
        width: 82px;
      }
    }

    .checkBox {
      position: relative;
      width: 100%;
      padding-left: 15px;
      margin-top: 1rem;
      font-size: 12px;
    }

    .submitBox {
      position: relative;
      margin-top: 8rem;
      margin-top: 3rem;
      margin-bottom: 2rem;
      text-align: center;

      .submitButton {
        width: calc(100% - 24px);
        margin-top: 5rem;
        // margin-left: 2%;
        margin-left: 12px;
      }
    }
  }
}
