import { MutableRefObject, useMemo, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import { COOKIE_KEY_OBJ, setCookie } from '@/utils';
import Icon from '@shein-components/Icon';
import '@/_/assets/js/gt';
import { usePersistFn } from '@shein-lego/use';
import { Button, Checkbox, Dialog, Field, Toast } from 'shineout-mobile';
import PersonalAgreementDrawer, { IAgreementRef } from '../_/components/PersonalAgreementDrawer';
import { CHALLENGE_EXPIRATION_OR_EXCEPTION } from './constant';
import useTimeCountDown from './hooks/useTimeCountDown';
import HeaderBannerImg from './img/header-banner.png';
import styles from './index.less';
import { SendPhoneMessageTypeEnum } from './interfaces';
import { createAuthToken, gtRegister, sendPhoneMessage } from './services';
import { camelToUnderlineForObject, setCookieForBsmsCarTeamToken } from './utils';

const BusManagementLoginPage: React.FC = () => {
  usePageTitle('物流园区车队管理');
  const navigator = useNavigate();

  const { time, isCounting, startCountDown } = useTimeCountDown(60);
  const codeInputRef = useRef<HTMLInputElement>(null);
  const gtRef = useRef<{
    reset: () => void;
    verify: () => void;
    getValidate: () => {
      geetest_challenge: string;
    };
  }>(null);

  const [phone, setPhone] = useState<string>('');
  const [code, setCode] = useState<string>('');
  const [challenge, setChallenge] = useState<string>('');
  const [codeVerityLoading, setCodeVerityLoading] = useState<boolean>(false);
  const [isCheckReadProtocol, setIsCheckReadProtocol] = useState(false);

  const isCanSendCode = useMemo(
    () => phone.length === 11 && !codeVerityLoading,
    [codeVerityLoading, phone.length],
  );
  const isCanNextStep = useMemo(
    () => phone.length === 11 && code.length === 6 && challenge !== '',
    [challenge, code.length, phone.length],
  );

  const personalAgreementRef: MutableRefObject<IAgreementRef | null> = useRef(null);

  // 图形验证回调
  const gtHandle = (gt) => {
    gt.onReady(() => {
      gtRef.current = gt;
      gtRef.current.verify();
    })
      .onSuccess(() => {
        const result = gtRef.current?.getValidate();
        if (!result) {
          Toast.fail('请完成验证');
        }
        setChallenge(result?.geetest_challenge);
        handleSendPhoneMessageCode(result?.geetest_challenge);
      })
      .onError(() => {
        setCodeVerityLoading(false);
      })
      .onClose(() => {
        setCodeVerityLoading(false);
      });
  };

  // 拉起图形验证
  const handleClickSendPhoneCode = () => {
    setCodeVerityLoading(true);
    gtRegister()
      .then((config) => {
        if (config?.enabled === false) {
          setChallenge(config?.challenge);
          handleSendPhoneMessageCode(config?.challenge);
        } else if (config.staticServers === null) {
          return Toast.fail('图形验证初始化失败');
        } else {
          window.initGeetest(
            {
              ...camelToUnderlineForObject(config),
              product: 'bind',
            },
            gtHandle,
          );
        }
      })
      .finally(() => {
        setCodeVerityLoading(false);
      });
  };

  // 发送手机验证码
  const handleSendPhoneMessageCode = usePersistFn((challenge: string) => {
    sendPhoneMessage({
      challenge,
      phone,
      type: SendPhoneMessageTypeEnum.CAR_CREW,
    })
      .then(() => {
        setCodeVerityLoading(false);
        codeInputRef.current?.focus();
        Toast.success('验证码已发送');
        startCountDown();
      })
      .catch((err) => {
        // 重试逻辑
        if (err.code === CHALLENGE_EXPIRATION_OR_EXCEPTION) {
          Toast.fail(err.msg);
          gtRef.current.reset();
          gtRegister().then((config) => {
            window.initGeetest(
              {
                ...camelToUnderlineForObject(config),
                product: 'bind',
              },
              gtHandle,
            );
          });
        } else {
          Toast.fail(err.msg);
        }
      })
      .finally(() => {
        setCodeVerityLoading(false);
      });
  });

  // 提交
  const handleSubmit = usePersistFn((phone, code) => {
    if (isCheckReadProtocol) {
      createAuthToken({
        challenge,
        figureCode: code,
        phone,
        type: SendPhoneMessageTypeEnum.CAR_CREW,
      }).then((res) => {
        Toast.success('登录成功');
        setCookieForBsmsCarTeamToken(res.bsms_car_team_token);
        setCookie(COOKIE_KEY_OBJ.siamToken, (res.siamToken || '').slice(0, 6), 30);
        navigator('/bus-fleet/home');
      });
    } else {
      Dialog.confirm({
        title: '提示',
        message: '请阅读并同意SHEIN《个人信息保护说明》',
        onOk: () => {
          setIsCheckReadProtocol(true);
          setTimeout(() => handleSubmit(phone, code), 0);
        },
        confirmButtonText: '同意',
        cancelButtonText: '拒绝',
      });
    }
  });

  return (
    <div className={styles.container}>
      <img className={styles.headerImg} src={HeaderBannerImg} alt={'头部底图'} />
      <div className={styles.title}>{'物流园区车队管理'}</div>
      <div className={styles.formContainer}>
        <div className={styles.formBox}>
          <Field
            label={'手机号'}
            placeholder={'请输入手机号码'}
            value={phone}
            extra={<></>}
            type="number"
            maxLength={11}
            onChange={(e) => {
              setPhone(e.target.value);
            }}
            leftIcon={
              <span className={styles.icon}>
                <Icon color="#ccc" name="m-phone" fontSize={17} />
              </span>
            }
          />
          <Field
            forwardRef={(ref) => {
              codeInputRef.current = ref;
            }}
            value={code}
            type="number"
            digits={0}
            maxLength={6}
            onChange={(e) => {
              setCode(e.target.value);
            }}
            leftIcon={
              <span className={styles.icon}>
                <Icon name="pinkong" color="#ccc" fontSize={18} />
              </span>
            }
            extra={
              isCounting ? (
                <Button className={styles.timeButton} type="primary" size="small" plain>
                  {time}
                </Button>
              ) : (
                <Button
                  className={styles.codeButton}
                  onClick={handleClickSendPhoneCode}
                  type="primary"
                  size="small"
                  loading={codeVerityLoading}
                  disabled={!isCanSendCode}
                >
                  {'发送验证码'}
                </Button>
              )
            }
            label={'验证码'}
            placeholder={'请输入验证码'}
          />
        </div>
        <div className={styles.checkBox}>
          <Checkbox
            shape="square"
            label={
              <div style={{ fontSize: 12 }}>
                {'我已阅读并同意'}
                <Button
                  text
                  type="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    personalAgreementRef.current?.open();
                  }}
                >
                  {'《个人信息保护说明》'}
                </Button>
              </div>
            }
            value={isCheckReadProtocol}
            onChange={(val) => setIsCheckReadProtocol(val)}
          />
        </div>
        <div className={styles.submitBox}>
          <Button
            className={styles.submitButton}
            type="primary"
            size="large"
            disabled={!isCanNextStep}
            onClick={() => handleSubmit(phone, code)}
          >
            {'登录'}
          </Button>
        </div>
      </div>
      <PersonalAgreementDrawer ref={personalAgreementRef} />
    </div>
  );
};

export default BusManagementLoginPage;
