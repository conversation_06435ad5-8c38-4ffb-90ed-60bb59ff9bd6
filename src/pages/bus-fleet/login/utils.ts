/**
 * @description 对象驼峰转下划线
 * @param {Record<string, unknown>} obj 需要转换的对象
 * @returns {Record<string, any>} 转换后的对象
 */
export function camelToUnderlineForObject(obj: Record<string, any>): Record<string, any> {
  const newObj = new Object();
  for (const key in obj) {
    newObj[key.replace(/([A-Z])/g, '_$1').toLowerCase()] = obj[key];
  }
  return newObj;
}

const BSMS_TOKEN_KEY = 'BSMS_CAR_TEAM_FRONT_TOKEN';

// 过期时间30天
const ExpirationTime = 30 * 24 * 60 * 60 * 1000;

/**
 * @description 设置BSMS车队前端token到cookie
 * @param {string} token - 需要设置的token值
 * @returns {void} 无返回值
 */
export function setCookieForBsmsCarTeamToken(token: string): void {
  const expires = new Date();
  expires.setTime(expires.getTime() + ExpirationTime);
  document.cookie = `${BSMS_TOKEN_KEY}=${token || ''};expires=${expires.toUTCString()};path=/`;
}

/**
 * @description 从cookie中获取BSMS车队前端token
 * @returns {string} 返回获取到的token值，如果不存在则返回空字符串
 */
export function getCookieForBsmsCarTeamToken(): string {
  const name = `${BSMS_TOKEN_KEY}=`;
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    const c = ca[i].trim();
    if (c.indexOf(name) === 0) {
      return c.substring(name.length, c.length);
    }
  }
  return '';
}

/**
 * @description 检查当前是否有有效的BSMS车队前端token登录
 * @returns {boolean} 返回true表示有有效token登录，false表示没有
 */
export function checkIsBsmsCarTeamTokenLogin(): boolean {
  return document.cookie.indexOf(BSMS_TOKEN_KEY) !== -1 && getCookieForBsmsCarTeamToken() !== '';
}

/**
 * @description 移除BSMS车队前端token的cookie
 * @returns {void} 无返回值
 */
export function removeCookieForBsmsCarTeamToken(): void {
  document.cookie = `${BSMS_TOKEN_KEY}=;expires=${new Date(0).toUTCString()};path=/`;
}
