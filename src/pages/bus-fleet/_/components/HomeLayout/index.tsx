import { useNavigate } from 'react-router-dom';
import Icon from '@shein-components/Icon';
import { Tabbar } from 'shineout-mobile';
import styles from './index.less';

type IHomeLayoutProps = {
  menuCurrent?: HomeLayoutTabEnum;
};

export enum HomeLayoutTabEnum {
  OPERATE_MANAGEMENT = 0,
  PERSONAL_CENTRA = 1,
}

const MenuMap = [
  {
    type: HomeLayoutTabEnum.OPERATE_MANAGEMENT,
    name: '运营管理',
    icon: <Icon name="home" />,
    activeIcon: <Icon name="home-fill" />,
    url: '/bus-fleet/home',
  },
  {
    type: HomeLayoutTabEnum.PERSONAL_CENTRA,
    name: '个人中心',
    icon: <Icon name="m-personal-1" />,
    activeIcon: <Icon name="m-personal-selected-fill" />,
    url: '/bus-fleet/personal',
  },
];
const { Item } = Tabbar;

/**
 * @description HomeLayout
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const HomeLayout: React.FC<IHomeLayoutProps> = (props) => {
  const { children, menuCurrent } = props;
  const navigate = useNavigate();

  return (
    <div className={styles.container}>
      <div className={styles.content}>{children}</div>
      <div className={styles.footer}>
        <Tabbar
          active={MenuMap.find((item) => item.type === menuCurrent)?.url}
          onChange={(v) => {
            navigate(v);
          }}
        >
          {MenuMap?.map((item) => {
            return (
              <Item key={item.type} name={item.url} icon={item.icon} activeIcon={item.activeIcon}>
                {item.name}
              </Item>
            );
          })}
        </Tabbar>
      </div>
    </div>
  );
};

export default HomeLayout;
