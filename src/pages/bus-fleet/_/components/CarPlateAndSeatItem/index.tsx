import { ICarDetailListItem } from '@/pages/bus-fleet/schedule-task/interfaces';
import Icon from '@shein-components/Icon';
import classNames from 'classnames';
import styles from './index.less';

interface IProps {
  item: ICarDetailListItem;
  smallSize?: boolean;
  disabled?: boolean;
  closable?: boolean;
  onClose?: () => void;
}

const level = [39, 49, 100];

/**
 * @description CarPlateAndSeatItem
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const CarPlateAndSeatItem: React.FC<IProps> = (props) => {
  const { item, smallSize, disabled = false, closable = false, onClose } = props;
  return (
    <div
      className={classNames(styles.carItem, disabled ? styles.disabled : null)}
      style={
        smallSize
          ? {
              fontSize: 12,
            }
          : null
      }
    >
      <div className={styles.licensePlate}>{item.licensePlate}</div>
      <div
        className={classNames(
          styles.seatNumber,
          item.seatNumber > level[2]
            ? styles.seatNumberDeepGreen
            : item.seatNumber > level[1]
            ? styles.seatNumberGreen
            : item.seatNumber > level[0]
            ? styles.seatNumberYellow
            : styles.seatNumberRed,
        )}
      >
        {item.seatNumber}
        {'座'}
      </div>
      {closable && (
        <Icon
          className={styles.closeIcon}
          name="close - multicolor"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            onClose();
          }}
        />
      )}
    </div>
  );
};

export default CarPlateAndSeatItem;
