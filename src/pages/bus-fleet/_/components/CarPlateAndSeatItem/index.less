.carItem {
  position: relative;
  display: flex;

  .licensePlate {
    padding: 8px;
    color: black !important;
    background-color: #e7ecef;
    border-radius: 4px 0 0 4px;
    border-right: #0d3b66 1px solid !important;
  }

  .seatNumber {
    padding: 8px;
    color: black !important;
    background-color: #078080 !important;
    border-radius: 0 4px 4px 0;
    border-left: #0d3b66 1px solid !important;
  }

  .seatNumberRed {
    background-color: #ffd803 !important;
  }

  .seatNumberYellow {
    background-color: #f25f4c !important;
  }

  .seatNumberGreen {
    background-color: #078080 !important;
  }

  .seatNumberDeepGreen {
    color: white !important;
    background-color: #00473e !important;
  }
}

.disabled {
  text-decoration: line-through;
  text-decoration-color: #0d3b66;
}

.closeIcon {
  position: absolute;
  top: -6px;
  right: -8px;
  font-size: 18px;
}
