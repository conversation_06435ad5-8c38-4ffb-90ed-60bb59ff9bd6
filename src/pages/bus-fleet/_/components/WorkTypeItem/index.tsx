import {
  CommutingTypeEnum,
  CommutingTypeEnumOptions,
} from '@/pages/bus-fleet/schedule-task/interfaces';
import { TaskTypeEnum } from '@/pages/bus-fleet/task-list/interfaces';
import Icon from '@shein-components/Icon';
import classNames from 'classnames';
import styles from './index.less';

interface IProps {
  commutingType: CommutingTypeEnum;
  taskType?: TaskTypeEnum;
}

const WorkTypeItem: React.FC<IProps> = (IProps) => {
  const { commutingType, taskType } = IProps;
  const isDay = [CommutingTypeEnum.DAY_GO_OFF_WORK, CommutingTypeEnum.DAY_GO_TO_WORK].includes(
    commutingType,
  );
  return (
    <div
      className={classNames(styles.container, isDay ? styles.containerDay : styles.containerNight)}
    >
      {isDay ? (
        <Icon name="m-sun-fill" color="#F6BB42" fontSize={16} />
      ) : (
        <Icon name="m-moon-fill" color="#4E4BE5" fontSize={16} />
      )}
      <span className={styles.name}>
        {CommutingTypeEnumOptions.find((item) => item.value === commutingType)?.label}
      </span>
      {taskType === TaskTypeEnum.ADDITIONAL && <span className={styles.suffix}>加</span>}
      {/* {taskType === TaskTypeEnum.REGULAR && <span className={styles.suffix}>常</span>} */}
    </div>
  );
};

export default WorkTypeItem;
