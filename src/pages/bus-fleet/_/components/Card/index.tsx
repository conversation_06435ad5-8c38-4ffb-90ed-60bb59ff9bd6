import { useState } from 'react';
import AutoGapLabel from '@/_/components/AutoGapLabel';
import { DriverWorkEnum } from '@/pages/bus-fleet/driver-manage/interfaces';
import { Button, Icons, Popover, Switch, Tag } from 'shineout-mobile';
import styles from './index.less';
import { IFooterTypes, IProps, ItemTypes } from './interfaces';

const Item: React.FC<ItemTypes> = (props) => {
  const { label, value } = props;
  return (
    <div className={styles.item}>
      {/* <div className={styles.label}>{label}</div> */}
      <AutoGapLabel className={styles.label} label={label} />
      <div className={styles.value}>{value}</div>
    </div>
  );
};

/**
 * @description Footer
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Footer: React.FC<IFooterTypes> = (props) => {
  const { status, onToggle, statusText, opt, id } = props;
  return (
    <div className={styles.footer}>
      <div className={styles.more}>
        {'更多'}
        <Popover.PopAction
          position="top-left"
          actionItemStyle={{
            textAlign: 'left',
          }}
          renderItem={(item) => (
            <Button
              text
              type={item.type}
              onClick={() => {
                item.callback(id);
              }}
            >
              <Icons style={{ fontSize: 16, marginRight: 4 }} name={item.icon} />
              <span>{item.title}</span>
            </Button>
          )}
          data={opt}
        />
      </div>
      <div className={styles.toggleStatus}>
        <span className={styles.text}>
          {'切换为'}
          {status === DriverWorkEnum.REST ? statusText.enabled : statusText.disabled}
        </span>
        <Switch
          onChange={onToggle}
          content={[statusText.enabled, statusText.enabled]}
          value={status === 1}
          size={20}
        />
      </div>
    </div>
  );
};

/**
 * @description CarItem
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const CarItem: React.FC<IProps> = (props) => {
  const { onToggle, data, status, statusText, title, subTitle, id, opt } = props;
  const [toggle, setToggle] = useState(false);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.carPlate}>
          {title}
          {status === DriverWorkEnum.REST ? (
            <Tag className={styles.tag} color="#FAE9E9" textColor="#F96156">
              {statusText.disabled}
            </Tag>
          ) : (
            <Tag className={styles.tag} color="#E3EDFA" textColor="#197AFA">
              {statusText.enabled}
            </Tag>
          )}
        </div>
        <div className={styles.toggle}>
          {toggle ? (
            <Button type="primary" text onClick={() => setToggle(!toggle)}>
              {'收起'}
            </Button>
          ) : (
            <Button type="primary" text onClick={() => setToggle(!toggle)}>
              {'展开'}
            </Button>
          )}
        </div>
      </div>
      <div className={styles.seatNum}>{subTitle}</div>
      {toggle &&
        data.map((item) => <Item key={item.label} label={item.label} value={item.value} />)}
      <Footer id={id} opt={opt} status={status} onToggle={onToggle} statusText={statusText} />
    </div>
  );
};

export default CarItem;
