.container {
  display: flex;
  padding: 12px;
  margin-bottom: 12px;
  background-color: white;
  border-radius: 4px;
  flex-direction: column;
  gap: 6px;

  .header {
    display: flex;
    flex-grow: 1;
    flex-direction: row;

    .carPlate {
      display: flex;
      font-size: 16px;
      font-weight: 500;
      flex-grow: 1;
      align-items: center;

      .tag {
        margin-left: 10px;
      }
    }

    .toggle {
      display: flex;
      // width: 40px;
      align-items: center;
    }
  }

  .seatNum {
    display: flex;
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 500;
  }
}

.item {
  display: flex;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #666c7c;
  flex-direction: row;
  align-items: center;

  .label {
    width: 65px;
    min-width: 65px;

    // &::after {
    //   content: ':';
    // }
  }

  .value {
    flex-grow: 1;
    color: #141737;
  }
}

.footer {
  border-top: 1px solid #e8ebf0;
  display: flex;
  padding: 12px 0 0;

  .more {
    display: flex;
    // width: 40px;
    justify-content: center;
    align-items: center;
    color: #666c7c;
  }

  .toggleStatus {
    flex-grow: 1;
    color: #666c7c;
    text-align: right;

    .text {
      &::after {
        margin-right: 10px;
        content: ':';
      }
    }
  }
}
