import { ReactNode } from 'react';
import { DriverWorkEnum } from '@/pages/bus-fleet/driver-manage/interfaces';

export interface IProps {
  id: number;
  status: DriverWorkEnum;
  title: string;
  subTitle: string;
  onToggle: (status: boolean) => void;
  data: {
    label: string;
    value: string | ReactNode;
  }[];
  statusText: IStatusType;
  opt: IOptType[];
}

export type IOptType = {
  title: string;
  callback: (id?: number) => void;
  type: 'primary' | 'default' | 'danger';
  icon: string;
};

export type IStatusType = {
  disabled: string;
  enabled: string;
};

export type ItemTypes = {
  label: string;
  value: string | ReactNode;
};

export type IFooterTypes = {
  id: number;
  status: DriverWorkEnum;
  onToggle: (status: boolean) => void;
  statusText: IStatusType;
  opt: IOptType[];
};
