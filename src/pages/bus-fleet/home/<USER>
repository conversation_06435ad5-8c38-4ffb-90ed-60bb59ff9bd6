import { useNavigate } from 'react-router-dom';
import Icon from '@/_/components/Icon';
import { usePageTitle } from '@/_/hooks';
import { Toast } from 'shineout-mobile';
import HomeLayout, { HomeLayoutTabEnum } from '../_/components/HomeLayout';
import ArrowIcon from './img/arrow.png';
import BusIcon from './img/bus.png';
import DriverIcon from './img/driver.png';
import BgImg from './img/home-wave.png';
import PointIcon from './img/point.png';
import styles from './index.less';

const ActionMap = [
  {
    img: 'order management',
    name: '排班任务管理',
    icon: 'triangle-right',
    url: '/bus-fleet/schedule-task',
  },
  {
    img: 'order management',
    name: '任务列表',
    icon: 'triangle-right',
    url: '/bus-fleet/task-list',
  },
  {
    img: 'm-time-fill',
    name: '临时用车',
    icon: 'triangle-right',
    url: '/bus-fleet/temporary-vehicle',
  },
];

/**
 * @description BusManagementHomePage
 * @returns {unknown} desc
 */
const BusManagementHomePage = () => {
  usePageTitle('运营管理');
  const navigate = useNavigate();

  /**
   * @description navigateToVehicle
   * @returns {unknown} desc
   */
  const navigateToVehicle = () => {
    navigate('/bus-fleet/vehicle-manage');
  };

  /**
   * @description navigateToDriver
   * @returns {unknown} desc
   */
  const navigateToDriver = () => {
    navigate('/bus-fleet/driver-manage');
  };

  return (
    <HomeLayout menuCurrent={HomeLayoutTabEnum.OPERATE_MANAGEMENT}>
      <div className={styles.main}>
        <img className={styles.headerBg} src={BgImg} alt={'背景图片'} />
        <div className={styles.container}>
          <div className={styles.title}>
            <div>
              <Icon name="shein-logo" />
            </div>
            <div>{'物流园区车队管理'}</div>
          </div>
          <div className={styles.btnGroup}>
            <div className={styles.inPark} onClick={() => navigateToVehicle()}>
              <div className={styles.cnWord}>{'车辆管理'}</div>
              <div className={styles.enWord}>Vehicle Manage</div>
              <div>
                <img className={styles.icon} src={ArrowIcon} alt={'箭头图标'} />
                <img className={styles.mainIcon} src={BusIcon} alt={'时间图标'} />
              </div>
            </div>

            <div className={styles.passCard} onClick={() => navigateToDriver()}>
              <div className={styles.cnWord}>{'司机管理'}</div>
              <div className={styles.enWord}>Driver Manage</div>
              <div>
                <img className={styles.icon} src={PointIcon} alt={'点点图标'} />
                <img className={styles.mainIcon} src={DriverIcon} alt={'盖章图标'} />
              </div>
            </div>
          </div>

          {ActionMap.map((item) => (
            <div
              key={item.name}
              className={styles.actionBtn}
              onClick={() => {
                if (item.img === 'm-time-fill') {
                  Toast.info('功能暂未开放');
                  return;
                }
                navigate(item.url);
              }}
            >
              <div className={styles.icon}>
                <Icon
                  name={item.img}
                  color="white"
                  style={{
                    opacity: 0.8,
                  }}
                  fontSize={18}
                />
              </div>
              <span className={styles.title}>{item.name}</span>
              <Icon name={item.icon} className={styles.right} fontSize={16} />
            </div>
          ))}
        </div>
      </div>
    </HomeLayout>
  );
};
export default BusManagementHomePage;
