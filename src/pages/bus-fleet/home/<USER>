/* stylelint-disable max-nesting-depth */
.main {
  position: relative;
  height: calc(100vh - 70px);
  max-width: 750px;
  background-color: white;

  .headerBg {
    position: absolute;
    z-index: 0;
    width: 100%;
  }

  .container {
    position: relative;
    z-index: 1;
    width: 100%;
    height: fit-content;

    .title {
      position: relative;
      top: 2rem;
      left: 3rem;

      div {
        position: relative;
        top: 1.5rem;
        z-index: 1;
        font-size: 2.2rem;
        font-weight: 500;
        color: white;
      }
    }

    .btnGroup {
      position: relative;
      top: 10rem;
      z-index: 1;
      display: flex;
      text-align: center;
      flex-direction: row;
      justify-content: center;

      .inPark {
        width: 41%;
        margin-right: 3%;
        text-align: left;
        cursor: pointer;
        /* stylelint-disable-next-line prettier/prettier */
        background: linear-gradient(
          225.81deg,
          rgb(249 255 255 / 30%) 15.23%,
          rgb(19 209 208 / 30%) 86.33%
        );
        background-color: rgb(255 255 255 / 100%);
        border-radius: 1rem;
        box-sizing: border-box;
        aspect-ratio: 1/1;
        // box-shadow: rgb(235 64 20 / 10%) 1rem 1rem 2rem 0;
        box-shadow: 0 2px 16px 0 rgb(19 21 51 / 10%);

        .cnWord {
          position: relative;
          top: 2.5rem;
          left: 1.5rem;
          display: inline-block;
          overflow: hidden;
          font-size: 1.8rem;
          font-weight: 500;
          color: rgb(12 165 170 / 100%);
        }

        .enWord {
          position: relative;
          top: 3rem;
          left: 1.5rem;
          font-size: 1.2rem;
          letter-spacing: 2px;
          color: rgb(0 143 142 / 60%);
        }

        .icon {
          position: relative;
          top: 9.5rem;
          left: 1.5rem;
          z-index: 1;
          float: left;
          width: 3rem;
        }

        .mainIcon {
          position: relative;
          top: 5rem;
          left: -2rem;
          z-index: 1;
          float: right;
          width: 5.5rem;
        }
      }

      .passCard {
        width: 41%;
        margin-left: 3%;
        text-align: left;
        cursor: pointer;
        /* stylelint-disable-next-line prettier/prettier */
        background: linear-gradient(
          225.81deg,
          rgb(255 217 214 / 20%) 15.23%,
          rgb(250 119 107 / 20%) 86.33%
        );
        background-color: white;
        border-radius: 1rem;
        box-sizing: border-box;
        aspect-ratio: 1/1;
        // box-shadow: rgb(235 64 20 / 10%) 1rem 1rem 2rem 0;
        box-shadow: 0 2px 16px 0 rgb(19 21 51 / 10%);

        .cnWord {
          position: relative;
          top: 2.5rem;
          left: 1.5rem;
          display: inline-block;
          overflow: hidden;
          font-size: 1.8rem;
          font-weight: 500;
          color: rgb(208 84 73 / 100%);
        }

        .enWord {
          position: relative;
          top: 3rem;
          left: 1.5rem;
          font-size: 1.2rem;
          letter-spacing: 2px;
          color: rgb(208 84 73 / 60%);
        }

        .icon {
          position: relative;
          top: 9.5rem;
          left: 1.5rem;
          z-index: 1;
          float: left;
          width: 3rem;
        }

        .mainIcon {
          position: relative;
          top: 5rem;
          left: -2rem;
          z-index: 1;
          float: right;
          width: 5rem;
        }
      }
    }

    .actionBtn {
      position: relative;
      top: 12.5rem;
      left: 6%;
      z-index: 1;
      display: flex;
      width: 88%;
      padding: 10px;
      margin-bottom: 10px;
      cursor: pointer;
      background-color: white;
      border: 1px solid #f5f6fb;
      border-radius: 1rem;
      box-shadow: rgb(20 23 55/3%) 1rem 1rem 2rem 0;
      flex-direction: row;
      align-items: center;

      .icon {
        position: relative;
        left: 1rem;
        display: block;
        display: flex;
        width: 3rem;
        height: 3rem;
        font-size: 2.4rem;

        /* stylelint-disable-next-line prettier/prettier */
        background: linear-gradient(213.99deg, #5bd6e1 11.62%, #82c3f7 54.07%, #5fd7e1 92.36%);
        border-radius: 4px;
        /* stylelint-disable-next-line prettier/prettier */
        opacity: .8;
        align-items: center;
        justify-content: center;
      }

      .title {
        position: relative;
        top: 0;
        left: 2rem;
        display: block;
        font-size: 1.4rem;
        color: #333e59;
        flex-grow: 1;
      }

      .right {
        position: relative;
        display: block;
        font-size: 3rem;
        /* stylelint-disable-next-line prettier/prettier */
        color: #999da8;
      }
    }

    .cookbook {
      position: relative;
      top: 17rem;
      left: 6%;
      width: 88%;
      padding: 10px;
      margin-bottom: 10rem;
      background-color: #f4f5f8;
      border-radius: 2rem;

      .section {
        position: relative;
        left: 1rem;
        width: 100%;
        padding-right: 10px;
        padding-bottom: 1rem;
      }

      .section ul {
        margin-left: 1rem;
        list-style-type: disc;
        list-style-position: outside;
      }

      .section li {
        padding: 5px 0;
        font-size: 12px;
        color: #666c7c;
      }

      .section li::marker {
        font-size: 16px;
        /* stylelint-disable-next-line prettier/prettier */
        color: #d5e1fc;
      }

      .section .title {
        position: relative;
        top: 0;
        left: 0;
        display: block;
        margin-top: 10px;
        margin-bottom: 10px;
        margin-left: 1.2rem;
        font-size: 1.4rem;
        font-weight: 500;
        color: #666c7c;
      }
    }

    .logout {
      position: relative;
      top: 10rem;
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: center;
    }
  }
}
