import { useState } from 'react';
import Icon from '@shein-components/Icon';
import moment from 'moment';
import { Calendar, Cell } from 'shineout-mobile';

type IDateFilterHeaderProps = {
  date?: string;
  handleChange: (date: string) => void;
};

/**
 * @description DateFilterHeader
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const DateFilterHeader: React.FC<IDateFilterHeaderProps> = ({ date, handleChange }) => {
  const [dateVisible, setDateVisible] = useState(false);

  return (
    <>
      <Cell
        label={'发车日期'}
        leftIcon={
          <div style={{ display: 'flex', alignItems: 'center', marginRight: 5 }}>
            <Icon name="calendar" color="#197AFA" fontSize={16} />
          </div>
        }
        value={date}
        isLink
        onClick={() => {
          setDateVisible(true);
        }}
      />
      <Calendar
        round
        visible={dateVisible}
        drawer={{ position: 'bottom', height: 486, closeable: true }}
        value={moment(date).toDate()}
        maxDate={moment().add(1, 'day').toDate()}
        minDate={moment().subtract(1, 'year').toDate()}
        defaultPosition={moment().toDate()}
        onCancel={() => setDateVisible(false)}
        onChange={(val) => {
          handleChange(moment(val).format('YYYY-MM-DD'));
          setDateVisible(false);
        }}
      />
    </>
  );
};

export default DateFilterHeader;
