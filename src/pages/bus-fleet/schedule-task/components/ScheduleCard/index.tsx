import React, { ReactNode, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AutoGapLabel from '@/_/components/AutoGapLabel';
import { CommonStatusEnum } from '@/pages/bus-employee/appointment/interfaces';
import CarPlateAndSeatItem from '@/pages/bus-fleet/_/components/CarPlateAndSeatItem';
import WorkTypeItem from '@/pages/bus-fleet/_/components/WorkTypeItem';
import { EMPTY_STR } from '@/share';
import classNames from 'classnames';
import isNil from 'lodash/isNil';
import { Button, Toast } from 'shineout-mobile';
import { AdjustFlagEnum, IWorkingScheduleListItem } from '../../interfaces';
import { rearrangement } from '../../services';
import styles from './index.less';

interface IProps {
  item: IWorkingScheduleListItem;
  afterSubmit: () => void;
  isAdmin?: boolean;
}

/**
 * @description Item
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const Item: React.FC<{
  label: string;
  value: string | ReactNode;
  style?: React.CSSProperties;
}> = ({ label, value, style: customStyles }) => {
  return (
    <div className={classNames(styles.item, customStyles)}>
      <AutoGapLabel className={styles.label} label={label} />
      <div className={styles.value}>{value}</div>
    </div>
  );
};

/**
 * @description ScheduleCard
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const ScheduleCard: React.FC<IProps> = (props) => {
  const { item, afterSubmit, isAdmin } = props;
  const [toggle, setToggle] = useState(false);
  const navigate = useNavigate();

  /**
   * @description handleReflow
   * @returns {unknown} desc
   */
  const handleReflow = () => {
    rearrangement(item.id, isAdmin).then(() => {
      Toast.success('操作完成');
      afterSubmit();
    });
  };

  /**
   * @description handleAdjust
   * @returns {unknown} desc
   */
  const handleAdjust = () => {
    let target = `/bus-fleet/schedule-task/adjust-task/${item.id}`;
    if (isAdmin) {
      target = `/bus-operate/schedule-task/adjust-task/${item.id}`;
    }
    navigate(target);
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.title}>
          <span className={styles.text}>{item.departureSiteName}</span>
          <span className={styles.shortArrow} />
          <span className={styles.text}>{item.destinationSiteName}</span>
        </div>
        <div className={styles.toggle}>
          {toggle ? (
            <Button text type="primary" onClick={() => setToggle(!toggle)}>
              {'收起'}
            </Button>
          ) : (
            <Button text type="primary" onClick={() => setToggle(!toggle)}>
              {'展开'}
            </Button>
          )}
        </div>
      </div>
      <div className={styles.content}>
        <Item
          style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', paddingTop: 5 }}
          label={'发车时间'}
          value={
            <div className={styles.scheduleTime}>
              <span className="font-bold">{item.scheduleTime}</span>
              <WorkTypeItem commutingType={item.commutingType} />
            </div>
          }
        />
        {toggle && <Item label={'预计抵达时间'} value={item.estimatedArrivalTime} />}
        {toggle && <Item label={'预约人数'} value={item.appointmentNum} />}
        {toggle && isAdmin && (
          <Item
            label="上车人数"
            value={isNil(item.passengersNum) ? EMPTY_STR : item.passengersNum}
          />
        )}
        {toggle && (
          <Item
            label={'分配车辆'}
            value={
              <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                {item.carDetailList.map((item) => {
                  return <CarPlateAndSeatItem key={item.licensePlate} item={item} />;
                })}
              </div>
            }
          />
        )}
      </div>
      {(item.rearrangementFlag === CommonStatusEnum.YES ||
        item.adjustFlag === AdjustFlagEnum.YES) && (
        <footer className={styles.footer}>
          {item.rearrangementFlag === CommonStatusEnum.YES && (
            <Button type="default" size="mini" onClick={handleReflow}>
              {'班次重排'}
            </Button>
          )}
          {item.adjustFlag === AdjustFlagEnum.YES && (
            <Button type="default" size="mini" onClick={handleAdjust}>
              {'任务调整'}
            </Button>
          )}
        </footer>
      )}
    </div>
  );
};

export default ScheduleCard;
