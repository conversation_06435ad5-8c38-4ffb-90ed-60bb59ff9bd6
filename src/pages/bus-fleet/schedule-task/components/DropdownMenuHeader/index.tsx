import { useMemo } from 'react';
import isNil from 'lodash/isNil';
import { DropdownMenu } from 'shineout-mobile';
import { CommutingTypeEnumOptions, IRouteLintList } from '../../interfaces';

type IDropDownMenuHeaderProps = {
  areaLineList: IRouteLintList[];
  areaValue: number;
  handleChangeArea: (val: number) => void;
  lineValue: number;
  handleChangeLine: (val: number) => void;
  commutingType: number;
  handleChangeCommutingType: (val: number) => void;
  isAdmin?: boolean;
};

/**
 * @description DropdownMenuHeader
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const DropdownMenuHeader: React.FC<IDropDownMenuHeaderProps> = (props) => {
  const {
    areaLineList,
    areaValue,
    lineValue,
    handleChangeArea,
    handleChangeLine,
    commutingType,
    handleChangeCommutingType,
    isAdmin,
  } = props;

  const renderTitle = useMemo(() => {
    const area = areaLineList.find((item) => item.id === areaValue);
    if (area) {
      if (!isAdmin) {
        return area.name;
      }
      return area.name + (area.status === 0 ? '（禁用）' : '');
    }
    return '';
  }, [areaLineList, areaValue, isAdmin]);

  const renderRouteTitle = useMemo(() => {
    const route = [
      ...areaLineList.reduce((acc, cur) => {
        if (cur.id === areaValue) {
          acc.push(...cur.children);
        }
        return acc;
      }, []),
    ].find((item) => item.id === lineValue);
    if (route) {
      if (!isAdmin) {
        return route.name;
      }
      return route.name + (route.status === 0 ? '（禁用）' : '');
    }
    return '';
  }, [areaLineList, areaValue, isAdmin, lineValue]);

  return (
    <DropdownMenu style={{ position: 'relative', zIndex: 1 }}>
      <DropdownMenu.Item
        format={'value'}
        renderItem={'label'}
        title={areaValue === undefined ? '全部区域' : renderTitle}
        data={[
          {
            label: '全部区域',
            value: undefined,
          },
          ...areaLineList.map((item) => ({
            label: item.name + (item.status === 0 ? '（禁用）' : ''),
            value: item.id.toString(),
          })),
        ]}
        value={areaValue ? areaValue.toString() : undefined}
        onChange={(val) => {
          handleChangeArea(val ? Number(val) : undefined);
        }}
      />
      <DropdownMenu.Item
        title={lineValue === undefined ? '全部线路' : renderRouteTitle}
        data={
          areaValue === undefined
            ? [
                {
                  label: '全部线路',
                  value: undefined,
                },
                ...areaLineList
                  .reduce((acc, cur) => {
                    acc.push(...cur.children);
                    return acc;
                  }, [])
                  .map((item) => ({
                    label: item.name + (item.status === 0 ? '（禁用）' : ''),
                    value: item.id.toString(),
                  })),
              ]
            : [
                {
                  label: '全部线路',
                  value: undefined,
                },
                ...(areaLineList.find((item) => item.id === areaValue)?.children || []).map(
                  (item) => ({
                    label: item.name + (item.status === 0 ? '（禁用）' : ''),
                    value: item.id.toString(),
                  }),
                ),
              ]
        }
        format={'value'}
        renderItem={'label'}
        value={lineValue ? lineValue.toString() : undefined}
        onChange={(val) => {
          handleChangeLine(val ? Number(val) : undefined);
        }}
      />
      <DropdownMenu.Item
        title={
          commutingType === undefined
            ? '全部类型'
            : CommutingTypeEnumOptions.find((item) => item.value === commutingType)?.label
        }
        format={'value'}
        renderItem={'label'}
        value={!isNil(commutingType) ? commutingType.toString() : undefined}
        data={[
          {
            label: '全部类型',
            value: undefined,
          },
          ...CommutingTypeEnumOptions.map((item) => {
            return {
              label: item.label,
              value: item.value.toString(),
            };
          }),
        ]}
        onChange={(val) => {
          handleChangeCommutingType(val ? Number(val) : undefined);
        }}
      />
    </DropdownMenu>
  );
};

export default DropdownMenuHeader;
