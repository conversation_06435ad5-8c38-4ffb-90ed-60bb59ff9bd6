import { get, post } from '@/utils';
import {
  ICarDetailListItem,
  IRouteLintList,
  IScheduleDetail,
  IWorkingScheduleListItem,
  IWorkingScheduleListParams,
} from './interfaces';

const adminPrefix = '/bsms/manage';
const fleetPrefix = '/bsms/carTeam';

/**
 * 排班任务管理-获取区域线路下拉选项（获取该车队绑定区域和线路，包含禁用）
 * https://soapi.sheincorp.cn/application/3694/routes/184314/doc
 * @returns
 */
export const getAreaLine = (isAdmin?: boolean) => {
  return get<IRouteLintList[]>(
    (isAdmin ? adminPrefix : fleetPrefix) + '/workingSchedule/selectAreaLine',
  );
};

/**
 * 排班任务管理-获取排班列表
 * https://soapi.sheincorp.cn/application/3694/routes/184341/doc
 */
export const getWorkingScheduleList = (params: IWorkingScheduleListParams, isAdmin?: boolean) => {
  return post<{
    total: number;
    rows: IWorkingScheduleListItem[];
  }>((isAdmin ? adminPrefix : fleetPrefix) + '/workingSchedule/selectWorkingSchedulePage', params);
};

/**
 * 排班任务管理-获取可替换车辆
 * https://soapi.sheincorp.cn/application/3694/routes/184539/doc
 */
export const getReplaceableCar = (id: number, isAdmin?: boolean) => {
  return get<ICarDetailListItem[]>(
    (isAdmin ? adminPrefix : fleetPrefix) + '/workingSchedule/selectReplaceableCar',
    {
      id,
    },
  );
};

/**
 * 排班任务管理-任务调整
 * https://soapi.sheincorp.cn/application/3694/routes/184560/doc
 */
export const taskAdjust = (
  params: { id: number; licensePlateList: string[] },
  isAdmin?: boolean,
) => {
  return post<boolean>(
    (isAdmin ? adminPrefix : fleetPrefix) + '/workingSchedule/taskAdjust',
    params,
  );
};

/**
 * 排班任务管理-班次详情
 * https://soapi.sheincorp.cn/application/3694/routes/184563/doc
 */
export const getScheduleDetail = (id: number, isAdmin?: boolean) => {
  return get<IScheduleDetail>(
    (isAdmin ? adminPrefix : fleetPrefix) + '/workingSchedule/selectScheduleDetail',
    {
      id,
    },
  );
};

/**
 * 排班任务管理-班次重排
 * https://soapi.sheincorp.cn/application/3694/routes/189965/doc
 */
export const rearrangement = (id: number, isAdmin?: boolean) => {
  return get<boolean>((isAdmin ? adminPrefix : fleetPrefix) + '/workingSchedule/rearrangement', {
    id,
  });
};
