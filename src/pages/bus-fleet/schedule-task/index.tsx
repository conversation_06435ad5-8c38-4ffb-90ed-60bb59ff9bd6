import React, { useRef, useState } from 'react';
import { Page } from '@/_/components';
import { usePageTitle } from '@/_/hooks';
import EmptyBus from '@/pages/bus-employee/components/_/EmptyBus';
import LoadingBus from '@/pages/bus-employee/components/_/LoadingBus';
import { t } from '@shein-bbl/react';
import { useInfiniteScroll, useMount, useUpdateEffect } from 'ahooks';
import moment from 'moment';
import store from 'store2';
import DateFilterHeader from './components/DateFilterHeader';
import DropdownMenuHeader from './components/DropdownMenuHeader';
import ScheduleCard from './components/ScheduleCard';
import { IRouteLintList, IWorkingScheduleListItem, IWorkingScheduleListParams } from './interfaces';
import { getAreaLine, getWorkingScheduleList } from './services';

interface IScheduleTaskPageProps {
  /** 是否来自园区通后台的引用 */
  isAdmin?: boolean;
}

const AREA_KEY = '__bus_admin_areaId';
const LINE_KEY = '__bus_admin_lineId';
const PAGE_SIZE = 10;
/**
 * @description ScheduleTaskPage
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const ScheduleTaskPage: React.FC<IScheduleTaskPageProps> = (props) => {
  const { isAdmin } = props;
  usePageTitle(isAdmin ? '排班管理' : '排班任务管理');

  const [searchParams, setSearchParams] = useState<IWorkingScheduleListParams>({
    scheduleDate: moment().format('YYYY-MM-DD'),
    areaId: isAdmin ? store.local.get(AREA_KEY) || undefined : undefined,
    lineId: isAdmin ? store.local.get(LINE_KEY) || undefined : undefined,
  });
  const contentRef = useRef<HTMLDivElement>(null);

  const { data, loading, loadingMore, noMore, reload } = useInfiniteScroll<{
    list: IWorkingScheduleListItem[];
    total: number;
  }>(
    (d) => {
      const page = d ? Math.ceil(d.list.length / PAGE_SIZE) + 1 : 1;
      return getWorkingScheduleList(
        {
          pageNumber: page,
          pageSize: PAGE_SIZE,
          ...searchParams,
        },
        isAdmin,
      ).then((res) => {
        return {
          list: res.rows,
          total: res.total,
        };
      });
    },
    {
      target: contentRef,
      isNoMore: (d) => d && d?.list?.length === d?.total,
    },
  );
  const [routeLineList, setRouteLineList] = useState<IRouteLintList[]>([]);

  useUpdateEffect(() => {
    reload();
  }, [searchParams]);

  useMount(() => {
    getAreaLine(isAdmin).then((res) => {
      setRouteLineList(res);
    });
  });

  return (
    <Page contentClassName="p-0 flex flex-col">
      <DateFilterHeader
        date={searchParams?.scheduleDate}
        handleChange={(v) => {
          setSearchParams({
            ...searchParams,
            scheduleDate: v,
          });
        }}
      />
      <DropdownMenuHeader
        areaLineList={routeLineList}
        lineValue={searchParams?.lineId}
        areaValue={searchParams?.areaId}
        commutingType={searchParams?.commutingType}
        handleChangeArea={(v) => {
          setSearchParams({
            ...searchParams,
            areaId: v,
            lineId: undefined,
          });
          if (isAdmin) {
            store.local.set(AREA_KEY, v || '');
            store.local.set(LINE_KEY, '');
          }
        }}
        handleChangeLine={(v) => {
          if (searchParams?.areaId === undefined) {
            const aid = routeLineList.find((item) => item.children.some((i) => i.id === v))?.id;
            setSearchParams({
              ...searchParams,
              areaId: aid,
              lineId: v,
            });
            if (isAdmin) {
              store.local.set(AREA_KEY, aid || '');
              store.local.set(LINE_KEY, v || '');
            }
          } else {
            setSearchParams({
              ...searchParams,
              lineId: v,
            });
            if (isAdmin) {
              store.local.set(LINE_KEY, v || '');
            }
          }
        }}
        handleChangeCommutingType={(v) => {
          setSearchParams({
            ...searchParams,
            commutingType: v,
          });
        }}
        isAdmin
      />

      <div className="flex-1 min-h-0 overflow-auto p-[12px]" ref={contentRef}>
        {!loading &&
          data.list?.map((item) => (
            <ScheduleCard
              key={item.id}
              item={item}
              afterSubmit={() => {
                reload();
              }}
              isAdmin={isAdmin}
            />
          ))}
        {loadingMore && (
          <div className="text-center text-[#666c7c] text-[12px] mt-md">{t('加载中...')}</div>
        )}
        {noMore && data?.list?.length >= PAGE_SIZE && (
          <div className="text-center text-[#666c7c] text-[12px] mt-md">{t('没有更多了')}</div>
        )}
        {!loading && data.list?.length === 0 && <EmptyBus height="100%" />}
        {loading && <LoadingBus />}
      </div>
    </Page>
  );
};
export default ScheduleTaskPage;
