import React, { useCallback, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import Icon from '@shein-components/Icon';
import { useMount } from 'ahooks';
import classNames from 'classnames';
import { <PERSON><PERSON>, Drawer, NPicker, Toast } from 'shineout-mobile';
import CarPlateAndSeatItem from '../../_/components/CarPlateAndSeatItem';
import { CarTeamFlagEnum, ICarDetailListItem } from '../interfaces';
import { getReplaceableCar, getScheduleDetail, taskAdjust } from '../services';
import styles from './index.less';

const OverRateThreshold = 0.1;

type ItemProps = {
  item: ICarDetailListItem;
  changedValue?: ICarDetailListItem;
  disabled: boolean;
  onClick: (licensePlate: string) => void;
  onClose?: () => void;
};

/**
 * @description Item
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Item: React.FC<ItemProps> = (props) => {
  const { item, disabled, changedValue, onClick, onClose } = props;
  return (
    <>
      <div className={styles.carItem}>
        <div className={styles.originValue}>
          <CarPlateAndSeatItem item={item} />
        </div>
        <div className={styles.changeValue}>
          {disabled ? (
            <div className={styles.placeholder} style={{ color: '#aaa' }}>
              {'非本车队，禁止修改'}
            </div>
          ) : (
            <div>
              <div className={styles.placeholder} onClick={() => onClick(item.licensePlate)}>
                {changedValue ? (
                  <CarPlateAndSeatItem item={changedValue} closable={true} onClose={onClose} />
                ) : (
                  '请选择替换车辆'
                )}
                <Icon name="m-arrow-right" />
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

/**
 * @description AdjustTaskPage
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const AdjustTaskPage: React.FC<{ isAdmin?: boolean }> = ({ isAdmin }) => {
  usePageTitle('任务调整');
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [detail, setDetail] = useState<ICarDetailListItem[]>([]);
  const [editDetail, setEditDetail] = useState<ICarDetailListItem[]>([]);
  const [replaceableCar, setReplaceableCar] = useState<ICarDetailListItem[]>([]);
  const [currentCardLicensePlate, setCurrentCardLicensePlate] = useState<string>(undefined);
  const [editVisible, setEditVisible] = useState(false);
  const [appointmentNum, setAppointmentNum] = useState(0);
  const isNotChangedValue = useMemo(() => {
    return (
      JSON.stringify(
        editDetail.filter((item) => item.licensePlate !== null).map((item) => item.licensePlate),
      ) ===
      JSON.stringify(
        detail
          .filter((item) => item.carTeamFlag === CarTeamFlagEnum.YES)
          .map((item) => item.licensePlate),
      )
    );
  }, [detail, editDetail]);

  const sumOfEditDetail = useMemo(() => {
    return (
      editDetail.reduce((prev, curr) => {
        return prev + curr.seatNumber;
      }, 0) +
      detail
        .filter((item) => item.carTeamFlag === CarTeamFlagEnum.NO)
        .reduce((prev, curr) => {
          return prev + curr.seatNumber;
        }, 0)
    );
  }, [detail, editDetail]);

  /**
   * @description getDetail
   * @returns {unknown} desc
   */
  const getDetail = () => {
    getScheduleDetail(Number(id), isAdmin).then((res) => {
      setDetail(res.scheduleVehicleVoList);
      setAppointmentNum(res.appointmentNum);
      setEditDetail(
        res?.scheduleVehicleVoList.filter((item) => item.carTeamFlag === CarTeamFlagEnum.YES),
      );
    });
  };

  /**
   * @description getReplaceableCarList
   * @returns {unknown} desc
   */
  const getReplaceableCarList = () => {
    getReplaceableCar(Number(id), isAdmin).then((res) => {
      setReplaceableCar(res);
    });
  };

  const handleAdjustTask = useCallback(() => {
    const arr = [];
    detail.forEach((_, index) => {
      if (editDetail?.[index] && editDetail?.[index]?.licensePlate !== null) {
        arr.push(editDetail?.[index]?.licensePlate);
      }
    });
    taskAdjust(
      {
        id: Number(id),
        licensePlateList: arr,
      },
      isAdmin,
    ).then(() => {
      Toast.success('调整成功');
      let target = '/bus-fleet/schedule-task';
      if (isAdmin) {
        target = '/bus-operate/schedule-task';
      }
      navigate(target, {
        replace: true,
      });
    });
  }, [detail, editDetail, id, isAdmin, navigate]);

  useMount(() => {
    getDetail();
    getReplaceableCarList();
  });

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.item}>
          <div className={styles.title}>{'现有车辆'}</div>
          <div className={styles.row}>
            <span className={styles.label}>{'预约人数'}</span>
            <span className={styles.value}>{appointmentNum}</span>
          </div>
        </div>
        <div className={styles.item}>
          <div className={styles.title}>{'替换车辆'}</div>
          <div className={styles.row}>
            <span className={styles.label}>{'总座位数'}</span>
            <span
              className={classNames(
                styles.value,
                sumOfEditDetail >= appointmentNum &&
                  sumOfEditDetail <= appointmentNum * (1 + OverRateThreshold)
                  ? styles.valueGreen
                  : sumOfEditDetail < appointmentNum
                  ? styles.valueRed
                  : styles.valueYellow,
              )}
            >
              {sumOfEditDetail}
            </span>
          </div>
        </div>
      </div>

      <div>
        {detail.map((item, index) => {
          return (
            <Item
              key={item.licensePlate}
              disabled={item.carTeamFlag === CarTeamFlagEnum.NO}
              item={item}
              onClick={(licensePlate) => {
                setCurrentCardLicensePlate(licensePlate);
                setEditVisible(true);
              }}
              changedValue={
                item?.licensePlate !== editDetail?.[index]?.licensePlate
                  ? editDetail?.[index]
                  : undefined
              }
              onClose={() => {
                setEditDetail(
                  editDetail.map((_item, i) => {
                    if (i === index) {
                      return item;
                    }
                    return _item;
                  }),
                );
              }}
            />
          );
        })}
      </div>

      <div className={styles.footer}>
        <Button
          style={{ width: '100%' }}
          type="primary"
          disabled={
            editDetail.length === 0 || isNotChangedValue || sumOfEditDetail < appointmentNum
          }
          onClick={handleAdjustTask}
        >
          {'调整完成'}
        </Button>
      </div>

      <Drawer
        visible={editVisible}
        position="bottom"
        onClose={() => {
          setEditVisible(false);
          setCurrentCardLicensePlate(undefined);
        }}
      >
        <NPicker
          data={replaceableCar}
          format="licensePlate"
          renderItem={(d) => (
            <CarPlateAndSeatItem
              smallSize
              item={d}
              disabled={d.licensePlate === currentCardLicensePlate}
            />
          )}
          disabled={(item) => item.licensePlate === currentCardLicensePlate}
          onOk={(val) => {
            if (val === null || val === undefined || (Array.isArray(val) && val.length === 0)) {
              setEditVisible(false);
              setCurrentCardLicensePlate(undefined);
              return;
            }
            setEditDetail(
              detail.map((item, index) => {
                if (item.licensePlate === currentCardLicensePlate) {
                  return {
                    licensePlate: replaceableCar.find((item) => item.licensePlate === val)
                      ?.licensePlate,
                    seatNumber: replaceableCar.find((item) => item.licensePlate === val)
                      ?.seatNumber,
                    carTeamFlag: CarTeamFlagEnum.YES,
                  };
                }
                return (
                  editDetail?.[index] ?? {
                    licensePlate: null,
                    seatNumber: null,
                    carTeamFlag: null,
                  }
                );
              }),
            );
            setEditVisible(false);
            setCurrentCardLicensePlate(undefined);
          }}
          onCancel={() => {
            setEditVisible(false);
            setCurrentCardLicensePlate(undefined);
          }}
        />
      </Drawer>
    </div>
  );
};

export default AdjustTaskPage;
