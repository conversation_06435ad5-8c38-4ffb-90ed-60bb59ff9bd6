/* stylelint-disable max-nesting-depth */

.container {
  position: relative;
  padding: 12px;

  .header {
    display: flex;
    flex-direction: row;

    .item {
      padding: 8px 12px;
      margin-bottom: 12px;
      background-color: white;
      border-radius: 4px;
      align-items: center;
      flex-grow: 1;
      flex-direction: column;

      .title {
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: #141737;
      }

      .row {
        display: flex;
        line-height: 22px;

        .label {
          width: 60px;
          font-size: 12px;
          color: #666c7c;

          &::after {
            content: ':';
          }
        }

        .value {
          font-size: 12px;
          font-weight: 500;
          color: #141737;
        }

        .valueGreen {
          color: #00bfa5;
        }

        .valueYellow {
          color: #f5a623;
        }

        .valueRed {
          color: #f5222d;
        }
      }
    }
  }

  .footer {
    width: 100%;
    margin-top: 20px;
  }
}

.carItem {
  display: flex;
  padding: 8px 12px;
  margin-bottom: 12px;
  background-color: white;
  border-radius: 4px;
  align-items: center;
  flex-direction: row;

  .originValue {
    flex-grow: 1;
  }

  .changeValue {
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }

  .placeholder {
    display: flex;
    color: #666c7c;
    flex-direction: row;
    align-items: center;
  }
}
