import { CommonStatusEnum } from '@/pages/bus-employee/appointment/interfaces';
import { t } from '@shein-bbl/react';
interface IRouteLineItem {
  /** 线路id */
  id?: number;
  /** 线路名称 */
  name?: string;
  status?: number;
}

type IChildren = IRouteLineItem[];

export interface IRouteLintList {
  /** 区域id */
  id?: number;
  /** 区域名称 */
  name?: string;
  children?: IChildren;
  status?: number;
}

export interface IWorkingScheduleListParams {
  lineId?: number;
  areaId?: number;
  commutingType?: CommutingTypeEnum;
  scheduleDate?: string;
  pageSize?: number;
  pageNumber?: number;
}

export enum CommutingTypeEnum {
  /** 白天上班 */
  DAY_GO_TO_WORK = 0,
  /** 白天下班 */
  DAY_GO_OFF_WORK = 1,
  /** 夜晚上班 */
  NIGHT_GO_TO_WORK = 2,
  /** 夜晚下班 */
  NIGHT_GO_OFF_WORK = 3,
}

export const CommutingTypeEnumOptions = [
  { label: t('白班/上班'), value: CommutingTypeEnum.DAY_GO_TO_WORK },
  { label: t('白班/下班'), value: CommutingTypeEnum.DAY_GO_OFF_WORK },
  { label: t('夜班/上班'), value: CommutingTypeEnum.NIGHT_GO_TO_WORK },
  { label: t('夜班/下班'), value: CommutingTypeEnum.NIGHT_GO_OFF_WORK },
];

export interface IWorkingScheduleListItem {
  /** 排班id */
  id?: number;
  /** 出发站点名称 */
  departureSiteName?: string;
  /** 目的地站点名称 */
  destinationSiteName?: string;
  /** 上下班类型：0-白班上班；1-白班下班；2-夜班上班；3-夜班下班 */
  commutingType?: CommutingTypeEnum;
  /** 预约人数 */
  appointmentNum?: number;
  /** 发车时间 */
  scheduleTime?: string;
  /** 是否可以进行任务调整或班次重排；0-否，1-是 */
  adjustFlag?: AdjustFlagEnum;
  /** 是否可以进行班次重排；0-否，1-是 */
  rearrangementFlag?: CommonStatusEnum;
  /** 排班车辆详情 */
  carDetailList?: ICarDetailList;
  /** 预计抵达时间 */
  estimatedArrivalTime?: string;
  passengersNum?: number;
}

export interface IScheduleDetail {
  appointmentNum: number;
  scheduleVehicleVoList: ICarDetailListItem[];
}

export interface ICarDetailListItem {
  /** 车牌号 */
  licensePlate?: string;
  /** 座位数量 */
  seatNumber?: number;

  carTeamFlag?: CarTeamFlagEnum;
}

export type ICarDetailList = ICarDetailListItem[];

export enum CarTeamFlagEnum {
  NO = 0,
  YES = 1,
}

export enum AdjustFlagEnum {
  NO = 0,
  YES = 1,
}
