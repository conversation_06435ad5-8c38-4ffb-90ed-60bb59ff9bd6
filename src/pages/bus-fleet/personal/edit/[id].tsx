import { useMemo, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import { useMount } from 'ahooks';
import { Button, Field, Toast } from 'shineout-mobile';
import { IPersonalInfo } from '../interfaces';
import { editPersonalInfo, getPersonalInfo } from '../services';

/**
 * @description PersonalEdit
 * @returns {unknown} desc
 */
const PersonalEdit = () => {
  usePageTitle('个人中心');
  const navigate = useNavigate();
  const originalData = useRef<{
    id: number;
    carTeamName: string;
    managerName: string;
  }>();

  const { id } = useParams();
  const [data, setData] = useState<Pick<IPersonalInfo, 'id' | 'carTeamName' | 'managerName'>>({
    id: Number(id),
  });
  const isCanNotSubmit = useMemo(() => {
    return (
      (data?.carTeamName === originalData.current?.carTeamName &&
        data?.managerName === originalData.current?.managerName) ||
      data?.carTeamName === '' ||
      data?.carTeamName === undefined ||
      data?.carTeamName === null ||
      data?.managerName === '' ||
      data?.managerName === undefined ||
      data?.managerName === null
    );
  }, [data?.carTeamName, data?.managerName]);

  useMount(() => {
    getPersonalInfo().then((res) => {
      originalData.current = {
        id: Number(id),
        carTeamName: res.carTeamName,
        managerName: res.managerName,
      };
      setData(res);
    });
  });

  return (
    <div>
      <Field
        label={'车队名'}
        value={data?.carTeamName}
        placeholder={'请输入车队名'}
        align="right"
        maxLength={20}
        onChange={(e) => {
          setData({
            ...data,
            carTeamName: e.target.value,
          });
        }}
      />
      <Field
        label={'管理员'}
        value={data?.managerName}
        placeholder={'请输入管理员姓名'}
        align="right"
        maxLength={10}
        onChange={(e) => {
          setData({
            ...data,
            managerName: e.target.value,
          });
        }}
      />
      <div
        style={{
          width: '100%',
          marginTop: 30,
          padding: 12,
        }}
      >
        <Button
          style={{
            width: '100%',
          }}
          type="primary"
          disabled={isCanNotSubmit}
          onClick={() => {
            editPersonalInfo(data).then(() => {
              Toast.success('编辑成功');
              navigate('/bus-fleet/personal');
            });
          }}
        >
          {'编辑完成'}
        </Button>
      </div>
    </div>
  );
};
export default PersonalEdit;
