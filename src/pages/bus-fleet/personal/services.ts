import { get, post } from '@/utils';
import { IPersonalInfo } from './interfaces';

/**
 * 个人信息
 * https://soapi.sheincorp.cn/application/3694/routes/185412/doc
 * @returns
 */
export const getPersonalInfo = () => {
  return get<IPersonalInfo>('/bsms/carTeam/userCenter/info');
};

/**
 * 个人信息编辑
 * https://soapi.sheincorp.cn/application/3694/routes/185413/doc
 * @param data
 * @returns
 */
export const editPersonalInfo = (data: IPersonalInfo) => {
  return post<Pick<IPersonalInfo, 'id' | 'carTeamName' | 'managerName'>>(
    '/bsms/carTeam/userCenter/carTeamInfoEdit',
    data,
  );
};
