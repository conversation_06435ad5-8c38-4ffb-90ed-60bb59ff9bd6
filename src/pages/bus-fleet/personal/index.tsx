import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import { COOKIE_KEY_OBJ, removeCookie } from '@/utils';
import { useMount } from 'ahooks';
import { Cell, Toast } from 'shineout-mobile';
import HomeLayout, { HomeLayoutTabEnum } from '../_/components/HomeLayout';
import { SendPhoneMessageTypeEnum } from '../login/interfaces';
import { logout } from '../login/services';
import { removeCookieForBsmsCarTeamToken } from '../login/utils';
import styles from './index.less';
import { IPersonalInfo } from './interfaces';
import { getPersonalInfo } from './services';

/**
 * @description PersonalPage
 * @returns {unknown} desc
 */
const PersonalPage = () => {
  usePageTitle('个人中心');
  const navigate = useNavigate();
  const [info, setInfo] = useState<IPersonalInfo>({});

  useMount(() => {
    getPersonalInfo().then((res) => {
      setInfo(res);
    });
  });

  return (
    <HomeLayout menuCurrent={HomeLayoutTabEnum.PERSONAL_CENTRA}>
      <div>
        <Cell label={'车队名'} value={info?.carTeamName} />
        <Cell label={'管理员'} value={info?.managerName} />
        <Cell label={'账号'} value={info?.phoneNum} />
        <Cell label={'注册日期'} value={info?.registerTime} />
        <div
          className={styles.actionBtn}
          onClick={() => {
            navigate('/bus-fleet/personal/edit/' + info.id);
          }}
        >
          {'编辑资料'}
        </div>
        <div
          className={styles.actionBtn}
          onClick={() => {
            logout({
              type: SendPhoneMessageTypeEnum.CAR_CREW,
            }).then(() => {
              Toast.success('退出登录');
              removeCookieForBsmsCarTeamToken();
              removeCookie(COOKIE_KEY_OBJ.siamToken);
              navigate('/bus-fleet/login');
            });
          }}
        >
          {'退出登录'}
        </div>
      </div>
    </HomeLayout>
  );
};

export default PersonalPage;
