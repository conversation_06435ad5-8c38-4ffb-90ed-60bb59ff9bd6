.container {
  position: relative;
  padding-bottom: 70px;

  .header {
    position: relative;

    .item {
      margin-left: 16px;
      color: #666c7c;

      span {
        color: #141737;
      }
    }

    .drawerDown {
      &:global(.sm-dropdown-menu-item) {
        padding-right: 12px;
        text-align: right;
      }
    }
  }

  .content {
    padding: 12px;

    .empty {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 12px;
      height: 50vh;
      flex-direction: column;
      color: #aaa;
    }
  }

  .add {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 70px;
    max-width: 750px;
    padding: 12px;
    background-color: white;
    // border: 1px solid #e8e8e8;
  }
}
