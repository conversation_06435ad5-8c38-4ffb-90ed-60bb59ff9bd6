export interface IDriverItem {
  id: number;
  /** 司机名称 */
  driverName?: string;
  /** 手机号 */
  driverPhone?: string;
  /** 司机编码 */
  driverCode?: string;
  /** 车牌号 */
  licensePlate?: string;
  /** 座位数 */
  seatNumber?: number;
  /** 在岗状态：0-休息，1-在岗 */
  workStatus?: DriverWorkEnum;
}

export enum DriverWorkEnum {
  /** 休息 */
  REST = 0,
  /** 在岗 */
  WORK = 1,
}

export type ISaveDeriverParams = {
  driverId: number;
  driverName: string;
};

export type IAddDeriverInfo = {
  driverName: string;
  driverPhone: string;
  sendSms: boolean;
};

export const WorkStatusMapOption = [
  {
    value: null,
    label: '全部',
  },
  {
    value: DriverWorkEnum.WORK,
    label: '在岗',
  },
  {
    value: DriverWorkEnum.REST,
    label: '休息',
  },
];
