import { useCallback, useMemo, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import { useMount, useSetState } from 'ahooks';
import { Button, Field, Toast } from 'shineout-mobile';
import { IDriverItem } from '../interfaces';
import { editDeriverInfo, getDriverDetail } from '../services';

/**
 * @description EditDriver
 * @returns {unknown} desc
 */
const EditDriver = () => {
  usePageTitle('司机信息编辑');
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const originalDetails = useRef<{
    driverName: string;
  }>(null);
  const [state, setState] = useSetState<{
    loading: boolean;
    data: Pick<IDriverItem, 'id' | 'driverCode' | 'driverName'>;
  }>({
    loading: false,
    data: null,
  });

  const getData = useCallback(() => {
    setState({ loading: true });
    getDriverDetail(Number(id))
      .then((res) => {
        originalDetails.current = {
          driverName: res.driverName,
        };
        setState({ data: res });
      })
      .finally(() => {
        setState({ loading: false });
      });
  }, [id, setState]);

  useMount(() => {
    getData();
  });

  const handleSubmit = useCallback(() => {
    editDeriverInfo({
      driverId: Number(id),
      driverName: state.data?.driverName,
    }).then(() => {
      Toast.success('编辑成功');
      navigate('/bus-fleet/driver-manage');
    });
  }, [id, state.data?.driverName, navigate]);

  const isCanSubmit = useMemo(() => {
    if (
      state.data?.driverName === '' ||
      state.data?.driverName === null ||
      state.data?.driverName === undefined
    ) {
      return false;
    }
    if (state.data?.driverName === originalDetails.current?.driverName) {
      return false;
    } else {
      return true;
    }
  }, [state.data?.driverName]);

  return (
    <>
      <div
        style={{
          padding: 12,
          background: 'white',
          marginBottom: 12,
          lineHeight: '20px',
          color: '#35383D',
        }}
      >
        {'司机编码：'}
        {state.data?.driverCode}
      </div>
      <Field
        label={'司机姓名'}
        clearable
        value={state.data?.driverName}
        align="right"
        placeholder={'请输入司机姓名'}
        maxLength={8}
        onChange={(e) => {
          setState({ data: { ...state.data, driverName: e.target.value } });
        }}
      />
      <div
        style={{
          marginTop: 60,
          padding: 12,
        }}
      >
        <Button
          disabled={!isCanSubmit}
          style={{ width: '100%' }}
          type="primary"
          onClick={handleSubmit}
        >
          {'编辑完成'}
        </Button>
      </div>
    </>
  );
};

export default EditDriver;
