import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import EmptyBus from '@/pages/bus-employee/components/_/EmptyBus';
import { useMount, usePersistFn } from '@shein-lego/use';
import { useSetState } from 'ahooks';
import { Button, Dialog, DropdownMenu, SearchBar, Toast } from 'shineout-mobile';
import Card from '../_/components/Card';
import CarPlateAndSeatItem from '../_/components/CarPlateAndSeatItem';
import styles from './index.less';
import { DriverWorkEnum, IDriverItem, WorkStatusMapOption } from './interfaces';
import { deleteDriver, getDriverList, switchWorkStatus } from './services';

/**
 * @description DriverManagePage
 * @returns {unknown} desc
 */
const DriverManagePage: React.FC = () => {
  usePageTitle('司机管理');
  const navigate = useNavigate();
  const [state, setState] = useSetState<{
    loading: boolean;
    list: IDriverItem[];
    searchValue: string;
    statusType: DriverWorkEnum;
  }>({
    loading: false,
    list: [],
    searchValue: null,
    statusType: null,
  });

  const initial = usePersistFn(() => {
    setState({ loading: true });
    getDriverList()
      .then((res) => {
        setState({ list: res });
      })
      .finally(() => {
        setState({ loading: false });
      });
  });

  useMount(() => {
    initial();
  });

  const searchedList = useMemo(() => {
    return state.list
      .filter((item) => {
        if (state.searchValue) {
          return item.driverName.includes(state.searchValue);
        }
        return true;
      })
      .filter((item) => {
        if (state.statusType !== null) {
          return item.workStatus === state.statusType;
        }
        return true;
      });
  }, [state.list, state.searchValue, state.statusType]);

  const driverNum = useMemo(() => {
    return searchedList.length;
  }, [searchedList.length]);

  const driverWorkNum = useMemo(() => {
    return searchedList.filter((item) => item.workStatus === DriverWorkEnum.WORK).length;
  }, [searchedList]);

  /**
   * @description handleEditDriver
   * @param {unknown} id desc
   * @returns {unknown} desc
   */
  const handleEditDriver = (id: number) => {
    navigate(`/bus-fleet/driver-manage/edit/${id}`);
  };

  /**
   * @description handleDeleteDriver
   * @param {unknown} id desc
   * @returns {unknown} desc
   */
  const handleDeleteDriver = (id: number) => {
    deleteDriver(id).then(() => {
      Toast.success('操作成功');
      initial();
    });
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <SearchBar
          autoFocus
          clearable={false}
          placeholder={'请输入司机姓名进行搜索'}
          onSearch={(v) => setState({ searchValue: v })}
          onCancel={() => setState({ searchValue: null })}
        />
        <DropdownMenu>
          <div className={styles.item}>
            {'司机数量：'}
            <span>{driverNum}</span>
          </div>
          <div className={styles.item}>
            {'在岗数量：'}
            <span>{driverWorkNum}</span>
          </div>
          <DropdownMenu.Item
            onChange={(v) => setState({ statusType: v })}
            className={styles.drawerDown}
            title={
              state.statusType === null
                ? '全部'
                : state.statusType === DriverWorkEnum.WORK
                ? '在岗'
                : '休息'
            }
            data={WorkStatusMapOption}
            format="value"
            renderItem={(item) => item.label}
          />
        </DropdownMenu>
      </div>
      <div className={styles.content}>
        {searchedList.map((item) => (
          <Card
            key={item.driverCode}
            id={item.id}
            status={Number(item.workStatus)}
            title={item.driverName}
            subTitle={item.driverPhone}
            statusText={{ disabled: '休息', enabled: '在岗' }}
            onToggle={(status) => {
              switchWorkStatus(item.id, status).then(() => {
                Toast.success('切换成功');
                initial();
              });
            }}
            data={[
              {
                label: '司机编码',
                value:
                  item.driverCode !== '' && item.driverCode !== null && item.driverCode
                    ? item.driverCode
                    : '-',
              },
              {
                label: '绑定车辆',
                value: item.licensePlate ? (
                  <CarPlateAndSeatItem
                    smallSize
                    item={{
                      licensePlate: item.licensePlate,
                      seatNumber: item.seatNumber,
                    }}
                  />
                ) : (
                  '-'
                ),
              },
            ]}
            opt={[
              {
                title: '编辑信息',
                callback: (id) => handleEditDriver(id),
                icon: 'edit',
                type: 'primary',
              },
              {
                title: '删除司机',
                callback: (id) => {
                  Dialog.confirm({
                    title: '删除司机',
                    message: '确认删除该司机',
                    onOk: () => {
                      handleDeleteDriver(id);
                    },
                  });
                },
                icon: 'delete',
                type: 'danger',
              },
            ]}
          />
        ))}
        {searchedList.length === 0 && <EmptyBus text={'暂无数据'} />}
      </div>
      <div className={styles.add}>
        <Button
          style={{
            width: '100%',
          }}
          type="primary"
          onClick={() => navigate('/bus-fleet/driver-manage/add')}
        >
          {'添加司机'}
        </Button>
      </div>
    </div>
  );
};

export default DriverManagePage;
