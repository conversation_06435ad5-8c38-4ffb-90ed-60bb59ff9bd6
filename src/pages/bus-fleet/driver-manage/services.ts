import { get, post } from '@/utils';
import { IAddDeriverInfo, IDriverItem, ISaveDeriverParams } from './interfaces';

/**
 * 车队端-司机列表
 * https://soapi.sheincorp.cn/application/3694/routes/184836/doc
 * @returns
 */
export const getDriverList = () => {
  return get<IDriverItem[]>('/bsms/carTeam/driver/list');
};

/**
 *https://soapi.sheincorp.cn/application/3694/routes/184550/doc
 * @param id
 * @returns
 */
export const getDriverDetail = (id: number) => {
  return get<Pick<IDriverItem, 'id' | 'driverCode' | 'driverName'>>(`/bsms/carTeam/driver/detail`, {
    id,
  });
};

/**
 * 编辑司机信息
 * https://soapi.sheincorp.cn/application/3694/routes/184714/doc
 * @param params
 * @returns
 */
export const editDeriverInfo = (params: ISaveDeriverParams) => {
  return post<boolean>('/bsms/carTeam/driver/editDriver', params);
};

/**
 * 新增司机
 * https://soapi.sheincorp.cn/application/3694/routes/184580/doc
 * @param params
 * @returns
 */
export const addDeriverInfo = (params: IAddDeriverInfo) => {
  return post<boolean>('/bsms/carTeam/driver/addDriver', params);
};

/**
 * 解绑司机（删除）
 * https://soapi.sheincorp.cn/application/3694/routes/184716/doc
 * @param id
 * @returns
 */
export const deleteDriver = (id: number) => {
  return post<boolean>('/bsms/carTeam/driver/deletedDriver', { driverId: id });
};

/**
 * 司机切换在岗状态
 * https://soapi.sheincorp.cn/application/3694/routes/184551/doc
 * @param id
 * @param status
 * @returns
 */
export const switchWorkStatus = (id: number, status: boolean) => {
  return post('/bsms/carTeam/driver/switchWorkStatus', {
    driverId: id,
    workStatus: status ? 1 : 0,
  });
};
