import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import { Button, Cell, Field, Switch, Toast } from 'shineout-mobile';
import { IAddDeriverInfo } from '../interfaces';
import { addDeriverInfo } from '../services';

/**
 * @description AddDriver
 * @returns {unknown} desc
 */
const AddDriver: React.FC = () => {
  usePageTitle('添加司机');
  const navigate = useNavigate();
  const [formData, setFormData] = useState<IAddDeriverInfo>({
    driverName: undefined,
    driverPhone: undefined,
    sendSms: false,
  });

  /**
   * @description handleSubmit
   * @returns {unknown} desc
   */
  const handleSubmit = () => {
    addDeriverInfo(formData).then(() => {
      Toast.success('添加成功');
      setFormData({
        driverName: '',
        driverPhone: '',
        sendSms: false,
      });
      navigate('/bus-fleet/driver-manage', {
        replace: true,
      });
    });
  };

  return (
    <div>
      <Field
        label={'司机姓名'}
        value={formData?.driverName}
        placeholder={'请输入司机姓名'}
        align="right"
        maxLength={8}
        onChange={(e) => {
          setFormData({ ...formData, driverName: e.target.value });
        }}
      />
      <Field
        label={'手机号'}
        placeholder={'请输入手机号'}
        value={formData?.driverPhone}
        align="right"
        type="number"
        digits={0}
        maxLength={11}
        onChange={(e) => {
          setFormData({ ...formData, driverPhone: e.target.value });
        }}
      />
      <Cell
        center
        label={'发送注册短信'}
        value={
          <Switch
            value={formData?.sendSms}
            content={['发送', '不发送']}
            onChange={(v) => {
              setFormData({ ...formData, sendSms: v });
            }}
          />
        }
      />

      <div
        style={{
          marginTop: 60,
          padding: 12,
        }}
      >
        <Button
          disabled={
            formData?.driverName === '' ||
            formData?.driverName === null ||
            formData?.driverName === undefined ||
            formData?.driverPhone === '' ||
            formData?.driverPhone === null ||
            formData?.driverPhone === undefined ||
            formData?.driverPhone.length !== 11
          }
          style={{ width: '100%' }}
          type="primary"
          onClick={handleSubmit}
        >
          {'确认添加'}
        </Button>
      </div>
    </div>
  );
};

export default AddDriver;
