import { TaskStatusEnum } from '@/pages/bus-driver/home/<USER>';

/** 任务类型：0-常规任务；1-加趟任务 */
export enum TaskTypeEnum {
  REGULAR = 0,
  ADDITIONAL = 1,
}

export type ITaskItem = {
  /** 任务id */
  id?: number;
  /** 班次时间 */
  scheduleTime?: string;
  /** 上下班类型：0-白班上班；1-白班下班；2-夜班上班；3-夜班下班 */
  commutingType?: number;
  /** 上下班类型：0-白班上班；1-白班下班；2-夜班上班；3-夜班下班 */
  commutingTypeName?: number;
  /** 司机名称 */
  driverName?: string;
  /** 车牌号 */
  licensePlate?: string;
  /** 预计行驶时长（分钟） */
  estimatedDrivingTime?: number;
  /** 实际行驶时长（分钟） */
  actualDrivingTime?: number | string;
  /** 座位数量 */
  seatNumber?: number;
  /** 任务状态：0-待出发；1-验票中；2-进行中；3-已完成，4-缺勤 */
  taskStatus?: TaskStatusEnum;
  /** 任务状态，展示用 0-待出发；1-验票中；2-进行中；3-已完成，4-缺勤 */
  taskStatusStr?: string;
  /** 出发站点名称 */
  departureSiteName?: string;
  /** 目的地站点名称 */
  destinationSiteName?: string;
  /** 任务编码 */
  taskNum?: string;
  /** 任务类型 */
  taskType: TaskTypeEnum;
};
