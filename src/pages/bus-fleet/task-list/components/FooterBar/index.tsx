import { useState } from 'react';
import moment from 'moment';
import { Button, Calendar, Loading, Toast } from 'shineout-mobile';
import useTaskExportPolling from '../../hooks/useTaskExportPolling';

const FooterBar: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [value, setValue] = useState<Date[]>();
  const [loading, setLoading] = useState(false);
  const { startPolling } = useTaskExportPolling();
  /**
   * @description handleOpen
   * @returns {unknown} desc
   */
  const handleOpen = () => {
    setVisible(true);
  };

  /**
   * @description handleClose
   * @returns {unknown} desc
   */
  const handleClose = () => {
    setVisible(false);
  };

  const handleExport = () => {
    setLoading(true);
    startPolling({
      scheduleDateStart: moment(value?.[0]).format('YYYY-MM-DD'),
      scheduleDateEnd: moment(value?.[1]).format('YYYY-MM-DD'),
    })
      .then((url) => {
        console.log('url', url);
        Toast.success('导出成功');
        handleDownload(url);
      })
      // .catch((msg) => {
      //   Toast.fail(msg ?? '导出失败，请稍后再试');
      // })
      .finally(() => {
        setLoading(false);
      });
  };

  /**
   * H5 移动端下载文件
   */
  const handleDownload = (url: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.target = '_blank';
    // link.download = 'file.pdf'; // Set the desired file name here
    link.click();
  };

  return (
    <div>
      {loading && (
        <div
          style={{
            zIndex: 9999,
            position: 'absolute',
            top: 0,
            left: 0,
            height: '100vh',
            width: '100%',
            maxWidth: '750px',
            background: '#333',
            opacity: '50%',
            display: 'block',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Loading size={32} color="#197AFA" vertical style={{ marginTop: '45vh' }}>
            <div style={{ marginTop: 8, color: '#197AFA', fontSize: 16 }}>
              文件导出中，请稍后...
            </div>
          </Loading>
        </div>
      )}
      <div className="text-right">
        <Button type="primary" onClick={handleOpen} size="small">
          任务明细导出
        </Button>

        <Calendar
          round
          type="range"
          confirmDisabled={(value || []).length < 2}
          confirmButtonText="导出"
          value={value}
          visible={visible}
          range={93}
          maxDate={moment().toDate()}
          defaultPosition={moment().toDate()}
          drawer={{ position: 'bottom', height: 486, closeable: true, visible: true }} // 控制Drawer
          onChange={(val) => {
            console.log('change', val);
            setValue(val);
          }}
          onConfirm={() => {
            console.log('confirm', value);
            handleExport();
          }}
          onCancel={() => {
            handleClose();
          }}
        />
      </div>
    </div>
  );
};

export default FooterBar;
