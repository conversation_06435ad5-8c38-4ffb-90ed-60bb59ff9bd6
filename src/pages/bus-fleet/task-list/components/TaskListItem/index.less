.container {
  position: relative;
  padding: 12px;
  margin-bottom: 12px;
  background-color: white;
  border-radius: 4px;

  .header {
    display: flex;
    align-items: center;

    .title {
      display: flex;
      flex-grow: 1;
      align-items: center;

      .text {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        color: #141737;
      }

      .shortArrow {
        display: flex;
        width: 25px;
        height: 10px;
        margin: 0 5px;
        background-image: url('./short-arrow.png');
        background-repeat: no-repeat;
        background-size: contain;
      }
    }

    .toggle {
      width: 40px;
      min-width: 40px;
      text-align: right;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 10px 0;

    .scheduleTime {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 10px;
    }
  }
}

.item {
  display: flex;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #666c7c;
  flex-direction: row;
  align-items: center;
  gap: 10px;

  .label {
    display: flex;
    width: 70px;
    min-width: 70px;
  }

  .value {
    display: flex;
    color: #141737;
    flex-grow: 1;
    align-items: flex-start;
  }
}
