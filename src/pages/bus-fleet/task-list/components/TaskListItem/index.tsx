import { ReactNode, useState } from 'react';
import AutoGapLabel from '@/_/components/AutoGapLabel';
import { TaskStatusEnum } from '@/pages/bus-driver/home/<USER>';
import CarPlateAndSeatItem from '@/pages/bus-fleet/_/components/CarPlateAndSeatItem';
import WorkTypeItem from '@/pages/bus-fleet/_/components/WorkTypeItem';
import classNames from 'classnames';
import { Button } from 'shineout-mobile';
import { ITaskItem } from '../../interfaces';
import styles from './index.less';

/**
 * @description Item
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const Item: React.FC<{
  label: string;
  value: string | ReactNode;
  style?: React.CSSProperties;
}> = ({ label, value, style: customStyles }) => {
  return (
    <div className={classNames(styles.item, customStyles)}>
      <AutoGapLabel className={styles.label} label={label} />
      <div className={styles.value}>{value}</div>
    </div>
  );
};

/**
 * @description TaskListItem
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const TaskListItem: React.FC<{ item: ITaskItem }> = (props) => {
  const { item } = props;
  const [toggle, setToggle] = useState(false);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.title}>
          <span className={styles.text}>{item.departureSiteName}</span>
          <span className={styles.shortArrow} />
          <span className={styles.text}>{item.destinationSiteName}</span>
        </div>
        <div className={styles.toggle}>
          {toggle ? (
            <Button text type="primary" onClick={() => setToggle(!toggle)}>
              {'收起'}
            </Button>
          ) : (
            <Button text type="primary" onClick={() => setToggle(!toggle)}>
              {'展开'}
            </Button>
          )}
        </div>
      </div>
      <div className={styles.content}>
        <Item
          style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', paddingTop: 5 }}
          label={'发车时间'}
          value={
            <div className={styles.scheduleTime}>
              <span className="font-bold">{item.scheduleTime}</span>
              <WorkTypeItem commutingType={item.commutingType} taskType={item.taskType} />
            </div>
          }
        />
        {toggle && <Item label={'司机'} value={item.driverName} />}
        {toggle && (
          <Item
            label={'车牌'}
            value={
              <CarPlateAndSeatItem
                item={{
                  licensePlate: item?.licensePlate,
                  seatNumber: item?.seatNumber,
                }}
              />
            }
          />
        )}
        {toggle && <Item label={'预计行驶'} value={item.estimatedDrivingTime + ' 分钟'} />}
        {toggle && (
          <Item
            label={'实际行驶'}
            value={
              item.taskStatus !== TaskStatusEnum.COMPLETED ? '-' : item.actualDrivingTime + ' 分钟'
            }
          />
        )}
        {toggle && <Item label={'任务状态'} value={item.taskStatusStr} />}
        {toggle && <Item label={'任务编码'} value={item.taskNum} />}
      </div>
    </div>
  );
};
export default TaskListItem;
