import { useEffect, useRef } from 'react';
import { useRequest } from 'ahooks';
import { Toast } from 'shineout-mobile';
import { exportTaskList, pollingTaskResult } from '../services';

const useTaskExportPolling = () => {
  const pollingInterval = 3000;

  const intervalRef = useRef<NodeJS.Timeout>();

  const firstRequest = useRequest((_payload) => exportTaskList(_payload), {
    manual: true,
  });

  const polling = useRequest((_taskId?: string) => pollingTaskResult({ id: _taskId }), {
    manual: true,
  });

  const startPolling = async (payload: { scheduleDateStart: string; scheduleDateEnd: string }) => {
    const taskId = await firstRequest.runAsync(payload);

    return new Promise<string | undefined>((resolve, reject) => {
      intervalRef.current = setInterval(async () => {
        try {
          const response = await polling.runAsync(taskId);

          if (response?.status === '0' && response?.code === '0') {
            // 成功，停止轮询
            resolve(response?.data);
            clearInterval(intervalRef.current);
          } else if (response?.status === '200') {
            // 继续轮训
          } else {
            // 网络错误，停止轮训
            reject();
            clearInterval(intervalRef.current);
            Toast.fail(response?.msg);
          }
        } catch (_e) {
          reject();
          clearInterval(intervalRef.current);
        }
      }, pollingInterval);
    });
  };

  /**
   * @description stopPolling
   * @returns {unknown} desc
   */
  const stopPolling = () => {
    clearInterval(intervalRef.current);
  };

  useEffect(() => {
    return () => {
      clearInterval(intervalRef.current);
    };
  }, []);

  return {
    startPolling,
    stopPolling,
  };
};

export default useTaskExportPolling;
