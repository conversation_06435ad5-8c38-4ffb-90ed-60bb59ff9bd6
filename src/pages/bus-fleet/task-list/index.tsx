import { useRef, useState } from 'react';
import { Page } from '@/_/components';
import EmptyBus from '@/pages/bus-employee/components/_/EmptyBus';
import LoadingBus from '@/pages/bus-employee/components/_/LoadingBus';
import { t } from '@shein-bbl/react';
import { useInfiniteScroll, useTitle } from 'ahooks';
import moment from 'moment';
import DateFilterHeader from '../schedule-task/components/DateFilterHeader';
import FooterBar from './components/FooterBar';
import TaskListItem from './components/TaskListItem';
import { ITaskItem } from './interfaces';
import { getTaskList } from './services';

const PAGE_SIZE = 20;

const TaskList = () => {
  useTitle('任务列表');
  const [searchParams, setSearchParams] = useState<{
    scheduleDate?: string;
  }>({
    scheduleDate: moment().format('YYYY-MM-DD'),
  });
  const contentRef = useRef<HTMLDivElement>(null);
  const { data, loading, loadingMore, noMore, reload } = useInfiniteScroll<{
    list: ITaskItem[];
    total: number;
  }>(
    (d) => {
      const page = d ? Math.ceil(d.list.length / PAGE_SIZE) + 1 : 1;
      return getTaskList({
        pageNumber: page,
        pageSize: PAGE_SIZE,
        ...searchParams,
      }).then((res) => {
        return {
          list: res.rows,
          total: res.total,
        };
      });
    },
    {
      target: contentRef,
      isNoMore: (d) => d && d?.list?.length === d?.total,
    },
  );

  return (
    <Page contentClassName="p-0 flex flex-col" footer={<FooterBar />}>
      <DateFilterHeader
        date={searchParams?.scheduleDate}
        handleChange={(v) => {
          setSearchParams({
            ...searchParams,
            scheduleDate: v,
          });
          setTimeout(() => reload(), 0);
        }}
      />
      <div ref={contentRef} className="flex-1 min-h-0 overflow-auto p-[12px]">
        {!loading && data?.list?.map((item) => <TaskListItem key={item.id} item={item} />)}
        {loadingMore && (
          <div className="text-center text-[#666c7c] text-[12px] mt-md">{t('加载中...')}</div>
        )}
        {noMore && data?.list?.length >= PAGE_SIZE && (
          <div className="text-center text-[#666c7c] text-[12px] mt-md">{t('没有更多了')}</div>
        )}
        {!loading && data?.list?.length === 0 && <EmptyBus height="100%" />}
        {loading && <LoadingBus />}
      </div>
      {/*{<FooterBar />}*/}
    </Page>
  );
};
export default TaskList;
