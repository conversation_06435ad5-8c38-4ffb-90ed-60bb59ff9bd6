import { get, post } from '@/utils';
import { ITaskItem } from './interfaces';

/**
 * 车队任务列表
 * https://soapi.sheincorp.cn/application/3694/routes/194340/doc
 * @param params
 * @returns
 */
export const getTaskList = (params: {
  /** 排期日期 */
  scheduleDate?: string;
  pageNumber?: number;
  pageSize?: number;
  mark?: number;
}) => {
  return post<{
    total: number;
    rows: ITaskItem[];
  }>('/bsms/carTeam/task/selectTaskPage', params);
};

/**
 * 车队任务列表导出
 * https://soapi.sheincorp.cn/application/3694/routes/203694/doc
 * @param params
 * @returns
 */
export const exportTaskList = (params: { scheduleDateStart: string; scheduleDateEnd: string }) => {
  return post<string>('/bsms/carTeam/task/export', params);
};

/**
 * 车队任务列表导出轮询接口
 * https://soapi.sheincorp.cn/application/3694/routes/203694/doc
 * @param params
 * @returns
 */
export const pollingTaskResult = (params: { id: string }) => {
  return get<{ code?: string; data?: string; status?: string; msg?: string }>(
    '/bsms/carTeam/task/request',
    params,
  );
};
