import { useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Page } from '@/_/components';
import Form from '@/_/components/Form';
import Icon from '@/_/components/Icon';
import { usePageTitle } from '@/_/hooks';
import ImageView from '@/pages/canteen/_/components/image-view';
import { EMPTY_STR } from '@/share';
import { t } from '@shein-bbl/react';
import { useMount, useRequest } from 'ahooks';
import { Button, Cell, Field, Rate, Toast } from 'shineout-mobile';
import { getCanteenDishEvaluate } from '../_/api';
import { ICanteenDishEvaluate } from '../_/interfaces';
import { formatDishDate } from '../_/utils';
import { submitCanteenDishEvaluation } from './api';
import ImageUpload from './components/image-upload';
import styles from './index.less';
import type { IEvaluationFormRequestBody } from './interfaces';

const StarRate = Rate(
  <Icon name="m-star-fill" color="#E8EBF0" fontSize={24} />,
  <Icon name="m-star-fill" color="#FFC91A" fontSize={24} />,
);

type IEvaluationFormData = Omit<IEvaluationFormRequestBody, 'orderId'>;

const TravelEvaluation = () => {
  usePageTitle(t('评价'));
  const [detail, setDetail] = useState<ICanteenDishEvaluate>({});
  const [formData, setFormData] = useState<IEvaluationFormData>({});
  const [isScored, setIsScored] = useState(true);
  const { id } = useParams<'id'>();
  useMount(() => {
    if (!id) {
      Toast.fail(t('访问参数错误'));
      return;
    }
    getCanteenDishEvaluate(id).then((res) => {
      setDetail(res);
      const isScored = res.evaluateScore > 0;
      setIsScored(isScored);
      if (isScored) {
        setFormData({
          evaluateScore: res.evaluateScore,
          evaluateContent: res.evaluateContent || '',
          fileInfo: (res.evaluatePic || []).map((item) => ({
            fileName: item,
            fileUrl: item,
          })),
        });
      }
    });
  });

  const navigate = useNavigate();
  /**
   * @description 返回
   * @returns {void}
   */
  const handleBack = (): void => {
    navigate(`/canteen/order/${id}`, { replace: true });
  };

  const submitButtonRef = useRef<HTMLButtonElement>(null);
  const { loading, runAsync } = useRequest(submitCanteenDishEvaluation, {
    manual: true,
  });

  /**
   * @description handleSubmit
   * @param {IEvaluationFormData} values 参数
   * @returns {void}
   */
  const handleSubmit = (values: IEvaluationFormData): void => {
    runAsync({
      ...values,
      orderId: id,
    }).then(() => {
      Toast.success(t('评价成功'));
      handleBack();
    });
  };

  return (
    <Page
      contentClassName={styles.content}
      footer={
        isScored ? (
          <Button className="w-full" onClick={handleBack}>
            {t('返回')}
          </Button>
        ) : (
          <div className="flex gap-8">
            <Button className="flex-1" onClick={handleBack}>
              {t('取消')}
            </Button>
            <Button
              className="flex-1"
              type="primary"
              onClick={() => submitButtonRef.current?.click()}
              loading={loading}
            >
              {t('提交评价')}
            </Button>
          </div>
        )
      }
    >
      <div className={styles.header}>
        <div className={styles.title}>{detail?.mealTimePeriodTypeName || EMPTY_STR}</div>
        <div className={styles.info}>
          <Icon name="m-schedule-shineout-fill" fontSize={16} />
          <span className={styles.infoValue}>
            {detail?.date ? formatDishDate(detail.date) : EMPTY_STR}
          </span>
        </div>
        <div className={styles.info}>
          <Icon name="m-location-shineout-fill" fontSize={16} />
          <span className={styles.infoValue}>{detail?.canteenName || EMPTY_STR}</span>
        </div>
        <div className={styles.info}>
          <Icon name="m-home-house-shineout-fill" fontSize={16} />
          <span className={styles.infoValue}>{detail?.stallName || EMPTY_STR}</span>
        </div>
      </div>
      <Form values={formData} onSubmit={handleSubmit}>
        <Form.Field name="evaluateScore" rules={{ required: t('请选择') }}>
          {({ value, onChange, errorMessage }) => (
            <Cell
              label={t('就餐评分')}
              required
              errorMessage={errorMessage}
              valueAlign="left"
              value={<StarRate gap={4} value={value} onChange={onChange} disabled={isScored} />}
            />
          )}
        </Form.Field>
        <Form.Field name="evaluateContent">
          {({ value, onChange, errorMessage }) => (
            <Field
              label={t('评价内容')}
              className="pt-0"
              type="textarea"
              placeholder={t('请输入')}
              showWordLimit
              maxLength={100}
              value={value}
              onChange={onChange}
              errorMessage={errorMessage}
              readonly={isScored}
              clearable={!isScored}
            />
          )}
        </Form.Field>
        <Form.Field name="fileInfo">
          {({ value, onChange, errorMessage }) => (
            <Cell
              label={t('上传图片')}
              errorMessage={errorMessage}
              value={
                isScored ? (
                  <ImageView data={detail?.evaluatePic || []} />
                ) : (
                  <ImageUpload value={value} onChange={onChange} />
                )
              }
            />
          )}
        </Form.Field>
        <button type="submit" ref={submitButtonRef} className="hidden" />
      </Form>
    </Page>
  );
};

export default TravelEvaluation;
