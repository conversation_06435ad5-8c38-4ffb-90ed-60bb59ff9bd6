import React from 'react';
import { t } from '@shein-bbl/react';
import { Icons, Toast, Upload } from 'shineout-mobile';

const add = <Icons style={{ color: '#CCCFD7', fontSize: 20 }} name="add" />;
/**
 * 企业微信的BUG，但input设置accept时，如果选择的文件不是图片，会被企业微信强制编成图片且size是0
 * 所以添加此来区分是否在微信中来方便本地开发
 * */
const isInWeChat = !!window.navigator.userAgent.match(/wxwork/i);

interface IImageUploadProps {
  value?: { fileName: string; fileUrl: string }[];
  onChange?: (data: { fileName: string; fileUrl: string }[]) => void;
  disabled?: boolean;
}

/**
 * @description 图片上传
 * @param {IImageUploadProps} props 图片上传组件的props
 * @returns {React.ReactElement} 图片上传组件
 */
const ImageUpload = (props: IImageUploadProps): React.ReactElement => {
  const { value, onChange, disabled } = props;

  return (
    <Upload
      icon={add}
      action="/attachmentFile/uploadImage"
      accept={isInWeChat ? undefined : 'image/jpg,image/jpeg,image/png,image/bmp'}
      limit={5}
      name="fileList"
      validator={{
        size: (size) => size > 5 * 1024 * 1024 && new Error(t('单张图片限5Mb以内')),
        custom: (file) => {
          const { size, type } = file;
          if (!/image\/(jpe?g|png|bmp)/.test(type)) {
            return new Error(t('只能上传jpg、jpeg、png、bmp格式的图片'));
          }
          if (size > 5 * 1024 * 1024) {
            return new Error(t('单张图片限5Mb以内'));
          }
          return undefined;
        },
      }}
      renderItem="fileUrl"
      multiple
      onSuccess={(responseJson) => {
        try {
          const res = JSON.parse(responseJson);
          if (!res.data) {
            Toast.fail(res.msg);
            return;
          }
          return res.data;
        } catch {
          Toast.fail(t('上传文件出错'));
        }
      }}
      value={value}
      onChange={(list) => onChange?.(list.filter((item) => Boolean(item)))}
      disabled={disabled}
    />
  );
};

export default ImageUpload;
