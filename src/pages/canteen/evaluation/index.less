.content {
  background: #f7f8fa;
}

.header {
  padding: 12px;
  margin-bottom: 8px;
  background-color: #fff;
  border-radius: 4px;
  background-image: url('./header-bg.png');
  background-size: auto 100%;
  background-repeat: no-repeat;
  background-position: top right;

  .title {
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #141737;
  }

  .info {
    display: flex;
    align-items: center;
    margin-top: 6px;
    color: #999da8;

    &Value {
      margin-left: 4px;
      font-size: 14px;
      line-height: 22px;
      color: #666c7c;
    }
  }
}
