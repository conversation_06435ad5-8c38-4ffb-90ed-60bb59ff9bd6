import { post } from '@/utils';
import { IEvaluationFormRequestBody } from './interfaces';

/**
 * @description 提交订单评价
 * @param {IEvaluationFormRequestBody} params 订单评价表单数据
 * @returns {Promise<IEvaluationFormRequestBody>} 订单评价表单数据
 */
export const submitCanteenDishEvaluation = (
  params: IEvaluationFormRequestBody,
): Promise<IEvaluationFormRequestBody> => {
  return post<IEvaluationFormRequestBody>('/canteen/staff/evaluation/submit', params);
};
