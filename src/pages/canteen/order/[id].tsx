import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Page } from '@/_/components';
import { EMPTY_STR } from '@/share';
import { t } from '@shein-bbl/react';
import { useRequest } from 'ahooks';
import classNames from 'classnames';
import { Button } from 'shineout-mobile';
import { getOrderDetail } from '../_/api';
import OrderCard from '../_/components/order-card';
import { EOrderStatus, orderTypeMap } from '../_/interfaces/order';
import CancelOrderButton from './components/cancel-order-button';
import EvaluateDetail from './components/evaluate-detail';
import OrderStatusCard from './components/order-status-card';
import styles from './index.less';

/**
 * @description 订单详情页
 * @returns {React.ReactElement} 订单详情页
 */
const OrderPage = (): React.ReactElement => {
  const { id } = useParams() as { id: string };
  const { data, refresh } = useRequest(getOrderDetail, {
    defaultParams: [{ orderId: id }],
  });
  const navigate = useNavigate();

  if (!data) return null;

  let footer = null;
  // 预定截止时间之前可取消，反之按钮隐藏
  if (data.orderStatus === EOrderStatus.RESERVED && data.canCancel) {
    footer = <CancelOrderButton orderNo={data.orderNo} onReload={() => refresh()} />;
    // 评价按钮在订单完成之后显示7天，超出隐藏
  } else if (
    data.orderStatus === EOrderStatus.COMPLETED &&
    !data.evaluateScore &&
    data.canEvaluate
  ) {
    footer = (
      <div className="p-[12px] pb-[4px] text-right">
        <Button type="primary" onClick={() => navigate(`/canteen/evaluation/${id}`)}>
          {t('去评价')}
        </Button>
      </div>
    );
  }

  return (
    <Page
      className={styles.page}
      contentClassName="bg-transparent"
      title={t('详情')}
      footerClassName="pl-0 pr-0 pt-0"
      footer={footer}
    >
      <OrderStatusCard detail={data} />
      {data.evaluateScore > 0 && <EvaluateDetail score={data.evaluateScore} orderId={id} />}
      <OrderCard
        mealTimePeriodTypeName={data.mealTimePeriodTypeName}
        date={data.date}
        canteenName={data.canteenName}
        stallName={data.stallName}
        dishes={data?.orderDetail || []}
      />
      <div className={styles.info}>
        <div className={styles.infoItem}>
          <span className={styles.infoItemLabel}>{t('就餐类型：')}</span>
          <span className={styles.infoItemValue}>{orderTypeMap[data.orderType]}</span>
        </div>
        {data.orderStatus === EOrderStatus.COMPLETED && (
          <div className={styles.infoItem}>
            <span className={styles.infoItemLabel}>{t('完成时间：')}</span>
            <span className={styles.infoItemValue}>{data.finishTime || EMPTY_STR}</span>
          </div>
        )}
        <div className={styles.infoItem}>
          <span className={styles.infoItemLabel}>{t('预定时间：')}</span>
          <span className={styles.infoItemValue}>{data.bookingTime || EMPTY_STR}</span>
        </div>
        <div className={classNames(styles.infoItem, styles.payItem)}>
          <span className={styles.infoItemLabel}>{t('支付方式：')}</span>
          <div className={styles.infoItemValue}>
            {data.payDetail.length
              ? data.payDetail?.map((item) => (
                  <div key={item.payChannelType}>
                    <span>{item.payChannelType}</span>
                    <span style={{ marginLeft: 4 }}>¥{item.payAmount}</span>
                  </div>
                ))
              : EMPTY_STR}
          </div>
        </div>
      </div>
    </Page>
  );
};

export default OrderPage;
