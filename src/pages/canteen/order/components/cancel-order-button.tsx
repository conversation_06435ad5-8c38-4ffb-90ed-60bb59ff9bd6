import { cancelCanteenOrder } from '@/pages/canteen/_/api';
import { t } from '@shein-bbl/react';
import { useRequest } from 'ahooks';
import { Button, Dialog, Toast } from 'shineout-mobile';

interface CancelOrderButtonProps {
  orderNo: string;
  onReload: () => void;
}

/**
 * @description CancelOrderButton
 * @param {CancelOrderButtonProps} props 参数
 * @returns {React.ReactElement} 取消预定按钮
 */
const CancelOrderButton = ({ orderNo, onReload }: CancelOrderButtonProps): React.ReactElement => {
  const { runAsync: runCancelOrder, loading: cancelOrderLoading } = useRequest(cancelCanteenOrder, {
    manual: true,
  });

  /**
   * @description handleCancel
   * @returns {void}
   */
  const handleCancel = (): void => {
    Dialog.confirm({
      title: t('是否取消预定'),
      message: t('取消后，退款金额将按原渠道退回'),
      confirmButtonText: t('确定取消'),
      cancelButtonText: t('暂不取消'),
      onOk: () => {
        runCancelOrder(orderNo).then(() => {
          Toast.success(t('取消成功'));
          onReload();
        });
      },
    });
  };

  return (
    <div className="p-[12px] pb-[4px] text-right">
      <Button loading={cancelOrderLoading} onClick={handleCancel}>
        {t('取消预定')}
      </Button>
    </div>
  );
};

export default CancelOrderButton;
