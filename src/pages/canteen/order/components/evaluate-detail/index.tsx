import { useState } from 'react';
import Icon from '@/_/components/Icon';
import { getCanteenDishEvaluate } from '@/pages/canteen/_/api';
import ImageView from '@/pages/canteen/_/components/image-view';
import { EMPTY_STR } from '@/share';
import { t } from '@shein-bbl/react';
import { useRequest } from 'ahooks';
import { Button, Drawer, Loading, Rate } from 'shineout-mobile';
import styles from './index.less';

const StarRate = Rate(
  <Icon name="m-star-fill" color="#E8EBF0" fontSize={24} />,
  <Icon name="m-star-fill" color="#FFC91A" fontSize={24} />,
);

interface IEvaluateDetailProps {
  score: number;
  orderId: string;
}

/**
 * @description 评价详情
 * @param {IEvaluateDetailProps} props
 * @returns {React.ReactElement} 评价详情
 */
const EvaluateDetail = ({ score, orderId }: IEvaluateDetailProps): React.ReactElement => {
  const [visible, setVisible] = useState(false);
  const {
    runAsync: getEvaluate,
    data,
    loading,
  } = useRequest(getCanteenDishEvaluate, {
    manual: true,
  });

  /**
   * @description 点击
   * @returns {void}
   */
  const handleClick = (): void => {
    setVisible(true);
    getEvaluate(orderId);
  };

  return (
    <>
      <div className={styles.evaluate} onClick={handleClick}>
        <span className={styles.title}>{t('评价')}</span>
        <div className={styles.content}>
          <StarRate value={score} gap={4} disabled />
          <span className={styles.score}>{score}</span>
          <Button type="primary" text>
            {t('详情')}
          </Button>
        </div>
      </div>
      <Drawer
        round
        closeable
        position="bottom"
        drawerClass={styles.cartDrawer}
        height="36vh"
        visible={visible}
        onClose={() => setVisible(false)}
      >
        <div className={styles.wrapper}>
          <div className={styles.wrapperTitle}>{t('评价详情')}</div>
          {loading ? (
            <div className="text-center p-4">
              <Loading />
            </div>
          ) : (
            <div className={styles.wrapperContent}>
              <div className={`${styles.wrapperItem} items-center`}>
                <div className={styles.wrapperItemTitle}>{t('就餐评分：')}</div>
                <div className={styles.wrapperItemContent}>
                  <StarRate value={score} gap={4} disabled />
                  <span className="ml-[16px]">{score}</span>
                </div>
              </div>
              <div className={styles.wrapperItem}>
                <div className={styles.wrapperItemTitle}>{t('评价内容：')}</div>
                <div className={styles.wrapperItemContent}>
                  <div>{data?.evaluateContent || EMPTY_STR}</div>
                  {data?.evaluatePic?.length > 0 && (
                    <div className="mt-[12px]">
                      <ImageView data={data?.evaluatePic} />
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </Drawer>
    </>
  );
};

export default EvaluateDetail;
