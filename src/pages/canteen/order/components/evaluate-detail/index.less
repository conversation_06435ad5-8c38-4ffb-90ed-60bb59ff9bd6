@base-color: #141737;

.evaluate {
  display: flex;
  padding: 12px;
  margin: 8px 0;
  font-size: 16px;
  color: @base-color;
  background-color: #fff;
  border-radius: 4px;
  align-items: center;
  justify-content: space-between;
  gap: 8px;

  .title {
    font-weight: 500;
    line-height: 24px;
  }

  .content {
    display: flex;
    align-items: center;
    line-height: 1;
  }

  .score {
    margin: 0 12px;
    font-size: 14px;
  }
}

.wrapper {
  display: flex;
  height: 100%;
  overflow: hidden;
  flex-direction: column;
  box-sizing: border-box;

  @supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
    padding-bottom: constant(safe-area-inset-bottom); // 兼容 IOS < 11.2
    padding-bottom: env(safe-area-inset-bottom); // 兼容 IOS >= 11.2
  }

  &Title {
    padding: 12px;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: @base-color;
    text-align: center;
  }

  &Content {
    padding: 12px;
    flex: 1;
    overflow-y: auto;
  }

  &Item {
    display: flex;
    font-size: 14px;
    line-height: 22px;
    color: @base-color;
    align-items: flex-start;

    &:not(:last-child) {
      margin-bottom: 12px;
    }

    &Title {
      color: #999da8;
    }

    &Content {
      flex: 1;
    }
  }
}
