@primary: #141737;

.card {
  margin-bottom: 8px;
}

.header {
  display: flex;
  padding: 12px;
  background: #fff;
  border-radius: 4px;
  align-items: flex-start;

  &.reserved {
    padding-bottom: 0;
    border-radius: 4px 4px 0 0;
  }
}

.statusIcon {
  margin-top: 4px;
  margin-right: 4px;
}

.statusText {
  margin-bottom: 4px;
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  color: @primary;
}

.timeLabel,
.refundText {
  font-size: 14px;
  line-height: 22px;
  color: #999da8;
}

.timeValue {
  margin-right: 8px;
  font-size: 14px;
  line-height: 22px;
  color: @primary;
}

.qrSection {
  padding: 20px 0 36px;
  text-align: center;
  background: #fff;
  border-radius: 0 0 4px 4px;

  .qrTip {
    font-size: 14px;
    line-height: 22px;
    color: #666c7c;
  }

  .qr<PERSON>rapper {
    margin-top: 12px;
  }
}

.qrNotice {
  padding: 10px 0 20px;
  font-size: 14px;
  line-height: 22px;
  color: #f75229;
  text-align: center;
  background: #fff;
}
