.page {
  background: linear-gradient(180deg, #3975c6 10.65%, #f4f5f8 51.48%);
}

.info {
  padding: 12px;
  margin-top: 8px;
  background: #fff;
  border-radius: 4px;
}

.infoItem {
  font-size: 14px;
  line-height: 22px;

  & + & {
    margin-top: 8px;
  }

  &Label {
    color: #999da8;
  }

  &Value {
    margin-left: 2px;
    color: #141737;
  }
}

.payItem {
  display: flex;
  align-items: flex-start;
}
