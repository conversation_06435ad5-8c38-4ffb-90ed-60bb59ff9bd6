.selectOrderPage {
  min-height: 100vh;
  background-color: #f7f8fa;

  .orderList {
    height: calc(100vh - 56px);
    padding-bottom: 30px;

    /* 减去头部和底部的高度 */
    overflow-y: auto;

    .orderItem {
      position: relative;
      display: flex;
      padding: 16px;
      margin-bottom: 12px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
      align-items: center;

      .radioWrapper {
        padding-top: 2px;
        margin-right: 12px;
      }

      .orderContent {
        flex: 1;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .statusTag {
        position: absolute;
        top: 0;
        right: 0;
        padding: 2px 8px;
        font-size: 14px;
        border-radius: 4px;
      }

      .statusTag-0 {
        color: #d9001b;
        background-color: #ec808dcd;
      }

      .statusTag-1 {
        color: #996f37;
        background-color: #fff9e8;
      }

      .statusTag-2 {
        color: #00a85f;
        background-color: #e4fced;
      }

      .content {
        margin-bottom: 12px;
      }

      .completedTag {
        display: inline-block;
        padding: 2px 8px;
        margin-bottom: 8px;
        font-size: 12px;
        color: #00a85f;
        background-color: #e4fced;
        border-radius: 4px;
      }

      .info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 14px;
        color: #646566;
      }

      .infoLabel {
        margin-right: 8px;
        color: #646566;
      }

      .infoValue {
        color: #141737;
        flex: 1;
      }

      .dishes {
        display: flex;
        padding: 8px;
        margin-top: 10px;
        overflow: hidden;
        font-size: 12px;
        color: #141737;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: #f7f8fa;
        border-radius: 4px;
        align-items: center;
        gap: 10px;
        align-self: stretch;
      }

      .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 12px;
        margin-top: 12px;
        border-top: 1px solid #f0f0f0;
      }

      .price {
        font-weight: bold;
        color: #141737;
      }

      .priceUnit {
        margin-right: 2px;
        font-size: 12px;
      }
    }

    .emptyData {
      padding: 40px 0;
      color: #969799;
      text-align: center;
    }

    .loadingMore {
      padding: 16px 0;
      font-size: 14px;
      color: #969799;
      text-align: center;
    }

    .loadMoreBtn {
      padding: 16px 0;
      font-size: 14px;
      color: #197afa;
      text-align: center;

      &:active {
        background-color: #f2f3f5;
      }
    }

    .noMoreData {
      padding: 16px 0;
      font-size: 14px;
      color: #969799;
      text-align: center;
    }
  }

  .footer {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    padding: 12px;
    background-color: #fff;
    box-shadow: 0 -2px 4px rgb(0 0 0 / 5%);

    .cancelButton {
      margin-right: 12px;
      color: #323233;
      background-color: #f7f8fa;
      flex: 1;
    }

    .confirmButton {
      flex: 1;
    }
  }
}
