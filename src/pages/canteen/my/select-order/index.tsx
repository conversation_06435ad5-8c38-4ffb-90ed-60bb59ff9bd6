import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Page } from '@/_/components';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import classnames from 'classnames';
import { Button, Radio, Toast } from 'shineout-mobile';
import { IOrderItem } from '../interfaces';
import { selectOrderPageAPI } from '../services';
import styles from './index.less';

const pageSize = 10;

/**
 * @description 选择关联订单页面
 */
const SelectOrderPage: React.FC = () => {
  usePageTitle(t('选择关联订单'));
  const navigate = useNavigate();

  // 订单列表数据
  const [orderList, setOrderList] = useState<IOrderItem[]>([]);
  // 选中的订单id
  const [selectedOrderId, setSelectedOrderId] = useState<number | undefined>(undefined);
  // 加载状态
  const [loading, setLoading] = useState<boolean>(false);
  // 分页数据
  const [pagination, setPagination] = useState({
    pageNumber: 1,
    pageSize: pageSize,
    hasMore: true,
    total: 0,
  });

  // 获取订单列表数据
  const fetchOrderList = useCallback(
    async (page = 1, size = pageSize, refresh = false) => {
      setLoading(true);
      try {
        const result = await selectOrderPageAPI({
          pageNumber: page,
          pageSize: size,
        });

        if (result) {
          setOrderList((prevList) => {
            const newList = refresh ? result.rows : [...prevList, ...(result.rows || [])];
            return newList;
          });
          // 计算是否还有更多数据
          const currentTotal = refresh ? 0 : orderListLengthRef.current;
          const newTotal = currentTotal + (result.rows?.length || 0);
          // 更新ref中的列表长度
          orderListLengthRef.current = newTotal;
          const hasMore = newTotal < (result.total || 0);

          setPagination({
            pageNumber: page,
            pageSize: size,
            hasMore,
            total: result.total || 0,
          });
        }
      } catch (error) {
        console.error(t('获取订单列表失败'), error);
        Toast.fail(t('获取订单列表失败'));
      } finally {
        setLoading(false);
      }
    },
    [], // 移除对orderList的依赖，使用函数式更新状态
  );

  // 加载更多
  const loadMore = () => {
    if (pagination.hasMore && !loading) {
      fetchOrderList(pagination.pageNumber + 1, pagination.pageSize, false);
    }
  };

  // 选择订单
  const handleSelectOrder = (orderId: number) => {
    setSelectedOrderId(orderId);
  };

  // 确认选择
  const handleConfirm = () => {
    if (!selectedOrderId) {
      Toast.fail(t('请选择一个订单'));
      return;
    }

    // 从sessionStorage获取之前保存的表单数据
    const savedFormData = sessionStorage.getItem('feedbackFormData');
    if (savedFormData) {
      try {
        const parsedData = JSON.parse(savedFormData);
        // 更新订单ID
        const updatedFormData = {
          ...parsedData,
          orderId: selectedOrderId,
        };
        // 保存更新后的表单数据
        sessionStorage.setItem('feedbackFormData', JSON.stringify(updatedFormData));
      } catch (error) {
        console.error('Failed to parse saved form data', error);
      }
    }

    // 返回上一页
    navigate(-1);
  };

  // 组件挂载后加载订单列表
  useEffect(() => {
    // 只在组件首次挂载时加载数据
    fetchOrderList(1, pagination.pageSize, true);

    // 从sessionStorage获取之前选择的订单ID
    const savedFormData = sessionStorage.getItem('feedbackFormData');
    if (savedFormData) {
      try {
        const parsedData = JSON.parse(savedFormData);
        if (parsedData.orderId) {
          setSelectedOrderId(parsedData.orderId);
        }
      } catch (error) {
        console.error('Failed to parse saved form data', error);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 渲染订单列表项
  const renderOrderItem = (record: IOrderItem) => {
    // 2月1日-白班晚餐-定餐(orderDate: yyyy-MM-dd)
    const addTimeMonth = record.orderDate?.split(' ')[0].split('-')[1];
    const addTimeDay = record.orderDate?.split(' ')[0].split('-')[2];
    const title = `${addTimeMonth}${t('月')}${addTimeDay}${t('日')}-${
      record.mealTimePeriodTypeStr
    }-${record.orderTypeStr}`;

    return (
      <div
        key={record.id}
        className={styles.orderItem}
        onClick={() => handleSelectOrder(record.id)}
      >
        <div className={styles.radioWrapper}>
          <Radio checked={selectedOrderId === record.id} />
        </div>
        <div className={styles.orderContent}>
          <div className={styles.header}>
            <div>{title}</div>
            <div
              className={classnames(styles.statusTag, styles[`statusTag-${record.orderStatus}`])}
            >
              {record.orderStatusStr}
            </div>
          </div>
          <div className={styles.content}>
            <div>
              <span className={styles.infoLabel}>
                {record.orderStatus === 1 ? t('预定时间') : t('完成时间')}:{' '}
              </span>
              <span className={styles.infoValue}>
                {record.orderStatus === 1 ? record.addTime : record.finishTime}
              </span>
            </div>
            <div className={styles.info}>
              <div className={styles.infoLabel}>{t('支付金额')}:</div>
              <div className={styles.infoValue}>
                {record.totalAmount ? (
                  <>
                    <span className={styles.priceUnit}>¥</span>
                    {record.totalAmount?.toFixed(2)}
                  </>
                ) : (
                  '--'
                )}
              </div>
            </div>
            <div className={styles.dishes}>
              {record.canteenName}-{record.stallName}-{record.dishNames?.join('、') || ''}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const listRef = useRef<HTMLDivElement>(null);
  const scrollTimerRef = useRef<number | null>(null);
  // 使用ref来跟踪当前列表长度，避免useCallback依赖问题
  const orderListLengthRef = useRef<number>(0);

  // 处理滚动加载更多，添加节流处理
  const handleScroll = () => {
    if (!listRef.current || loading || !pagination.hasMore) return;

    // 如果已经有定时器在执行，则不再重复执行
    if (scrollTimerRef.current !== null) return;

    // 设置节流定时器，100ms内只执行一次
    scrollTimerRef.current = window.setTimeout(() => {
      if (!listRef.current) return;

      const { scrollHeight, scrollTop, clientHeight } = listRef.current;
      const bottomDistance = scrollHeight - scrollTop - clientHeight;

      // console.log(t('滚动检测:'), {
      //   scrollHeight,
      //   scrollTop,
      //   clientHeight,
      //   bottomDistance,
      //   hasMore: pagination.hasMore,
      //   loading,
      // });

      // 当滚动到底部时加载更多，放宽触发条件
      if (bottomDistance <= 100 && !loading && pagination.hasMore) {
        console.log(t('触发加载更多'));
        loadMore();
      }

      // 清除定时器引用
      scrollTimerRef.current = null;
    }, 100);
  };

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (scrollTimerRef.current !== null) {
        clearTimeout(scrollTimerRef.current);
      }
    };
  }, []);

  // 同步orderList长度到ref
  useEffect(() => {
    orderListLengthRef.current = orderList.length;
  }, [orderList.length]);

  return (
    <Page title={t('选择关联订单')} className={styles.selectOrderPage}>
      <div className={styles.orderList} ref={listRef} onScroll={handleScroll}>
        {orderList.length > 0 ? (
          <div>
            {orderList.map((order) => renderOrderItem(order))}
            {loading ? (
              <div className={styles.loadingMore}>{t('加载中...')}</div>
            ) : pagination.hasMore ? (
              <div className={styles.loadMoreBtn} onClick={loadMore}>
                {t('点击加载更多')}
              </div>
            ) : (
              <div className={styles.noMoreData}>{t('没有更多数据了')}</div>
            )}
          </div>
        ) : (
          !loading && <div className={styles.emptyData}>{t('暂无订单记录')}</div>
        )}
      </div>

      <div className={styles.footer}>
        <Button className={styles.cancelButton} onClick={() => navigate(-1)}>
          {t('取消')}
        </Button>
        <Button
          className={styles.confirmButton}
          type="primary"
          disabled={!selectedOrderId}
          onClick={handleConfirm}
        >
          {t('确定')}
        </Button>
      </div>
    </Page>
  );
};

export default SelectOrderPage;
