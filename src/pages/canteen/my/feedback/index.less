.feedbackPage {
  min-height: 100vh;
  padding-bottom: 60px; // 为底部按钮留出空间
  background-color: #f7f8fa;

  .formArea {
    margin-top: 0;
    background-color: #fff;

    // 表单项通用样式
    .formItem {
      display: flex;
      padding: 16px;
      border-bottom: 1px solid #f2f2f2;

      &:active {
        background-color: #f9f9f9;
      }
    }

    // 表单标签
    .formLabel {
      display: flex;
      width: 90px;
      font-size: 14px;
      color: #333;
      align-items: center;

      .requiredMark {
        margin-right: 4px;
        color: #ff4d4f;
      }
    }

    // 表单值区域
    .formValue {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    // 订单选择区域
    .orderSelectValue {
      flex: 1;

      .selectedOrder {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .selectedOrderSpan {
        margin-right: 8px;
      }

      .selectPlaceholder {
        font-size: 14px;
        color: #999;
      }
    }

    // 文本输入区域
    .textareaWrapper {
      width: 100%;
      padding: 0 16px 16px;

      :global(.so-field) {
        padding: 8px;
        border: 1px solid #eee;
        border-radius: 4px;
      }

      .textCount {
        margin-top: 5px;
        font-size: 12px;
        color: #999;
        text-align: right;
      }
    }

    // 上传区域
    .uploadArea {
      display: flex;
      padding: 16px;

      .uploadLabel {
        width: 90px;
        font-size: 14px;
        color: #333;
        align-items: center;
      }

      .uploadContent {
        min-height: 50px;
        flex: 1;
      }
    }
  }

  // 底部按钮组
  .buttonGroup {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    padding: 12px 16px;
    background-color: #fff;
    box-shadow: 0 -2px 4px rgb(0 0 0 / 5%);

    .cancelButton {
      flex: 1;
      margin-right: 12px;
      border-radius: 4px;
    }

    .submitButton {
      flex: 1;
      border-radius: 4px;
    }
  }

  // 适配底部安全区域
  @supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
    .buttonGroup {
      padding-bottom: calc(12px + constant(safe-area-inset-bottom));
      padding-bottom: calc(12px + env(safe-area-inset-bottom));
    }
  }
}
