import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Page } from '@/_/components';
import { usePageTitle } from '@/_/hooks';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import { Button, Field, Icons, Radio, Toast, Upload } from 'shineout-mobile';
import type { IPictureListItem } from '../interfaces';
import { submitSuggestionAPI } from '../services';
import styles from './index.less';

const MAX_CONTENT_LENGTH = 100; // 文本最大长度
const MAX_UPLOAD_IMAGE = 5; // 最大上传图片数量

/**
 * @description 投诉建议页面
 */
const FeedbackPage: React.FC = () => {
  usePageTitle(t('投诉建议'));
  const navigate = useNavigate();

  // 表单数据
  const [formData, setFormData] = useState({
    orderComplaintType: 0, // 0投诉，1建议
    orderId: undefined, // 订单id
    suggestionContent: '', // 建议内容
    pictureList: [] as IPictureListItem[], // 上传图片列表
  });

  // 处理类型切换
  const handleTypeChange = (value: number) => {
    setFormData({
      ...formData,
      orderComplaintType: value,
      // 如果切换为"建议"，清空订单ID
      orderId: value === 1 ? undefined : formData.orderId,
    });
  };

  // 处理内容变更
  const handleContentChange = (e: any) => {
    const value = e?.target?.value;
    if (value?.length <= MAX_CONTENT_LENGTH) {
      setFormData({
        ...formData,
        suggestionContent: value,
      });
    }
  };

  // 选择关联订单
  const handleSelectOrder = () => {
    // 保存当前表单状态到sessionStorage，以便从订单选择页面返回时恢复
    sessionStorage.setItem('feedbackFormData', JSON.stringify(formData));
    navigate('/canteen/my/select-order');
  };

  // 删除已选订单
  const handleRemoveOrder = (e: React.MouseEvent) => {
    e.stopPropagation();
    setFormData({
      ...formData,
      orderId: undefined,
    });
  };

  // 提交表单
  const handleSubmit = () => {
    if (!formData.suggestionContent.trim()) {
      Toast.fail(t('请输入内容'));
      return;
    }

    // 如果选择了投诉类型且没有选择订单
    if (formData.orderComplaintType === 0 && !formData.orderId) {
      Toast.fail(t('请选择关联订单'));
      return;
    }

    // 文本内容长度限制在10-100个字符之间
    if (
      formData.suggestionContent.length < 10 ||
      formData.suggestionContent.length > MAX_CONTENT_LENGTH
    ) {
      Toast.fail(t('内容长度需在{}-{}个字符之间', 10, MAX_CONTENT_LENGTH));
      return;
    }

    // 提交表单
    submitSuggestionAPI(formData)
      .then(() => {
        Toast.success(t('提交成功'));
        // 提交成功后返回上一页
        navigate(-1);
      })
      .catch((error) => {
        Toast.fail(error.message || t('提交失败'));
      });
  };

  // 组件挂载后从sessionStorage恢复表单数据
  React.useEffect(() => {
    const savedFormData = sessionStorage.getItem('feedbackFormData');
    if (savedFormData) {
      try {
        const parsedData = JSON.parse(savedFormData);
        setFormData(parsedData);
        // 清除保存的表单数据
        sessionStorage.removeItem('feedbackFormData');
      } catch (error) {
        console.error('Failed to parse saved form data', error);
      }
    }
  }, []);

  return (
    <Page title={t('投诉建议')} className={styles.feedbackPage}>
      {/* 表单区域 */}
      <div className={styles.formArea}>
        {/* 类型选择 */}
        <div className={styles.formItem}>
          <div className={styles.formLabel}>
            <span className={styles.requiredMark}>*</span>
            <span>{t('类型')}</span>
          </div>
          <div className={styles.formValue}>
            <Radio.Group inline value={formData.orderComplaintType} onChange={handleTypeChange}>
              <Radio value={0}>{t('投诉')}</Radio>
              <Radio value={1}>{t('建议')}</Radio>
            </Radio.Group>
          </div>
        </div>

        {/* 关联订单（投诉时显示） */}
        {formData.orderComplaintType === 0 && (
          <div className={styles.formItem} onClick={handleSelectOrder}>
            <div className={styles.formLabel}>
              <span className={styles.requiredMark}>*</span>
              <span>{t('就餐记录')}</span>
            </div>
            <div className={styles.formValue}>
              <div className={styles.orderSelectValue}>
                {formData.orderId ? (
                  <div className={styles.selectedOrder}>
                    <span className={styles.selectedOrderSpan}>{t('已选择一个订单')}</span>
                    <Icon
                      name="pc-close-circle"
                      color="#999"
                      fontSize={16}
                      onClick={handleRemoveOrder}
                    />
                  </div>
                ) : (
                  <div style={{ display: 'flex', flexDirection: 'column' }}>
                    <span className={styles.selectPlaceholder} style={{ marginBottom: '6px' }}>
                      {t('必填')}
                    </span>
                    <span className={styles.selectPlaceholder}>
                      {t('若指向具体柜口请务必正确选择')}
                    </span>
                  </div>
                )}
              </div>
              <Icon name="pc-arrow-right" color="#C8C9CC" fontSize={16} />
            </div>
          </div>
        )}

        {/* 内容输入区域 */}
        <div className={styles.formItem}>
          <div className={styles.formLabel}>
            <span className={styles.requiredMark}>*</span>
            <span>{formData.orderComplaintType === 0 ? t('投诉内容') : t('建议内容')}</span>
          </div>
          <div className={styles.textareaWrapper}>
            <Field
              type="textarea"
              placeholder={t('请输入')}
              rows={4}
              value={formData.suggestionContent}
              onChange={handleContentChange}
              maxLength={MAX_CONTENT_LENGTH}
            />
            <div className={styles.textCount}>
              {formData.suggestionContent.length}/{MAX_CONTENT_LENGTH}
            </div>
          </div>
        </div>

        {/* 图片上传区域 */}
        <div className={styles.uploadArea}>
          <div className={styles.uploadLabel}>
            <span>{t('上传图片')}</span>
          </div>
          <div className={styles.uploadContent}>
            <Upload
              icon={<Icons style={{ color: '#CCCFD7', fontSize: 20 }} name="add" />}
              name="fileList"
              action="/attachmentFile/uploadImage"
              accept="image/jpg,image/jpeg,image/png"
              limit={MAX_UPLOAD_IMAGE}
              multiple
              validator={{
                size: (size) => size > 10 * 1024 * 1024 && new Error(t('图片必须小于10MB')),
                custom: (file) => {
                  const { size, type } = file;
                  if (!/image\/(jpe?g|png|gif|bmp)/.test(type)) {
                    return new Error(t('只能上传jpg、jpeg、png、gif、bmp格式的图片'));
                  }
                  if (size > 10 * 1024 * 1024) {
                    return new Error(t('图片必须小于10MB'));
                  }
                  return undefined;
                },
              }}
              renderItem="fileUrl"
              value={formData.pictureList}
              onChange={(list) =>
                setFormData({
                  ...formData,
                  pictureList: list.filter(Boolean),
                })
              }
              onSuccess={(responseJson) => {
                try {
                  const res = JSON.parse(responseJson);
                  if (!res.data) {
                    Toast.fail(res.msg);
                    return;
                  }
                  return res.data;
                } catch {
                  Toast.fail(t('上传文件出错'));
                }
              }}
            />
          </div>
        </div>
      </div>

      {/* 底部按钮区域 */}
      <div className={styles.buttonGroup}>
        <Button className={styles.cancelButton} onClick={() => navigate(-1)}>
          {t('取消')}
        </Button>
        <Button className={styles.submitButton} type="primary" onClick={handleSubmit}>
          {formData.orderComplaintType === 0 ? t('确认提交') : t('确认提交')}
        </Button>
      </div>
    </Page>
  );
};

export default FeedbackPage;
