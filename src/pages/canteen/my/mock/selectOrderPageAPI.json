{"total": 10, "rows": [{"id": 1001, "orderStatus": 2, "addTime": "2023-10-01 12:30:45", "mealTimePeriodTypeStr": "午餐", "canteenName": "总部食堂", "stallName": "川菜档口", "dishNames": ["麻婆豆腐", "宫保鸡丁", "米饭"], "totalAmount": 2500}, {"id": 1002, "orderStatus": 2, "addTime": "2023-10-02 18:15:30", "mealTimePeriodTypeStr": "晚餐", "canteenName": "总部食堂", "stallName": "粤菜档口", "dishNames": ["白切鸡", "清蒸鱼", "米饭"], "totalAmount": 3000}, {"id": 1003, "orderStatus": 1, "addTime": "2023-10-03 11:45:20", "mealTimePeriodTypeStr": "午餐", "canteenName": "研发大楼食堂", "stallName": "湘菜档口", "dishNames": ["剁椒鱼头", "小炒肉", "米饭"], "totalAmount": 3500}, {"id": 1004, "orderStatus": 3, "addTime": "2023-10-04 12:10:15", "mealTimePeriodTypeStr": "午餐", "canteenName": "总部食堂", "stallName": "西餐档口", "dishNames": ["牛排", "沙拉", "面包"], "totalAmount": 4500}, {"id": 1005, "orderStatus": 2, "addTime": "2023-10-05 18:30:00", "mealTimePeriodTypeStr": "晚餐", "canteenName": "研发大楼食堂", "stallName": "自选档口", "dishNames": ["红烧肉", "炒青菜", "米饭"], "totalAmount": 2800}, {"id": 1006, "orderStatus": 4, "addTime": "2023-10-06 12:20:10", "mealTimePeriodTypeStr": "午餐", "canteenName": "总部食堂", "stallName": "川菜档口", "dishNames": ["水煮鱼", "回锅肉", "米饭"], "totalAmount": 3200}, {"id": 1007, "orderStatus": 2, "addTime": "2023-10-07 18:05:25", "mealTimePeriodTypeStr": "晚餐", "canteenName": "研发大楼食堂", "stallName": "粤菜档口", "dishNames": ["烧鹅", "蒸虾", "米饭"], "totalAmount": 4000}, {"id": 1008, "orderStatus": 1, "addTime": "2023-10-08 12:15:30", "mealTimePeriodTypeStr": "午餐", "canteenName": "总部食堂", "stallName": "湘菜档口", "dishNames": ["辣子鸡", "腊肉炒笋", "米饭"], "totalAmount": 2700}, {"id": 1009, "orderStatus": 2, "addTime": "2023-10-09 18:25:40", "mealTimePeriodTypeStr": "晚餐", "canteenName": "研发大楼食堂", "stallName": "西餐档口", "dishNames": ["意面", "沙拉", "面包"], "totalAmount": 3800}, {"id": 1010, "orderStatus": 0, "addTime": "2023-10-10 11:55:15", "mealTimePeriodTypeStr": "午餐", "canteenName": "总部食堂", "stallName": "自选档口", "dishNames": ["糖醋排骨", "炒青菜", "米饭"], "totalAmount": 3100}]}