import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '@/_/components/Icon';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { Switch, Toast } from 'shineout-mobile';
import styles from './index.less';
import { type IMyInfoData } from './interfaces';
import { getMyInfoAPI, getMySwitchRemindAPI } from './services';

/**
 * @description 定餐系统-我的页面
 * @returns {React.ReactElement}
 */
const MyPage: React.FC = () => {
  usePageTitle(t('个人信息'));
  const navigate = useNavigate();
  const [userInfo, setUserInfo] = useState<IMyInfoData>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [remindSwitch, setRemindSwitch] = useState<boolean>(false);

  // 获取个人信息
  const fetchUserInfo = async () => {
    try {
      setLoading(true);
      const data = await getMyInfoAPI();
      setUserInfo(data);
      setRemindSwitch(data.remind || false);
    } catch (error) {
      console.error(t('获取个人信息失败'), error);
      Toast.fail(t('获取个人信息失败'));
    } finally {
      setLoading(false);
    }
  };

  // 切换定餐提醒
  const handleSwitchRemind = async (checked: boolean) => {
    try {
      setRemindSwitch(checked);
      await getMySwitchRemindAPI({ remind: checked }).then((res) => {
        Toast.success({ content: res ? t('已开启定餐提醒') : t('已关闭定餐提醒') });
      });
    } catch (error) {
      console.error(t('切换定餐提醒失败'), error);
      setRemindSwitch(!checked); // 切换失败，恢复原状态
      Toast.fail(t('切换定餐提醒失败'));
    }
  };

  // 跳转到投诉建议页面
  const goToFeedback = () => {
    navigate('/canteen/my/feedback');
  };

  useEffect(() => {
    fetchUserInfo();
  }, []);

  console.log(`remindSwitch`, remindSwitch);

  return (
    <div className={styles.myPage}>
      {/* 用户信息区域 */}
      <div className={styles.userInfoCard}>
        <div className={styles.userAvatar}>
          {userInfo.avatar ? (
            <img src={userInfo.avatar} alt="avatar" />
          ) : (
            <div className={styles.defaultAvatar}>{userInfo.staffName?.substring(0, 1) || ''}</div>
          )}
        </div>
        <div className={styles.userInfo}>
          <div className={styles.userName}>{userInfo.staffName || 'Bill Liu'}</div>
          <div className={styles.workNum}>
            {t('工号')}: {userInfo.workNum || '1028383832'}
          </div>
        </div>
      </div>

      {/* 功能区域 */}
      <div className={styles.functionArea}>
        {/* 投诉建议 */}
        <div className={styles.menuItem} onClick={goToFeedback}>
          <div className={styles.menuLeft}>
            <Icon name="pc-feedback" fontSize={20} color="#666" />
            <span className={styles.menuLeftLabel}>{t('投诉建议')}</span>
          </div>
          <div className={styles.menuRight}>
            <Icon name="pc-arrow-right" fontSize={16} color="#C8C9CC" />
          </div>
        </div>

        {/* 定餐提醒 */}
        <div className={styles.menuItem} style={{ borderBottom: 'none' }}>
          <div className={styles.menuLeft}>
            <Icon name="pc-notification" fontSize={20} color="#666" />
            <span className={styles.menuLeftLabel}>{t('定餐提醒')}</span>
          </div>
          <div className={styles.menuRight}>
            <Switch
              disabled={loading}
              value={remindSwitch}
              onChange={handleSwitchRemind}
              className={styles.switchBtn}
            />
          </div>
        </div>

        {/* 定餐提醒说明 */}
        <div className={styles.remindDesc}>
          {t('开启后，系统将会在每个餐段截止定餐前，推送定餐提醒')}
        </div>
      </div>
    </div>
  );
};

export default MyPage;
