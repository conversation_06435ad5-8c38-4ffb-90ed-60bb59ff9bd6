interface IMyInfoData {
  /** 员工名 */
  staffName?: string;
  /** 头像 */
  avatar?: string;
  /** 工号 */
  workNum?: string;
  /** 是否开启了提醒 */
  remind?: boolean;
}

interface IPictureListItem {
  /** 文件链接	 */
  fileUrl?: string;
  /** 文件名称	 */
  fileName?: string;
  /** ossKey */
  ossKey?: string;
}

/**
 * 提交投诉建议参数
 */
export interface ISubmitSuggestionRequestBody {
  /** 投诉类型 0投诉，1建议 */
  orderComplaintType: number;
  /** 订单id */
  orderId?: number;
  /** 建议内容 */
  suggestionContent: string;
  /** 上传图片 */
  pictureList?: IPictureListItem[];
}

/**
 * 查询关联订单记录列表参数
 */
interface ISelectOrderPageRequestBody {
  /** 页码 */
  pageNumber?: number;
  /** 每页数量 */
  pageSize?: number;
}

/**
 * 订单项数据结构
 */
export interface IOrderItem {
  /** 订单id */
  id?: number;
  /** 预定时间 */
  addTime?: string;
  /** yyyy-MM-dd */
  orderDate?: string;
  /** 订单类型 0定餐，1现场点餐 */
  orderType?: number;
  /** 订单类型描述 0定餐，1现场点餐 */
  orderTypeStr?: string;
  /** 餐段类型 0午餐，1白班晚餐，2晚班晚餐，3宵夜 */
  mealTimePeriodType?: number;
  /** 餐段类型描述 0午餐，1白班晚餐，2晚班晚餐，3宵夜 */
  mealTimePeriodTypeStr?: string;
  /** 订单状态 0待支付，1已预定，2已完成，3已取消，4已退款 */
  orderStatus?: number;
  /** 订单状态描述 0待支付，1已预定，2已完成，3已取消，4已退款 */
  orderStatusStr?: string;
  /** 食堂id */
  canteenId?: number;
  /** 食堂名称 */
  canteenName?: string;
  /** 档口id */
  stallId?: number;
  /** 档口名称 */
  stallName?: string;
  /** 菜品名称列表 */
  dishNames?: string[];
  /** 支付金额 */
  totalAmount?: number;
  /** 完成时间 */
  finishTime?: string;
}

/**
 * 订单记录列表响应
 */
interface IOrderPageResponse {
  /** 总记录数 */
  total?: number;
  /** 当前记录集合 */
  rows?: IOrderItem[];
}

export type { IMyInfoData, IOrderPageResponse, IPictureListItem, ISelectOrderPageRequestBody };
