import { get, post } from '@/utils/fetch';
import {
  IMyInfoData,
  IOrderPageResponse,
  ISelectOrderPageRequestBody,
  ISubmitSuggestionRequestBody,
} from './interfaces';
// // 导入mock数据
// import mockSelectOrderPageData from './mock/selectOrderPageAPI.json';

/**
 * 定餐首页-我的-个人信息
 * https://soapi.sheincorp.cn/application/3694/routes/244339/doc
 * @returns
 */
export const getMyInfoAPI = () => {
  return get<IMyInfoData>('/canteen/staff/my/info');
};

/**
 * 定餐首页-我的-切换定餐提醒
 * https://soapi.sheincorp.cn/application/3694/routes/244338/doc
 * @returns
 */
export const getMySwitchRemindAPI = (data: { remind?: boolean }) => {
  return post<boolean>('/canteen/staff/my/switchRemind', data);
};

/**
 * 定餐首页-我的-投诉建议
 * https://soapi.sheincorp.cn/application/3694/routes/244760/doc
 * @returns
 */
export const submitSuggestionAPI = (data: ISubmitSuggestionRequestBody) => {
  return post<boolean>('/canteen/staff/my/submitSuggestion', data);
};

/**
 * 定餐首页-我的-查询关联订单记录列表
 * https://soapi.sheincorp.cn/application/3694/routes/243653/doc
 * @returns 订单列表和分页信息
 */
export const selectOrderPageAPI = (data: ISelectOrderPageRequestBody) => {
  // // 开发环境使用mock数据
  // if (process.env.NODE_ENV === 'development') {
  //   return Promise.resolve(mockSelectOrderPageData);
  // }
  return post<IOrderPageResponse>('/canteen/staff/my/selectOrderPage', data);
};
