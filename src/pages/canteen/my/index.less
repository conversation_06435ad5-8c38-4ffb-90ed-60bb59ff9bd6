.myPage {
  min-height: 100vh;
  padding-bottom: 20px;
  background-color: #f5f5f5;

  // 顶部导航栏
  .header {
    position: relative;
    display: flex;
    height: 44px;
    padding: 0 16px;
    color: #fff;
    background-color: #1976d2;
    align-items: center;
    justify-content: space-between;

    .headerLeft {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 4px;
    }

    .headerTitle {
      position: absolute;
      top: 50%;
      left: 50%;
      font-size: 17px;
      font-weight: 500;
      transform: translate(-50%, -50%);
    }

    .headerRight {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 4px;
    }
  }

  // 用户信息卡片
  .userInfoCard {
    position: relative;
    display: flex;
    padding: 16px;
    margin-top: 12px;
    margin-bottom: 12px;
    background-color: #fff;
    align-items: center;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;

    .userAvatar {
      width: 50px;
      height: 50px;
      margin-right: 12px;
      overflow: hidden;
      border: 1px solid #eee;
      border-radius: 50%;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .defaultAvatar {
        display: flex;
        width: 100%;
        height: 100%;
        font-size: 20px;
        font-weight: bold;
        color: #fff;
        background-color: #1890ff;
        align-items: center;
        justify-content: center;
      }
    }

    .userInfo {
      flex: 1;

      .userName {
        margin-bottom: 4px;
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .workNum {
        font-size: 14px;
        color: #666;
      }
    }
  }

  // 功能区域
  .functionArea {
    background-color: #fff;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;

    // 菜单项
    .menuItem {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;

      &:active {
        background-color: #f9f9f9;
      }

      .menuLeft {
        display: flex;
        align-items: center;
      }

      .menuLeftLabel {
        margin-left: 10px;
        font-size: 15px;
        color: #333;
      }

      .menuRight {
        display: flex;
        align-items: center;
      }
    }

    // 提醒说明
    .remindDesc {
      padding: 0 66px 16px 45px;
      font-size: 13px;
      line-height: 1.5;
      color: #999;
    }

    // 开关按钮
    .switchBtn {
      :global(.so-switch) {
        height: 24px;
        min-width: 44px;
      }
    }
  }
}
