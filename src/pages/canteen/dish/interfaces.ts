import { IDishListItem } from '../_/interfaces';
export interface IGetDishListParams {
  /** 档口id */
  stallId: number;
  /** 餐段类型code */
  mealTimePeriodTypeCode: number;
}

export interface IDishListResponse {
  listVos: IDishListItem[];
  /** 食堂名称 */
  canteenName?: string;
  /** 餐段类型名称 */
  mealTimePeriodTypeName?: string;
}

interface IDishInfoItem {
  /** 菜品id */
  dishId: number;
  /** 菜品数量 */
  dishAmount: number;
  /** 菜品金额 */
  dishPrice: number;
}

export interface IDishOrderAddRequestBody {
  /** 档口id */
  stallId: number;
  /** 定餐日期 yyyy-dd-MM */
  orderDate: string;
  /** 餐段类型 0午餐，1白班晚餐，2晚班晚餐，3宵夜 */
  mealTimePeriodType: number;
  /** 菜品明细 */
  dishInfo: IDishInfoItem[];
}

export interface IDishOrderAddResponse {
  /** 订单id */
  id?: number;
  /** 订单号 */
  orderNo?: string;
  /** 订单总金额 */
  totalAmount?: number;
  /** 钱包余额 */
  walletBalance?: number;
}
