import { post } from '@/utils';
import {
  IDishListResponse,
  IDishOrderAddRequestBody,
  IDishOrderAddResponse,
  IGetDishListParams,
} from './interfaces';

/**
 * @description 获取菜品列表
 * @param {IGetDishListParams} data
 * @returns {Promise<IDishListResponse>}
 */
export const getDishList = (data: IGetDishListParams): Promise<IDishListResponse> => {
  return post('/canteen/staff/home/<USER>', data);
};

/**
 * @description 添加订单
 * @param {IDishOrderAddRequestBody} data
 * @returns {Promise<IDishOrderAddResponse>}
 */
export const addDishOrder = (data: IDishOrderAddRequestBody): Promise<IDishOrderAddResponse> => {
  return post('/canteen/staff/order/add', data);
};
