.page {
  height: 100vh;
  overflow: auto;
  background-color: #f5f6f7;
  box-sizing: border-box;
}

.header {
  padding: 12px;
  background-color: #fff;

  .title {
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #141737;
  }

  .info {
    margin-top: 6px;
    color: #999da8;

    &Value {
      margin-left: 4px;
      font-size: 14px;
      line-height: 22px;
      color: #666c7c;
    }
  }
}

.stall {
  display: flex;
  padding: 12px;
  margin-top: 8px;
  background-color: #f7f8fa;
  border-radius: 4px;
  align-items: flex-start;

  &Image {
    width: 54px;
    height: 54px;
    overflow: hidden;
    background-color: #d9d9d9;
    border-radius: 4px;
  }

  &Content {
    flex: 1;
    margin-left: 16px;
  }

  &Title {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #141737;
  }

  &Desc {
    margin-top: 4px;
    overflow: hidden;
    font-size: 14px;
    line-height: 22px;
    color: #666c7c;
  }
}

.main {
  padding: 12px;
}

.list {
  padding: 12px;
  background-color: #fff;
  border-radius: 4px;
}

.footer {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1060;
  background-color: #fff;

  @supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
    & {
      padding-bottom: constant(safe-area-inset-bottom); // 兼容 IOS < 11.2
      padding-bottom: env(safe-area-inset-bottom); // 兼容 IOS < 11.2
    }
  }
}
