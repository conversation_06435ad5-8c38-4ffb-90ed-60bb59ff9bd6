import { useMemo, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '@/_/components/Icon';
import { usePageTitle } from '@/_/hooks';
import { canteenStore, initCartInfoCache } from '@/_/lego-stores/canteenStore';
import { EMPTY_STR } from '@/share';
import { t } from '@shein-bbl/react';
import { useMount, useRequest } from 'ahooks';
import { Loading } from 'shineout-mobile';
import { IDishListItemWithAmount } from '../_/interfaces';
import { formatDishDate } from '../_/utils';
import { getDishList } from './api';
import CartBar from './components/car-bar';
import DishCard from './components/dish-card';
import styles from './index.less';

const DishPage = () => {
  canteenStore.useMount();
  const { currentStall, cartInfo } = canteenStore.useState();
  const navigate = useNavigate();
  usePageTitle(t('选择菜品'));

  const [footerHeight, setFooterHeight] = useState(64);
  const footerRef = useRef<HTMLDivElement>(null);

  useMount(() => {
    // 没有当前档口信息，可能用户刷新页面之类的，重新跳转到首页
    if (!currentStall) {
      navigate('/canteen/home', { replace: true });
    } else {
      setFooterHeight(footerRef.current?.offsetHeight || 64);
    }
  });

  const { data: dishList, loading } = useRequest(getDishList, {
    manual: !currentStall,
    defaultParams: [
      {
        stallId: currentStall?.id,
        mealTimePeriodTypeCode: currentStall?.mealTimePeriodTypeCode,
      },
    ],
    onSuccess: (res) => {
      initCartInfoCache(res?.listVos || []);
    },
  });

  const dishMap = useMemo(() => {
    return (
      cartInfo?.dishList?.reduce((acc, item) => {
        acc[item.id] = item;
        return acc;
      }, {} as Record<number, IDishListItemWithAmount>) || {}
    );
  }, [cartInfo]);

  if (!currentStall) {
    return null;
  }
  const { canteenName, mealTimePeriodTypeName, date, stallPic, stallName, areaLocation } =
    currentStall;

  const { listVos } = dishList || {};

  return (
    <div className={styles.page} style={{ paddingBottom: footerHeight }}>
      <div className={styles.header}>
        <div className={styles.title}>{mealTimePeriodTypeName}</div>
        <div className={styles.info}>
          <Icon name="m-location-shineout-fill" fontSize={16} />
          <span className={styles.infoValue}>{canteenName}</span>
        </div>
        <div className={styles.info}>
          <Icon name="m-schedule-shineout-fill" fontSize={16} />
          <span className={styles.infoValue}>{formatDishDate(date)}</span>
        </div>
        <div className={styles.stall}>
          <div className={styles.stallImage}>
            <img src={stallPic} alt={stallName} className="w-full h-full object-cover" />
          </div>
          <div className={styles.stallContent}>
            <div className={styles.stallTitle}>{stallName}</div>
            <div className={styles.stallDesc}>{areaLocation || EMPTY_STR}</div>
          </div>
        </div>
      </div>
      <div className={styles.main}>
        {loading ? (
          <div className="text-center">
            <Loading />
          </div>
        ) : (
          <div className={styles.list}>
            {listVos?.map((item) => (
              <DishCard key={item.id} data={item} buyCount={dishMap[item.id]?.buyCount || 0} />
            ))}
          </div>
        )}
      </div>
      <div className={styles.footer} ref={footerRef}>
        <CartBar footerHeight={footerHeight} />
      </div>
    </div>
  );
};

export default DishPage;
