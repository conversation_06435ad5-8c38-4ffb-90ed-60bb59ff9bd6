.cartBar {
  position: relative;
  display: flex;
  padding: 12px;
  background: #fff;
  align-items: center;
  justify-content: space-between;
}

.cartLeft {
  position: relative;
  display: flex;
  align-items: center;

  .cartIcon {
    position: relative;
    padding: 8px;
    color: #94cdff;
    background-color: #e9f5fe;
    border-radius: 4px;
  }

  .cartIconActive {
    color: #197afa;
  }

  .badge {
    position: absolute;
    top: -4px;
    right: -4px;
    min-width: 16px;
    font-size: 12px;
    line-height: 16px;
    color: #fff;
    text-align: center;
    background: #eb4242;
    border-radius: 50%;
  }
}

.totalPriceWrapper {
  margin-left: 7px;
  font-size: 12px;
  line-height: 20px;
  color: #666c7c;

  &Active {
    color: #eb4242;
  }

  .totalPrefix {
    margin-right: 2px;
    margin-left: 4px;
    font-weight: 500;
  }

  .totalPrice {
    font-size: 20px;
    font-weight: 600;
    line-height: 24px;
  }
}

.cartDrawer {
  overflow: hidden;
}

.cartWrapper {
  display: flex;
  height: 100%;
  overflow: hidden;
  flex-direction: column;
  box-sizing: border-box;

  .cartTitle {
    padding: 12px;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #141737;
    text-align: center;
  }

  .cartList {
    padding: 12px;
    flex: 1;
    overflow-y: auto;
  }
}
