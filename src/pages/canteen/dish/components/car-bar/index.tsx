import React, { useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '@/_/components/Icon';
import { canteenStore } from '@/_/lego-stores/canteenStore';
import { t } from '@shein-bbl/react';
import { useRequest, useUpdateEffect } from 'ahooks';
import classNames from 'classnames';
import numeral from 'numeral';
import { Button, Drawer } from 'shineout-mobile';
import { addDishOrder } from '../../api';
import DishCard from '../dish-card';
import styles from './index.less';

/**
 * @description 购物车底部栏
 * @returns {React.ReactElement} 购物车底部栏
 */
const CartBar = ({ footerHeight }: { footerHeight: number }): React.ReactElement => {
  const { cartInfo, currentStall } = canteenStore.useState();

  const { dishList } = cartInfo || {};
  const { totalCount, totalPrice } = useMemo(() => {
    const totalCount =
      dishList?.reduce((acc, curr) => numeral(acc).add(curr.buyCount).value(), 0) || 0;
    const totalPrice =
      dishList?.reduce((acc, curr) => {
        const price =
          numeral(curr.buyCount || 0)
            .multiply(curr.dishPrice || 0)
            .value() || 0;
        return numeral(acc).add(price).value();
      }, 0) || 0;
    return { totalCount, totalPrice };
  }, [dishList]);

  const isEmpty = totalCount === 0;

  const [visible, setVisible] = useState(false);
  const navigate = useNavigate();

  const { run: addDishOrderRequest, loading: addDishOrderLoading } = useRequest(addDishOrder, {
    manual: true,
    onSuccess: (res) => {
      navigate(`/canteen/pay/${res.id}`);
    },
  });

  /**
   * @description 去结算
   * @param {MouseEvent} e 事件
   * @returns {void}
   */
  const handleCheckout = (e: MouseEvent): void => {
    e.stopPropagation();
    if (addDishOrderLoading) {
      return;
    }
    addDishOrderRequest({
      stallId: currentStall?.id,
      orderDate: currentStall?.date,
      mealTimePeriodType: currentStall?.mealTimePeriodTypeCode,
      dishInfo: dishList?.map((dish) => ({
        dishId: dish.id,
        dishAmount: dish.buyCount,
        dishPrice: dish.dishPrice,
      })),
    });
  };

  /**
   * @description 购物车浮层点击
   * @returns {void}
   */
  const handleCartBarClick = (): void => {
    if (isEmpty || visible) {
      return;
    }
    setVisible(true);
  };

  // 全部菜品减为0时，关闭购物车浮层
  useUpdateEffect(() => {
    if (isEmpty && visible) {
      setVisible(false);
    }
  }, [isEmpty]);

  return (
    <>
      <div className={styles.cartBar} onClick={handleCartBarClick}>
        <div className={styles.cartLeft}>
          <div className={classNames(styles.cartIcon, !isEmpty && styles.cartIconActive)}>
            <Icon name="mes-menu-shopping-cart" fontSize={22} />
            {totalCount > 0 && <span className={styles.badge}>{totalCount}</span>}
          </div>
          <div
            className={classNames(
              styles.totalPriceWrapper,
              !isEmpty && styles.totalPriceWrapperActive,
            )}
          >
            <span>{t('合计')}</span>
            <span className={styles.totalPrefix}>¥</span>
            <span className={styles.totalPrice}>{totalPrice}</span>
          </div>
        </div>
        <Button type="primary" disabled={isEmpty || addDishOrderLoading} onClick={handleCheckout}>
          {t('去结算')}
        </Button>
      </div>
      <Drawer
        round
        closeable
        position="bottom"
        drawerClass={styles.cartDrawer}
        height="60vh"
        visible={visible}
        onClose={() => setVisible(false)}
      >
        <div className={styles.cartWrapper} style={{ paddingBottom: footerHeight }}>
          <div className={styles.cartTitle}>{t('购物车')}</div>
          <div className={styles.cartList}>
            {dishList?.map((dish) => (
              <DishCard key={dish.id} data={dish} buyCount={dish.buyCount} />
            ))}
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default CartBar;
