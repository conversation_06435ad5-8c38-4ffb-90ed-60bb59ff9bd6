import React from 'react';
import Icon from '@/_/components/Icon';
import { changeCartInfo } from '@/_/lego-stores/canteenStore';
import { IDishListItem } from '@/pages/canteen/_/interfaces';
import { formatNumber } from '@/utils';
import { t } from '@shein-bbl/react';
import { Lazyload, Stepper } from 'shineout-mobile';
import styles from './index.less';

interface IDishCardProps {
  data: IDishListItem;
  buyCount: number;
}

/**
 * @description 菜品卡片
 * @param {IDishCardProps} data
 * @returns {React.ReactElement}
 */
const DishCard = ({ data, buyCount }: IDishCardProps): React.ReactElement => {
  return (
    <div className={styles.card}>
      <Lazyload>
        <div className={styles.image}>
          <img src={data.dishPic} alt={data.dishName} className="w-full h-full object-cover" />
        </div>
      </Lazyload>
      <div className={styles.content}>
        <div className={styles.title}>{data.dishName}</div>
        <div className={styles.sales}>
          <span style={{ marginRight: 6 }}>{t('月销')}</span>
          <span>{formatNumber(data.salesAmount, '0,0')}</span>
        </div>
        <div className={styles.footer}>
          <div className={styles.price}>
            <span>¥</span>
            <span className={styles.priceVal}>{data.dishPrice}</span>
          </div>
          {buyCount > 0 ? (
            <Stepper
              theme="rect"
              min={0}
              value={buyCount}
              inputWidth={50}
              onChange={(value) => {
                changeCartInfo({
                  ...data,
                  buyCount: value,
                });
              }}
            />
          ) : (
            <button
              className={styles.addButton}
              onClick={() => {
                changeCartInfo({
                  ...data,
                  buyCount: 1,
                });
              }}
            >
              <Icon name="m-add-big-shineout" fontSize={16} />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DishCard;
