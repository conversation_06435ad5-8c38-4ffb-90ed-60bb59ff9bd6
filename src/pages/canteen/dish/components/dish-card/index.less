.card {
  display: flex;
  align-items: flex-start;

  & + & {
    margin-top: 12px;
  }
}

.image {
  width: 76px;
  height: 76px;
  overflow: hidden;
  background-color: #d9d9d9;
  border-radius: 4px;
}

.content {
  flex: 1;
  margin-left: 12px;
}

.title {
  display: box;
  overflow: hidden;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: #141737;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.sales {
  margin-top: 4px;
  font-size: 14px;
  line-height: 22px;
  color: #666c7c;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 4px;

  .price {
    font-size: 12px;
    line-height: 20px;
    color: #eb4242;

    &Val {
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
    }
  }

  :global {
    .sm-stepper {
      .sm-stepper-minus,
      .sm-stepper-plus {
        border-color: #e8ebf0;
        color: #666c7c;
      }

      .sm-stepper-input {
        color: #141737;
        border-color: #e8ebf0;
      }
    }
  }
}

.addButton {
  padding: 5px;
  line-height: 1;
  color: #fff;
  background: #197afa;
  border: none;
  border-radius: 4px;
}
