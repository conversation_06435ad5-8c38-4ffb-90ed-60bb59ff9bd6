import { useLocation, useNavigate } from 'react-router-dom';
import Icon from '@/_/components/Icon';
import { usePageTitle } from '@/_/hooks';
import { canteenStore, changeCurrentStall } from '@/_/lego-stores/canteenStore';
import { queryStringParse } from '@/utils/share';
import { t } from '@shein-bbl/react';
import { useRequest } from 'ahooks';
import { Loading } from 'shineout-mobile';
import { formatDishDate } from '../_/utils';
import { getStallList } from './api';
import StallCard from './components/stall-card';
import styles from './index.less';
import { IStallListItem, IStallListResponse } from './interfaces';

/**
 * @description 档口页面
 * @returns {React.ReactElement} 档口页面
 */
const StallPage = (): React.ReactElement => {
  const location = useLocation();
  const { canteenId, mealTimePeriodTypeCode, date } = queryStringParse(location.search) as {
    canteenId: string;
    mealTimePeriodTypeCode: string;
    date: string;
  };

  canteenStore.useMount();

  usePageTitle(t('选择档口'));

  /**
   * @description 跳转菜品
   * @param {IStallListItem} data 档口信息
   * @param {IStallListResponse} info 档口列表信息
   * @param {boolean} replace 是否替换
   * @returns {void}
   */
  const handleJumpDish = (
    data: IStallListItem,
    info: IStallListResponse,
    replace?: boolean,
  ): void => {
    changeCurrentStall({
      ...data,
      mealTimePeriodTypeCode: Number(mealTimePeriodTypeCode),
      date,
      canteenId: Number(canteenId),
      canteenName: info.canteenName,
      mealTimePeriodTypeName: info.mealTimePeriodTypeName,
    });
    navigate('/canteen/dish', { replace });
  };

  const { data: stallInfo, loading } = useRequest(getStallList, {
    defaultParams: [
      {
        canteenId: Number(canteenId),
        mealTimePeriodTypeCode: Number(mealTimePeriodTypeCode),
        date,
      },
    ],
    onSuccess: (res) => {
      // 如有该食堂只有一个档口时，则直接选择菜品
      if (res?.stallList?.length === 1) {
        handleJumpDish(res.stallList[0], res, true);
      }
    },
  });

  const navigate = useNavigate();

  if (loading || !stallInfo) {
    return (
      <div className="h-screen flex flex-col justify-center items-center">
        <Loading />
      </div>
    );
  }

  const { canteenName, mealTimePeriodTypeName, stallList } = stallInfo;

  /**
   * @description 点击档口
   * @param {IStallListItem} data 档口信息
   * @returns {void}
   */
  const handleClickCard = (data: IStallListItem): void => {
    handleJumpDish(data, stallInfo);
  };

  return (
    <div className="min-h-screen bg-[#F5F6F7]">
      <div className={styles.header}>
        <div className={styles.title}>{mealTimePeriodTypeName}</div>
        <div className={styles.info}>
          <Icon name="m-location-shineout-fill" fontSize={16} />
          <span className={styles.infoValue}>{canteenName}</span>
        </div>
        <div className={styles.info}>
          <Icon name="m-schedule-shineout-fill" fontSize={16} />
          <span className={styles.infoValue}>{formatDishDate(date)}</span>
        </div>
      </div>
      <div className={styles.list}>
        {stallList?.map((item) => (
          <StallCard key={item.id} data={item} handleClick={handleClickCard} />
        ))}
      </div>
    </div>
  );
};

export default StallPage;
