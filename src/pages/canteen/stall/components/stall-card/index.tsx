import React from 'react';
import Icon from '@/_/components/Icon';
import { EMPTY_STR } from '@/share';
import { Lazyload } from 'shineout-mobile';
import { IStallListItem } from '../../interfaces';
import styles from './index.less';
interface IStallCardProps {
  data: IStallListItem;
  handleClick: (data: IStallListItem) => void;
}

/**
 * @description 档口卡片
 * @param {IStallCardProps} data
 * @returns {React.ReactElement}
 */
const StallCard = ({ data, handleClick }: IStallCardProps): React.ReactElement => {
  return (
    <div className={styles.card} onClick={() => handleClick(data)}>
      <Lazyload>
        <div className={styles.image}>
          <img src={data.stallPic} alt={data.stallName} className="w-full h-full object-cover" />
        </div>
      </Lazyload>
      <div className={styles.content}>
        <div className={styles.title}>{data.stallName}</div>
        <div className={styles.desc}>{data.areaLocation || EMPTY_STR}</div>
      </div>
      <div className={styles.arrow}>
        <Icon name="arrow-right" fontSize={14} />
      </div>
    </div>
  );
};

export default StallCard;
