export interface IStallListItem {
  /** 档口id */
  id?: number;
  /** 档口名称 */
  stallName?: string;
  /** 区域位置 */
  areaLocation?: string;
  /** 档口图片 */
  stallPic?: string;
}

export interface IStallListResponse {
  stallList: IStallListItem[];
  /** 餐厅名称 */
  canteenName?: string;
  /** 餐段类型名称 */
  mealTimePeriodTypeName?: string;
}

export interface IGetStallListParams {
  /** 餐厅id */
  canteenId: number;
  /** 餐段类型code */
  mealTimePeriodTypeCode: number;
  /** yyyy-MM-dd */
  date: string;
}
