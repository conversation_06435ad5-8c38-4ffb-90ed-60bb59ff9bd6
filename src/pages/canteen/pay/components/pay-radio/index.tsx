import Icon from '@/_/components/Icon';
import classNames from 'classnames';
import styles from './index.less';

interface PayRadioProps {
  children: React.ReactNode;
  checked?: boolean;
  disabled?: boolean;
  onChange?: (checked: boolean) => void;
}

/**
 * @description 支付单选
 * @param {PayRadioProps} props 参数
 * @returns {React.ReactElement} 支付单选
 */
const PayRadio = ({
  children,
  checked = false,
  disabled = false,
  onChange,
}: PayRadioProps): React.ReactElement => {
  let radio = null;
  if (checked) {
    radio = (
      <span className={classNames(styles.payRadio, styles.payRadioChecked)}>
        <Icon color="#fff" name="m-check-shineout" fontSize={16} />
      </span>
    );
  } else if (disabled) {
    radio = <span className={classNames(styles.payRadio, styles.payRadioDisabled)} />;
  } else {
    radio = <span className={styles.payRadio} />;
  }

  return (
    <div className={styles.payItem} onClick={() => !disabled && onChange?.(!checked)}>
      <div className={styles.payInfo}>{children}</div>
      {radio}
    </div>
  );
};

export default PayRadio;
