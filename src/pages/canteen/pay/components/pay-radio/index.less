.payItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding-top: 12px;
}

.payInfo {
  font-size: 14px;
  line-height: 22px;
  color: #141737;
}

.payRadio {
  width: 18px;
  height: 18px;
  text-align: center;
  border: 1px solid #cccfd7;
  border-radius: 50%;
  box-sizing: border-box;

  &Checked {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #197afa;
    border: none;
  }

  &Disabled {
    background-color: #f4f5f8;
  }
}
