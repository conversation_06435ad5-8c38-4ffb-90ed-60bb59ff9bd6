import React, { useEffect, useRef, useState } from 'react';
import { t } from '@shein-bbl/react';
import { But<PERSON>, Divider, Drawer } from 'shineout-mobile';
import styles from './index.less';

interface IPayResultDrawerProps {
  visible: boolean;
  onClose?: () => void;
  onOk?: () => void;
}

/**
 * @description PayResultDrawer
 * @param {IPayResultDrawerProps} props 属性
 * @returns {React.ReactElement} ReactElement
 */
const PayResultDrawer = (props: IPayResultDrawerProps): React.ReactElement => {
  const { visible, onClose, onOk } = props;
  const [countDown, setCountDown] = useState(15);
  const timer = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (visible) {
      if (countDown > 0 && !timer.current) {
        timer.current = setInterval(() => {
          setCountDown((prev) => prev - 1);
        }, 1000);
      } else if (countDown === 0) {
        clearInterval(timer.current);
      }
    } else {
      clearInterval(timer.current);
      setTimeout(() => {
        setCountDown(15);
      }, 1000);
      timer.current = null;
    }
  }, [countDown, visible]);

  return (
    <Drawer visible={visible} onClose={onClose} drawerClass={styles.drawer} maskCloseAble={false}>
      <div className={styles.container}>
        <div className={styles.title}>{t('请确认微信支付是否已完成')}</div>
        <Divider />
        <div
          onClick={() => {
            onClose?.();
            onOk?.();
          }}
        >
          <Button block text type="primary" disabled={countDown > 0}>
            {t('已完成支付')}
            {countDown === 0 ? t('（点击刷新页面）') : t('（{}s后刷新页面）', countDown)}
          </Button>
        </div>
        <Divider />
        <div className={styles.repay} onClick={onClose}>
          {t('支付遇到其他问题，重新支付')}
        </div>
      </div>
    </Drawer>
  );
};

export default PayResultDrawer;
