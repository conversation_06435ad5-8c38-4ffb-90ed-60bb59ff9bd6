import React from 'react';
import { usePageTitle } from '@/_/hooks';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import { Button } from 'shineout-mobile';
import styles from './index.less';

interface IPaySuccessProps {
  onBackHome: () => void;
}

/**
 * @description 支付成功
 * @param {IPaySuccessProps} props 参数
 * @returns {React.ReactElement} 支付成功
 */
const PaySuccess = ({ onBackHome }: IPaySuccessProps): React.ReactElement => {
  usePageTitle(t('支付成功'));
  return (
    <div className={styles.paySuccessContainer}>
      <div className={styles.iconWrapper}>
        <Icon name="m-success-multicolor-shineout-multic" fontSize={120} />
      </div>
      <div className={styles.successText}>{t('支付成功')}</div>
      <Button className={styles.backHomeBtn} type="primary" onClick={onBackHome}>
        {t('返回首页')}
      </Button>
    </div>
  );
};

export default PaySuccess;
