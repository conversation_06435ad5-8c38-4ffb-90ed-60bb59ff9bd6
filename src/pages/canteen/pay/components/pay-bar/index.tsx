import React, { useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { clearCartInfoCache } from '@/_/lego-stores/canteenStore';
import RemainingWXPay from '@/pages/canteen/_/components/remaining-wx-pay';
import { useCanteenPay } from '@/pages/canteen/_/hooks/use-canteen-pay';
import { ECanteenPayChannelType, ICanteenPayResponse } from '@/pages/canteen/_/interfaces';
import { IOrderDetailItem } from '@/pages/canteen/_/interfaces/order';
import { t } from '@shein-bbl/react';
import numeral from 'numeral';
import { Button } from 'shineout-mobile';
import styles from './index.less';

interface PayBarProps {
  payChannelType: ECanteenPayChannelType;
  orderNo: string;
  dishes: IOrderDetailItem[];
  onRefreshOrder: () => void;
}

/**
 * @description 支付底部栏
 * @param {ECanteenPayChannelType} payChannelType 支付方式
 * @returns {React.ReactElement} 支付底部栏
 */
const PayBar = ({
  payChannelType,
  orderNo,
  dishes,
  onRefreshOrder,
}: PayBarProps): React.ReactElement => {
  const { handlePayWallet, handlePayWechat, payLoading } = useCanteenPay();
  const [remainingPay, setRemainingPay] = useState<{
    visible: boolean;
    info?: ICanteenPayResponse;
  }>({
    visible: false,
    info: null,
  });
  const navigate = useNavigate();

  const { totalAmount } = useMemo(() => {
    const totalAmount =
      dishes?.reduce((acc, curr) => {
        const price =
          numeral(curr.dishNum || 0)
            .multiply(curr.dishPrice || 0)
            .value() || 0;
        return numeral(acc).add(price).value();
      }, 0) || 0;
    return { totalAmount };
  }, [dishes]);

  const handlePay = () => {
    if (payLoading) {
      return;
    }
    if (payChannelType === ECanteenPayChannelType.VIRTUAL_WALLET) {
      handlePayWallet(orderNo)
        .then((res) => {
          // 还有差额，需要继续使用微信支付
          if (res.margin > 0) {
            setRemainingPay({
              visible: true,
              info: res,
            });
          } else {
            onRefreshOrder();
          }
        })
        .finally(() => {
          // 支付后清空购物车缓存
          clearCartInfoCache();
        });
    } else {
      handlePayWechat(orderNo).finally(() => {
        // 支付后清空购物车缓存
        clearCartInfoCache();
      });
    }
  };

  /**
   * @description 继续支付
   * @returns {void}
   */
  const handleContinuePay = (): void => {
    handlePayWechat(orderNo);
  };

  /**
   * @description 取消成功
   * @returns {void}
   */
  const handleCancelSuccess = (): void => {
    setRemainingPay({ visible: false, info: null });
    navigate('/canteen/dish', { replace: true });
  };

  return (
    <>
      <div className={styles.bar}>
        <div className={styles.left}>
          <div className={styles.totalPriceWrapper}>
            <span>{t('合计')}</span>
            <span className={styles.totalPrefix}>¥</span>
            <span className={styles.totalPrice}>{totalAmount}</span>
          </div>
        </div>
        <Button
          className="pl-[16px] pr-[16px] text-[16px]"
          type="primary"
          loading={payLoading}
          onClick={handlePay}
        >
          {t('确认支付')}
        </Button>
      </div>
      <RemainingWXPay
        visible={remainingPay.visible}
        onCancelSuccess={handleCancelSuccess}
        onConfirm={handleContinuePay}
        margin={remainingPay.info?.margin}
        totalAmount={remainingPay.info?.totalAmount}
        walletPayAmount={remainingPay.info?.walletPayAmount}
        orderNo={orderNo}
        loading={payLoading}
      />
    </>
  );
};

export default PayBar;
