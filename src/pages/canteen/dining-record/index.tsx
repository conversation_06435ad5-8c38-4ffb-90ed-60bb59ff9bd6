import React, { useCallback, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { useMount } from 'ahooks';
import { Button, Toast } from 'shineout-mobile';
import { LoadingIndicator, PullRefresh, Tabs } from './components';
import CancelOrderButton from './components/cancel-order-button';
import PayNowButton from './components/pay-now-button';
import styles from './index.less';
import {
  type IDiningRecordItem,
  // type IDiningRecordPageData,
  type IDiningRecordStatisticsData,
} from './interfaces';
import { getDiningRecordPageAPI, getDiningRecordStatisticsAPI } from './services';

// 每页加载的记录数
const PAGE_SIZE = 10;

// 标签页类型定义
type TabType = 'all' | 'reserved' | 'toBeEvaluated' | 'completed';

/**
 * @description 就餐记录页面
 * @returns {React.ReactElement}
 */
const DiningRecordPage: React.FC = () => {
  usePageTitle(t('就餐记录'));

  // 统计数据
  const [statistics, setStatistics] = useState<IDiningRecordStatisticsData>({});
  // 当前选中的标签页
  const [activeTab, setActiveTab] = useState<TabType>('all');
  // 记录列表
  const [recordList, setRecordList] = useState<IDiningRecordItem[]>([]);
  // 是否正在加载
  const [loading, setLoading] = useState(false);
  // 是否还有更多数据
  const [hasMore, setHasMore] = useState(true);
  // 当前页码
  const pageNumberRef = useRef(1);
  // 导航
  const navigate = useNavigate();

  /**
   * @description 获取统计数据
   */
  const fetchStatistics = useCallback(async () => {
    getDiningRecordStatisticsAPI().then((res) => {
      setStatistics(res);
    });
  }, []);

  /**
   * @description 获取记录列表
   * @param refresh 是否刷新（重置页码）
   * @param tabType 标签页类型，如果不传则使用当前activeTab
   */
  const fetchRecordList = usePersistFn(async (refresh = false, tabType?: TabType) => {
    try {
      setLoading(true);

      // 如果是刷新，重置页码
      if (refresh) {
        pageNumberRef.current = 1;
        setRecordList([]);
      }

      // 根据当前标签页设置请求参数
      const params: any = {
        pageNumber: pageNumberRef.current,
        pageSize: PAGE_SIZE,
      };

      // 使用传入的tabType或当前activeTab
      const currentTab = tabType || activeTab;

      // 根据标签页类型设置不同的查询参数
      switch (currentTab) {
        case 'reserved':
          params.orderStatus = 1; // 已预定
          break;
        case 'toBeEvaluated':
          params.orderStatus = 2; // 已完成
          params.isToBeEvaluated = true; // 待评价
          break;
        case 'completed':
          params.orderStatus = 2; // 已完成
          break;
        default:
          // 全部不需要特殊参数
          params.orderStatus = undefined;
          break;
      }

      const data = await getDiningRecordPageAPI(params);

      // 更新记录列表（追加或替换）
      if (refresh) {
        setRecordList(data.rows || []);
      } else {
        setRecordList((prev) => [...prev, ...(data.rows || [])]);
      }

      // 判断是否还有更多数据
      const currentTotal = data.rows?.length || 0;
      setHasMore(
        currentTotal === PAGE_SIZE && pageNumberRef.current * PAGE_SIZE < (data.total || 0),
      );

      // 页码加1，为下次加载做准备
      pageNumberRef.current += 1;
    } catch (error) {
      console.error(t('获取记录列表失败'), error);
      Toast.fail(t('获取记录列表失败'));
    } finally {
      setLoading(false);
    }
  });

  /**
   * @description 切换标签页
   */
  const handleTabChange = (tab: TabType) => {
    if (tab === activeTab) return;
    setActiveTab(tab);
    // 切换标签页时重置数据并重新加载
    setHasMore(true);
    // 传递新的tab值给fetchRecordList，确保使用最新的tab值
    fetchRecordList(true, tab);
  };

  /**
   * @description 下拉刷新
   */
  const handleRefresh = async () => {
    await fetchStatistics();
    await fetchRecordList(true);
    return true;
  };

  /**
   * @description 加载更多
   */
  const loadMore = useCallback(async () => {
    if (!hasMore || loading) return;
    await fetchRecordList();
  }, [hasMore, loading, fetchRecordList]);

  /**
   * @description 评价
   * @param {IDiningRecordItem} record
   * @returns {void}
   */
  const handleGoToEvaluate = (record: IDiningRecordItem): void => {
    navigate(`/canteen/evaluation/${record.id}`);
  };

  /**
   * @description 取消订单后刷新
   * @returns {void}
   */
  const handleOrderCancelled = (): void => {
    setHasMore(true);
    fetchRecordList(true);
  };

  /**
   * @description 订单详情
   * @param {IDiningRecordItem} record
   * @returns {void}
   */
  const handleGoToOrderDetail = (record: IDiningRecordItem): void => {
    navigate(`/canteen/order/${record.id}`);
  };

  // 初始化加载
  useMount(() => {
    fetchStatistics();
    fetchRecordList(true);
  });

  /**
   * @description 渲染记录项
   */
  const renderRecordItem = (record: IDiningRecordItem) => {
    // 2月1日-白班晚餐-定餐(orderDate: yyyy-MM-dd)
    const addTimeMonth = record.orderDate?.split(' ')[0].split('-')[1];
    const addTimeDay = record.orderDate?.split(' ')[0].split('-')[2];
    const title = `${addTimeMonth}${t('月')}${addTimeDay}${t('日')}-${
      record.mealTimePeriodTypeStr
    }-${record.orderTypeStr}`;

    return (
      <div key={record.id} className={styles.recordItem}>
        <div className={`${styles.recordStatus} ${styles[`status${record.orderStatus}`]}`}>
          {record.orderStatusStr}
        </div>

        <div className={styles.recordHeader} onClick={() => handleGoToOrderDetail(record)}>
          <div className={styles.recordTitle}>{title}</div>
        </div>

        <div className={styles.recordTime} onClick={() => handleGoToOrderDetail(record)}>
          {record.orderStatus === 1 ? t('预定时间') : t('完成时间')}:{' '}
          <span style={{ color: '#141737' }}>
            {record.orderStatus === 1 ? record.addTime : record.finishTime}
          </span>
        </div>

        <div className={styles.recordTime} onClick={() => handleGoToOrderDetail(record)}>
          {t('支付金额')}:{' '}
          <span style={{ color: '#141737' }}>
            {record.totalAmount ? `¥${record.totalAmount?.toFixed(2)}` : '--'}
          </span>
        </div>

        <div className={styles.recordContent} onClick={() => handleGoToOrderDetail(record)}>
          <div className={styles.canteenInfo}>
            {record.canteenName}-{record.stallName}-{record.dishNames?.join('、') || ''}
          </div>
        </div>

        {/* 待评价状态显示评价按钮 */}
        {Boolean(record.toBeEvaluated) === true && (
          <div className={styles.recordFooter}>
            <Button
              type="primary"
              size="small"
              className={styles.evaluateBtn}
              onClick={() => handleGoToEvaluate(record)}
            >
              {t('去评价')}
            </Button>
          </div>
        )}

        {/* 待支付状态显示支付和取消按钮 */}
        {Number(record.orderStatus) === 0 && (
          <div className={styles.recordFooter}>
            <PayNowButton orderNo={record.orderNo} />
            <CancelOrderButton orderNo={record.orderNo} onCancelled={handleOrderCancelled} />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={styles.diningRecordPage}>
      <Tabs className={styles.tabs} activeKey={activeTab} onChange={handleTabChange as any}>
        <Tabs.Tab title={t('全部')} key="all" />
        <Tabs.Tab
          title={
            <div className={styles.tabWithBadge}>
              {t('已预定')}
              {statistics.reservedOrderCount ? (
                <span className={styles.badge}>{statistics.reservedOrderCount}</span>
              ) : null}
            </div>
          }
          key="reserved"
        />
        <Tabs.Tab
          title={
            <div className={styles.tabWithBadge}>
              {t('待评价')}
              {statistics.toBeEvaluatedOrderCount ? (
                <span className={styles.badge}>{statistics.toBeEvaluatedOrderCount}</span>
              ) : null}
            </div>
          }
          key="toBeEvaluated"
        />
        <Tabs.Tab
          title={
            <div className={styles.tabWithBadge}>
              {t('已完成')}
              {statistics.completedOrderCount ? (
                <span className={styles.badge}>{statistics.completedOrderCount}</span>
              ) : null}
            </div>
          }
          key="completed"
        />
      </Tabs>

      <div className={styles.recordListContainer}>
        <PullRefresh onRefresh={handleRefresh}>
          {recordList.length > 0 ? (
            <div className={styles.recordList}>
              {recordList.map(renderRecordItem)}

              {/* 使用LoadingIndicator组件来触发加载更多 */}
              <LoadingIndicator onVisible={loadMore} loading={loading} hasMore={hasMore} />
            </div>
          ) : (
            <div className={styles.emptyContainer}>
              <div className={styles.emptyText}>{t('暂无记录')}</div>
            </div>
          )}
        </PullRefresh>
      </div>
    </div>
  );
};

export default DiningRecordPage;
