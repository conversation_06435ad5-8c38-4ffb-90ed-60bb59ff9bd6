// 就餐记录统计接口返回数据
interface IDiningRecordStatisticsData {
  /** 已预定订单数量 */
  reservedOrderCount?: number;
  /** 待评价订单数量 */
  toBeEvaluatedOrderCount?: number;
  /** 已完成订单数量 */
  completedOrderCount?: number;
}

// 就餐记录分页查询接口请求参数
interface IDiningRecordPageRequest {
  /** 订单状态 0待支付，1已预定，2已完成，3已取消，4已退款 */
  orderStatus?: number;
  /** 是否查询待评价订单 */
  isToBeEvaluated?: boolean;
  /** 页码 */
  pageNumber?: number;
  /** 页容量 */
  pageSize?: number;
}

// 就餐记录项
interface IDiningRecordItem {
  /** 订单id */
  id?: number;
  /** 订单号 */
  orderNo?: string;
  /** 预定时间 */
  addTime?: string;
  /** yyyy-MM-dd */
  orderDate?: string;
  /** 订单类型 0定餐，1现场点餐 */
  orderType?: number;
  /** 订单类型描述 0定餐，1现场点餐 */
  orderTypeStr?: string;
  /** 餐段类型 0午餐，1白班晚餐，2晚班晚餐，3宵夜 */
  mealTimePeriodType?: number;
  /** 餐段类型描述 0午餐，1白班晚餐，2晚班晚餐，3宵夜 */
  mealTimePeriodTypeStr?: string;
  /** 订单状态 0待支付，1已预定，2已完成，3已取消，4已退款 */
  orderStatus?: number;
  /** 订单状态描述 0待支付，1已预定，2已完成，3已取消，4已退款 */
  orderStatusStr?: string;
  /** 食堂id */
  canteenId?: number;
  /** 食堂名称 */
  canteenName?: string;
  /** 档口id */
  stallId?: number;
  /** 档口名称 */
  stallName?: string;
  /** 菜品名称列表 */
  dishNames?: string[];
  /** 支付金额 */
  totalAmount?: number;
  /** 完成时间 */
  finishTime?: string;
  /** 是否可评价 */
  toBeEvaluated?: boolean;
}

// 就餐记录分页查询接口返回数据
interface IDiningRecordPageData {
  /** 总记录数 */
  total?: number;
  /** 当前记录集合 */
  rows?: IDiningRecordItem[];
}

export type {
  IDiningRecordItem,
  IDiningRecordPageData,
  IDiningRecordPageRequest,
  IDiningRecordStatisticsData,
};
