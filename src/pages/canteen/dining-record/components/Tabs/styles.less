.tabs {
  display: flex;
  width: 100%;
  flex-direction: column;

  .tabsHeader {
    display: flex;
    width: 100%;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fff;

    .tabItem {
      position: relative;
      padding: 12px 0;
      font-size: 14px;
      color: #666;
      text-align: center;
      flex: 1;

      &.active {
        font-weight: 500;
        color: #1890ff;
      }
    }
  }

  .tabsContent {
    flex: 1;
    overflow: auto;
  }
}

// 提取出来减少嵌套深度
.tabs .tabsHeader .tabItem.active::after {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 100%;
  height: 2px;
  background-color: #1890ff;
  content: '';
  transform: translateX(-50%);
}
