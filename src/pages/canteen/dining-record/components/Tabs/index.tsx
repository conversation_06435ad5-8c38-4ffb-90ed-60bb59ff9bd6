import React, { useCallback, useState } from 'react';
import styles from './styles.less';

interface TabProps {
  title: React.ReactNode;
  key: string;
}

interface TabsProps {
  activeKey?: string;
  onChange?: (key: string) => void;
  className?: string;
  children: React.ReactElement<TabProps>[] | React.ReactElement<TabProps>;
}

interface TabPanelProps {
  title: React.ReactNode;
  key: string;
  children?: React.ReactNode;
}

const TabPanel: React.FC<TabPanelProps> = () => null;

const Tabs: React.FC<TabsProps> & { Tab: typeof TabPanel } = (props) => {
  const { activeKey: propActiveKey, onChange, className = '', children } = props;
  const [activeKey, setActiveKey] = useState<string>(propActiveKey || '');

  // 获取所有标签页配置
  const tabs = React.Children.toArray(children)
    .map((child) => {
      if (React.isValidElement(child)) {
        return {
          key: child.key?.toString().replace('.$', '') || '',
          title: child.props.title,
        };
      }
      return null;
    })
    .filter(Boolean) as { key: string; title: React.ReactNode }[];

  // 如果没有设置初始activeKey，使用第一个标签页的key
  React.useEffect(() => {
    if (!propActiveKey && tabs.length > 0) {
      setActiveKey(tabs[0].key);
    } else if (propActiveKey) {
      setActiveKey(propActiveKey);
    }
  }, [propActiveKey, tabs]);

  // 处理标签页切换
  const handleTabChange = useCallback(
    (key: string) => {
      if (key === activeKey) return;
      setActiveKey(key);
      onChange?.(key);
    },
    [activeKey, onChange],
  );

  return (
    <div className={`${styles.tabs} ${className}`}>
      <div className={styles.tabsHeader}>
        {tabs.map((tab) => (
          <div
            key={tab.key}
            className={`${styles.tabItem} ${activeKey === tab.key ? styles.active : ''}`}
            onClick={() => handleTabChange(tab.key)}
          >
            {tab.title}
          </div>
        ))}
      </div>
      <div className={styles.tabsContent}>
        {React.Children.toArray(children).map((child) => {
          if (
            React.isValidElement(child) &&
            child.key?.toString().replace('.$', '') === activeKey
          ) {
            return child.props.children;
          }
          return null;
        })}
      </div>
    </div>
  );
};

Tabs.Tab = TabPanel;

export default Tabs;
