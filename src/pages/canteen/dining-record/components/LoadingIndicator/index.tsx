import React, { useEffect, useRef } from 'react';
import { t } from '@shein-bbl/react';

interface LoadingIndicatorProps {
  onVisible: () => void;
  loading?: boolean;
  hasMore?: boolean;
}

/**
 * @description 加载指示器组件，当它进入视口时会触发加载更多的操作
 */
const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  onVisible,
  loading = false,
  hasMore = true,
}) => {
  const indicatorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 如果没有更多数据或正在加载，不需要监听
    if (!hasMore) return null;

    const observer = new IntersectionObserver(
      (entries) => {
        // 当指示器进入视口且有更多数据可加载时触发回调
        if (entries[0].isIntersecting && hasMore && !loading) {
          console.log(t('加载指示器进入视口，触发加载更多'));
          onVisible();
        }
      },
      {
        // 设置根元素为null，表示相对于视口
        root: null,
        // 当指示器有10%进入视口时触发
        threshold: 0.1,
        // 提前100px触发，使加载更平滑
        rootMargin: '0px 0px 100px 0px',
      },
    );

    // 开始观察指示器元素
    if (indicatorRef.current) {
      observer.observe(indicatorRef.current);
    }

    // 组件卸载时停止观察
    return () => {
      observer.disconnect();
    };
  }, [onVisible, hasMore, loading]);

  return (
    <div ref={indicatorRef} style={{ padding: '20px 0', textAlign: 'center' }}>
      {hasMore ? (
        loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <div
              style={{
                width: '20px',
                height: '20px',
                marginRight: '8px',
                border: '2px solid #e8f1fe',
                borderTopColor: '#197afa',
                borderRadius: '50%',
                animation: 'spin 0.8s linear infinite',
              }}
            ></div>
            <span>{t('加载中...')}</span>
          </div>
        ) : (
          <div>{t('上拉加载更多')}</div>
        )
      ) : (
        <div style={{ fontSize: '14px', color: '#999da8' }}>{t('没有更多数据了')}</div>
      )}
    </div>
  );
};

export default LoadingIndicator;
