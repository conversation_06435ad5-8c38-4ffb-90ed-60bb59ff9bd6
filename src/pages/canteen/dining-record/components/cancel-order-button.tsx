import React from 'react';
import { t } from '@shein-bbl/react';
import { useRequest } from 'ahooks';
import { Button, Toast } from 'shineout-mobile';
import { cancelCanteenOrder } from '../../_/api';
import styles from '../index.less';
interface ICancelOrderButtonProps {
  orderNo: string;
  onCancelled: () => void; // 取消成功后的回调
}

/**
 * @description 取消订单按钮
 * @param {ICancelOrderButtonProps} props
 * @returns {React.ReactElement}
 */
const CancelOrderButton = ({
  orderNo,
  onCancelled,
}: ICancelOrderButtonProps): React.ReactElement => {
  const { run: runCancelOrder, loading: cancelOrderLoading } = useRequest(cancelCanteenOrder, {
    manual: true,
    onSuccess: () => {
      Toast.success(t('取消成功'));
      onCancelled();
    },
  });

  return (
    <Button
      type="default"
      size="small"
      className={styles.evaluateBtn}
      onClick={() => runCancelOrder(orderNo)}
      loading={cancelOrderLoading}
    >
      {t('取消')}
    </Button>
  );
};

export default CancelOrderButton;
