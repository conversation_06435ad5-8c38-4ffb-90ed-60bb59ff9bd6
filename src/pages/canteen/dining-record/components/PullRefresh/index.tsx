import React, { useEffect, useRef, useState } from 'react';
import { t } from '@shein-bbl/react';
import styles from './styles.less';

interface PullRefreshProps {
  onRefresh: () => Promise<boolean | void>;
  children: React.ReactNode;
  pullingText?: string;
  loosingText?: string;
  loadingText?: string;
  finishText?: string;
  headHeight?: number;
  threshold?: number;
}

const PullRefresh: React.FC<PullRefreshProps> = ({
  onRefresh,
  children,
  pullingText = t('下拉刷新'),
  loosingText = t('释放刷新'),
  loadingText = t('加载中...'),
  finishText = t('刷新成功'),
  headHeight = 50,
  threshold = 70,
}) => {
  const [status, setStatus] = useState<'idle' | 'pulling' | 'loosing' | 'loading' | 'success'>(
    'idle',
  );
  const [height, setHeight] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const touchStartY = useRef(0);
  const [lastTime, setLastTime] = useState(0);

  // 处理触摸开始事件
  const handleTouchStart = (e: React.TouchEvent) => {
    // 只有在滚动到顶部时才允许下拉刷新
    if (containerRef.current && containerRef.current.scrollTop === 0) {
      touchStartY.current = e.touches[0].clientY;
      setStatus('idle');
    }
  };

  // 处理触摸移动事件
  const handleTouchMove = (e: React.TouchEvent) => {
    if (status === 'loading') return;

    if (containerRef.current && containerRef.current.scrollTop === 0) {
      const touchMoveY = e.touches[0].clientY;
      const distance = touchMoveY - touchStartY.current;

      if (distance > 0) {
        e.preventDefault();
        const newHeight = Math.min(distance * 0.6, threshold * 1.5);
        setHeight(newHeight);

        if (newHeight < threshold) {
          setStatus('pulling');
        } else {
          setStatus('loosing');
        }
      }
    }
  };

  // 处理触摸结束事件
  const handleTouchEnd = async () => {
    if (status === 'loosing') {
      setStatus('loading');
      setHeight(headHeight);

      try {
        await onRefresh();
        setStatus('success');

        // 显示成功状态一段时间后恢复
        setTimeout(() => {
          setHeight(0);
          setTimeout(() => {
            setStatus('idle');
          }, 300);
        }, 500);
      } catch (error) {
        // 发生错误时也恢复初始状态
        setHeight(0);
        setTimeout(() => {
          setStatus('idle');
        }, 300);
      }
    } else {
      // 如果没有达到触发刷新的阈值，直接恢复
      setHeight(0);
      setStatus('idle');
    }
  };

  // 防止频繁触发刷新
  useEffect(() => {
    const now = Date.now();
    if (status === 'loading' && now - lastTime < 1000) {
      setStatus('idle');
      setHeight(0);
    }
    if (status === 'loading') {
      setLastTime(now);
    }
  }, [status, lastTime]);

  // 获取当前状态对应的文本
  const getStatusText = () => {
    switch (status) {
      case 'pulling':
        return pullingText;
      case 'loosing':
        return loosingText;
      case 'loading':
        return loadingText;
      case 'success':
        return finishText;
      default:
        return '';
    }
  };

  return (
    <div
      className={`${styles.pullRefresh} pullRefresh`} // 添加不带样式模块的类名，便于InfiniteScroll组件选择
      ref={containerRef}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <div
        className={styles.pullRefreshHead}
        style={{
          height: `${height}px`,
          transition: status === 'idle' || status === 'loading' ? 'height 0.3s' : 'none',
        }}
      >
        <div className={styles.pullRefreshContent}>
          {status === 'loading' && <div className={styles.loadingIcon}></div>}
          {status === 'success' && <div className={styles.successIcon}>✓</div>}
          <span>{getStatusText()}</span>
        </div>
      </div>
      <div className={styles.pullRefreshContainer}>{children}</div>
    </div>
  );
};

export default PullRefresh;
