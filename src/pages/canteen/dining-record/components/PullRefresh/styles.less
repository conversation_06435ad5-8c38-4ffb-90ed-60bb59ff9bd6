.pullRefresh {
  position: relative;
  height: 100%;

  /* 保持滚动，但确保与全局滚动事件协同工作 */
  overflow: auto;
  -webkit-overflow-scrolling: touch;

  .pullRefreshHead {
    position: absolute;
    left: 0;
    width: 100%;
    overflow: hidden;
    text-align: center;

    .pullRefreshContent {
      display: flex;
      height: 100%;
      font-size: 14px;
      color: #999;
      align-items: center;
      justify-content: center;

      .loadingIcon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
        border: 2px solid #f3f3f3;
        border-radius: 50%;
        border-top: 2px solid #1890ff;
        animation: spin 1s linear infinite;
      }

      .successIcon {
        display: flex;
        width: 20px;
        height: 20px;
        margin-right: 8px;
        font-weight: bold;
        color: #52c41a;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .pullRefreshContainer {
    min-height: 100%;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
