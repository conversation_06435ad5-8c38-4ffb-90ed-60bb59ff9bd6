import React from 'react';
import { t } from '@shein-bbl/react';
import { Button } from 'shineout-mobile';
import { useCanteenPay } from '../../_/hooks/use-canteen-pay';
import styles from '../index.less';

interface IPayNowButtonProps {
  orderNo: string;
}

/**
 * @description 立即支付按钮
 * @param {IPayNowButtonProps} props
 * @returns {React.ReactElement}
 */
const PayNowButton = ({ orderNo }: IPayNowButtonProps): React.ReactElement => {
  const { payLoading, handlePayWechat } = useCanteenPay();

  const handlePay = () => {
    handlePayWechat(orderNo);
  };

  return (
    <Button
      type="primary"
      size="small"
      className={styles.evaluateBtn}
      style={{ marginRight: '6px' }}
      onClick={handlePay}
      loading={payLoading}
    >
      {t('立即支付')}
    </Button>
  );
};

export default PayNowButton;
