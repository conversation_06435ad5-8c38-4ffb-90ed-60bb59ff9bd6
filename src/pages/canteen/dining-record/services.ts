import { get, post } from '@/utils/fetch';
import {
  type IDiningRecordPageData,
  type IDiningRecordPageRequest,
  type IDiningRecordStatisticsData,
} from './interfaces';
// // 导入mock数据
// import mockDiningRecordPageData from './mock/getDiningRecordPageAPI.json';
// import mockDiningRecordStatisticsData from './mock/getDiningRecordStatisticsAPI.json';

/**
 * 就餐记录统计
 * https://soapi.sheincorp.cn/application/3694/routes/243671/doc
 * @param data 查询参数
 * @returns 统计数据
 */
export const getDiningRecordStatisticsAPI = () => {
  // // 开发环境使用mock数据
  // if (process.env.NODE_ENV === 'development') {
  //   return Promise.resolve(mockDiningRecordStatisticsData);
  // }
  return get<IDiningRecordStatisticsData>('/canteen/staff/diningRecord/selectStatistics');
};

/**
 * 就餐记录分页查询
 * https://soapi.sheincorp.cn/application/3694/routes/244740/doc
 * @param data 分页查询参数
 * @returns 分页数据
 */
export const getDiningRecordPageAPI = (data: IDiningRecordPageRequest = {}) => {
  // // 开发环境使用mock数据
  // if (process.env.NODE_ENV === 'development') {
  //   return Promise.resolve(mockDiningRecordPageData);
  // }
  return post<IDiningRecordPageData>('/canteen/staff/diningRecord/selectPage', data);
};
