.diningRecordPage {
  display: flex;
  height: 100vh;

  /* 确保页面可以滚动 */
  overflow-y: auto;
  background-color: #f5f5f5;
  flex-direction: column;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */

  .tabs {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #fff;

    :global {
      .so-tabs-header {
        background-color: #fff;
      }
    }
  }

  .tabWithBadge {
    position: relative;
    display: inline-block;
    padding-right: 8px;

    .badge {
      position: absolute;
      top: -8px;
      right: -8px;
      height: 16px;
      min-width: 16px;
      padding: 0 4px;
      font-size: 12px;
      line-height: 16px;
      color: #fff;
      text-align: center;
      background-color: #ff4d4f;
      border-radius: 8px;
    }
  }

  .recordListContainer {
    padding: 12px;

    /* 移除overflow-y: auto，避免与PullRefresh组件的滚动冲突 */
    flex: 1;
  }

  .recordList {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .recordItem {
    position: relative;
    padding: 16px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 5%);

    .recordStatus {
      position: absolute;
      top: 0;
      right: 0;
      padding: 2px 8px;
      font-size: 14px;
      border-radius: 4px;

      &.status0 {
        color: #d9001b;
        background-color: #ec808dcd;
      }

      &.status1 {
        color: #996f37;
        background-color: #fff9e8;
      }

      &.status2 {
        color: #00a85f;
        background-color: #e4fced;
      }
    }

    .recordHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .recordTitle {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    .recordTime {
      margin-bottom: 12px;
      font-size: 14px;
      color: #999;
    }

    .recordContent {
      display: flex;
      padding: 8px;
      background: #f7f8fa;
      border-radius: 4px;
      align-items: center;
      gap: 10px;
      align-self: stretch;

      .canteenInfo {
        overflow: hidden;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        color: #141737;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .recordFooter {
      display: flex;
      padding-top: 12px;
      margin-top: 12px;
      justify-content: flex-end;
      border-top: 1px solid #f0f0f0;

      .evaluateBtn {
        height: 32px;
        padding: 0 16px;
        font-size: 14px;
      }
    }
  }

  .emptyContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;

    .emptyText {
      margin-top: 12px;
      font-size: 14px;
      color: #999;
    }
  }
}
