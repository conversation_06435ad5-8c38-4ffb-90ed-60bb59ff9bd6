import { useNavigate } from 'react-router-dom';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import styles from './index.less';

const menus = [
  {
    name: t('消费码'),
    path: '/canteen/consumer-qrcode',
    icon: 'm-my-wallet-multic',
  },
  {
    name: t('就餐记录'),
    path: '/canteen/dining-record',
    icon: 'm-dining-records-multic',
  },
  {
    name: t('我的'),
    path: '/canteen/my',
    icon: 'm-personal-information-multic',
  },
];

/**
 * @description 食堂头部
 * @returns {React.ReactElement} 头部
 */
const CanteenHeader = (): React.ReactElement => {
  const navigate = useNavigate();

  return (
    <div className={styles.container}>
      {menus.map((menu) => {
        return (
          <div className={styles.menu} key={menu.path} onClick={() => navigate(menu.path)}>
            <Icon name={menu.icon} fontSize={48} />
            <div className={styles.name}>{menu.name}</div>
          </div>
        );
      })}
    </div>
  );
};

export default CanteenHeader;
