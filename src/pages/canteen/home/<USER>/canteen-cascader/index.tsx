import { useState } from 'react';
import Icon from '@/_/components/Icon';
import { getParkCanteenTree } from '@/pages/canteen/_/api';
import { t } from '@shein-bbl/react';
import { useRequest, useSetState } from 'ahooks';
import classNames from 'classnames';
import { Cascader, CascaderProps } from 'shineout-mobile';
import styles from './index.less';

export interface ICanteenCascaderValue {
  id: string | number;
  name: string;
}

export interface ICascaderData extends ICanteenCascaderValue {
  children?: ICascaderData[];
}

const CANTEEN_STORAGE_KEY = 'canteen_cascader_value';

/**
 * @description 根据parkId, canteenId找到对应的对象
 * @param {ICascaderData[]} data 数据源
 * @param {string | number} parkId 园区id
 * @param {string | number} canteenId 食堂id
 * @returns {ICascaderData[] | null} 返回对象
 */
export const findCascaderData = (
  data: ICascaderData[],
  parkId: string | number,
  canteenId: string | number,
): ICascaderData[] | null => {
  const park = data?.find((x) => x.id === parkId);
  const canteen = park?.children?.find((x) => x.id === canteenId);
  if (canteen) {
    return [
      {
        id: park.id,
        name: park.name,
      },
      {
        id: canteen.id,
        name: canteen.name,
      },
    ];
  }
  return null;
};

export const CanteenCascader = (
  props: Omit<
    CascaderProps<ICascaderData>,
    'data' | 'keygen' | 'renderItem' | 'onChange' | 'value'
  > & {
    value: ICanteenCascaderValue[];
    onChange: (value: ICanteenCascaderValue[]) => void;
  },
): React.ReactElement => {
  const { onChange, value, ...restProps } = props;
  const [areaOptions, setAreaOptions] = useState<ICascaderData[]>([]);
  const [state, setState] = useSetState<{
    popupValue: (string | number)[];
    visible: boolean;
  }>({
    popupValue: value?.map((x) => x.id),
    visible: false,
  });
  const { loading: areaLoading } = useRequest(getParkCanteenTree, {
    onSuccess: (data) => {
      const options = (data || []).map((x) => {
        return {
          id: `park-${x.parkId}`,
          name: x.parkName,
          children:
            x.canteenVos?.map((y) => {
              return {
                id: y.id,
                name: y.canteenName,
              };
            }) || [],
        };
      });
      setAreaOptions(options);
      const storageValue = localStorage.getItem(CANTEEN_STORAGE_KEY);
      if (storageValue) {
        const cascaderValue = JSON.parse(storageValue);
        const cascaderData = findCascaderData(
          options,
          cascaderValue?.[0]?.id,
          cascaderValue?.[1]?.id,
        );
        if (cascaderData) {
          setState({ popupValue: cascaderData.map((x) => x.id) });
          onChange?.(cascaderData);
          return;
        }
      }
      // 未选择则默认弹出让用户选择
      setState({ visible: true });
    },
  });

  /**
   * @description 处理change
   * @param {string | number} v 值
   * @returns {void}
   */
  const handleChange = (v: (string | number)[]): void => {
    setState({ popupValue: v });
    if (v?.length === 2) {
      const parkId = v[0];
      const canteenId = v[1];
      const cascaderValue = findCascaderData(areaOptions, parkId, canteenId);
      if (cascaderValue) {
        localStorage.setItem(CANTEEN_STORAGE_KEY, JSON.stringify(cascaderValue));
      }
      onChange?.(cascaderValue);
    }
  };

  const hasSelectedCanteen = value?.[1];

  return (
    <Cascader
      title={t('请选择食堂')}
      loading={areaLoading}
      data={areaOptions}
      keygen="id"
      renderItem="name"
      placeholder={t('请选择')}
      visible={state.visible}
      onVisibleChange={(v) => {
        setState({ visible: v });
        const popupValue = value?.map((x) => x.id);
        if (popupValue.join(',') !== state.popupValue.join(',')) {
          setState({ popupValue });
        }
      }}
      onChange={handleChange}
      value={state.popupValue}
      {...restProps}
    >
      <div className={classNames(styles.canteen, { [styles.canteenSelected]: hasSelectedCanteen })}>
        <Icon className={styles.canteenIcon} name="m-location-shineout-fill" fontSize={16} />
        <div className={styles.canteenText}>
          {hasSelectedCanteen ? value[1].name : t('请先选择食堂')}
        </div>
        <Icon name="arrow-right" fontSize={14} />
      </div>
    </Cascader>
  );
};
