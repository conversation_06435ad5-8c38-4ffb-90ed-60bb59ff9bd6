import React from 'react';
import { useNavigate } from 'react-router-dom';
import { EMPTY_STR } from '@/share';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';
import { Toast } from 'shineout-mobile';
import { EOrderDisplayType, IMealTypeListItem, orderDisplayTypeTextMap } from '../../interfaces';
import styles from './index.less';

interface MealPeriodCardProps {
  data: IMealTypeListItem;
  canteenId: number;
  date: string;
}

const statusClassMap = {
  [EOrderDisplayType.AVAILABLE]: styles.available,
  [EOrderDisplayType.BOOKED]: styles.booked,
  [EOrderDisplayType.COMPLETED]: styles.completed,
  [EOrderDisplayType.EXPIRED]: styles.expired,
  [EOrderDisplayType.NOT_OPEN]: styles.notOpen,
};

const MealPeriodCard = ({ data, canteenId, date }: MealPeriodCardProps): React.ReactElement => {
  const { mealTimePeriodTypeName, timePeriod, bookDeadlineTime, displayType, orderDto } = data;
  const navigate = useNavigate();

  const bookDeadlineRow = (
    <div className={styles.infoRow}>
      <span className={styles.label}>{t('预定截止：')}</span>
      <span className={styles.value}>{bookDeadlineTime}</span>
    </div>
  );

  const orderRow = <div className={styles.orderRow}>{orderDto?.orderContent || EMPTY_STR}</div>;

  let handleClick: (() => void) | undefined;
  let mainRender: React.ReactNode;

  switch (displayType) {
    // 可预定
    case EOrderDisplayType.AVAILABLE:
      mainRender = (
        <>
          {bookDeadlineRow}
          <div className={styles.actionRow}>
            <button
              className={classNames(styles.btn, styles.bookBtn)}
              onClick={() => {
                navigate(
                  `/canteen/stall?canteenId=${canteenId}&mealTimePeriodTypeCode=${data.mealTimePeriodTypeCode}&date=${date}`,
                );
              }}
            >
              {t('立即预定')}
            </button>
          </div>
        </>
      );
      break;
    // 已预定
    case EOrderDisplayType.BOOKED:
      handleClick = () => {
        navigate(`/canteen/order/${orderDto?.orderId}`);
      };
      mainRender = (
        <>
          {orderRow}
          <div className={styles.actionRow}>
            <button className={classNames(styles.btn, styles.verifyBtn)}>{t('核销定餐码')}</button>
          </div>
        </>
      );
      break;
    // 已完成
    case EOrderDisplayType.COMPLETED:
      handleClick = () => {
        navigate(`/canteen/order/${orderDto?.orderId}`);
      };
      mainRender = (
        <>
          {orderRow}
          <div className={styles.actionRow}>
            <button className={classNames(styles.btn, styles.detailBtn)} onClick={handleClick}>
              {t('查看详情')}
            </button>
          </div>
        </>
      );
      break;
    // 已截止
    case EOrderDisplayType.EXPIRED:
      handleClick = () => {
        Toast.info(t('该餐段已截止预定'));
      };
      mainRender = bookDeadlineRow;
      break;
    // 未开放
    case EOrderDisplayType.NOT_OPEN:
      handleClick = () => {
        Toast.info(t('该餐段未开放'));
      };
      mainRender = bookDeadlineRow;
      break;
  }

  return (
    <div className={styles.card} onClick={handleClick}>
      <div className={styles.header}>
        <span className={styles.title}>{mealTimePeriodTypeName}</span>
        <span className={classNames(styles.status, statusClassMap[displayType])}>
          {orderDisplayTypeTextMap[displayType]}
        </span>
      </div>
      <div className={styles.infoRow}>
        <span className={styles.label}>{t('就餐时段：')}</span>
        <span className={styles.value}>{timePeriod}</span>
      </div>
      {mainRender}
    </div>
  );
};

export default MealPeriodCard;
