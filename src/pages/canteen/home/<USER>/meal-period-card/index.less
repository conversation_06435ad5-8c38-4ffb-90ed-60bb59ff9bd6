@base-color: #141737;
@primary-color: #197afa;

.card {
  position: relative;
  padding: 12px;
  margin-bottom: 8px;
  background: #fff;
  border-radius: 4px;
}

.header {
  overflow: hidden;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: @base-color;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #23253b;
}

.status {
  position: absolute;
  top: 0;
  right: 0;
  padding: 1px 6px;
  font-size: 12px;
  line-height: 20px;
  border-radius: 0 4px;

  &.available {
    color: @primary-color;
    background: #e9f5fe;
  }

  &.booked {
    color: #996f37;
    background: #fff9e8;
  }

  &.completed {
    color: #00a85f;
    background: #e4fced;
  }

  &.expired,
  &.notOpen {
    color: @base-color;
    background: #f4f5f8;
  }
}

.infoRow {
  margin-top: 4px;
  font-size: 14px;
  line-height: 22px;
}

.label {
  margin-right: 2px;
  color: #999da8;
}

.value {
  color: @base-color;
}

.orderRow {
  padding: 8px;
  margin-top: 4px;
  overflow: hidden;
  font-size: 12px;
  line-height: 20px;
  color: @base-color;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: #f7f8fa;
  border-radius: 4px;
}

.actionRow {
  margin-top: 8px;
  text-align: right;
}

.btn {
  padding: 2px 8px;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  border: none;
  border-radius: 4px;
}

.bookBtn {
  color: #fff;
  background: @primary-color;
}

.verifyBtn {
  color: #fff;
  background: #f56c0a;
}

.detailBtn {
  padding: 2px 7px;
  color: @base-color;
  background: #fff;
  border: 1px solid #cccfd7;
}
