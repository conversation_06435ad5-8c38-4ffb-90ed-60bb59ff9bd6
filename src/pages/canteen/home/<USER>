import { t } from '@shein-bbl/react';

interface IOrderDto {
  /** 订单id -查询核销码使用 */
  orderId?: number;
  /** 订单内容 */
  orderContent?: string;
}

/** 订单显示类型 */
export enum EOrderDisplayType {
  /** 已截止 */
  EXPIRED = 0,
  /** 可预定 */
  AVAILABLE,
  /** 已预定 */
  BOOKED,
  /** 已完成 */
  COMPLETED,
  /** 未开放 */
  NOT_OPEN,
}

export const orderDisplayTypeTextMap = {
  [EOrderDisplayType.EXPIRED]: t('已截止'),
  [EOrderDisplayType.AVAILABLE]: t('可预定'),
  [EOrderDisplayType.BOOKED]: t('已预定'),
  [EOrderDisplayType.COMPLETED]: t('已完成'),
  [EOrderDisplayType.NOT_OPEN]: t('未开放'),
};

export interface IMealTypeListItem {
  /** 如11:00~13:00 */
  timePeriod?: string;
  /** HH:mm */
  bookDeadlineTime?: string;
  /** 订单显示类型 */
  displayType?: EOrderDisplayType;
  /** 如果为null则没有预定 */
  orderDto?: IOrderDto;
  /** 餐段类型名称 */
  mealTimePeriodTypeName?: string;
  /** 餐段类型code */
  mealTimePeriodTypeCode?: number;
}

export interface IGetMealTypeListParams {
  /** 餐厅id */
  canteenId?: number;
  /** 时间 */
  date?: string;
}
