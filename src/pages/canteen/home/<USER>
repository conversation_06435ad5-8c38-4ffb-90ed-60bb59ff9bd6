import { useLocation } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import { queryStringParse, updateRouteQuery } from '@/utils/share';
import { t } from '@shein-bbl/react';
import { useRequest, useSetState } from 'ahooks';
import moment from 'moment';
import { Loading } from 'shineout-mobile';
import DateSelect from '../_/components/date-select';
import { getMealTypeList } from './api';
import { CanteenCascader, ICanteenCascaderValue } from './components/canteen-cascader';
import CanteenHeader from './components/canteen-header';
import MealPeriodCard from './components/meal-period-card';

const CanteenHome = (): React.ReactElement => {
  const location = useLocation();
  const routeQuery = queryStringParse(location.search) as {
    date: string;
  };
  const { date: homeDate } = routeQuery;

  const [formData, setFormData] = useSetState<{
    date: string;
    canteenCascader: ICanteenCascaderValue[];
  }>(() => {
    // 判断url参数的date是不是今天+未来6天期间内
    const isInFuture6Days =
      moment(homeDate).isSameOrAfter(moment(), 'day') &&
      moment(homeDate).isSameOrBefore(moment().add(6, 'days'), 'day');
    const date = homeDate && isInFuture6Days ? homeDate : moment().format('YYYY-MM-DD');
    return {
      date,
      canteenCascader: [],
    };
  });

  usePageTitle(t('园区食堂'));

  const {
    data: mealTypeList,
    run: runGetMealTypeList,
    loading,
  } = useRequest(getMealTypeList, {
    manual: true,
  });

  /**
   * @description 选择餐厅
   * @param {ICanteenCascaderValue[]} canteenCascader 餐厅
   */
  const handleChangeCanteen = (canteenCascader: ICanteenCascaderValue[]): void => {
    setFormData({ canteenCascader });
    runGetMealTypeList({
      canteenId: Number(canteenCascader[1].id),
      date: formData.date,
    });
  };

  const canteenId = formData.canteenCascader?.[1]?.id;

  /**
   * @description 选择日期
   * @param {string} date 日期
   */
  const handleChangeDate = (date: string): void => {
    updateRouteQuery(location.pathname, {
      date,
    });
    setFormData({ date });
    if (canteenId) {
      runGetMealTypeList({
        canteenId: canteenId as number,
        date,
      });
    }
  };

  return (
    <div className="min-h-screen bg-[#F5F6F7]">
      <CanteenHeader />
      <CanteenCascader value={formData.canteenCascader} onChange={handleChangeCanteen} />
      <DateSelect value={formData.date} onChange={handleChangeDate} />
      <div className="p-[12px]">
        {loading && (
          <div className="text-center">
            <Loading />
          </div>
        )}
        {!loading &&
          mealTypeList?.map((item) => (
            <MealPeriodCard
              key={item.mealTimePeriodTypeCode}
              data={item}
              canteenId={canteenId as number}
              date={formData.date}
            />
          ))}
      </div>
    </div>
  );
};

export default CanteenHome;
