import { get, post } from '@/utils/fetch';
import {
  IConsumerBalanceInfo,
  IConsumerQrcodeRequestBody,
  IConsumerResultResponse,
} from './interfaces';
// // 导入mock数据
// import getConsumerBalanceData from './mock/getConsumerBalanceAPI.json';
// import getConsumerResultData from './mock/getConsumerResultAPI.json';

/**
 * 定餐首页-消费码-二维码
 * https://soapi.sheincorp.cn/application/3694/routes/244340/doc
 * @returns
 */
export const getConsumerQrcodeAPI = (data: IConsumerQrcodeRequestBody) => {
  return post<string>('/canteen/staff/consumer/qrcode', data);
};

/**
 * 定餐首页-消费码-余额查询
 * https://soapi.sheincorp.cn/application/3694/routes/244341/doc
 * @returns
 */
export const getConsumerBalanceAPI = () => {
  // // 开发环境使用mock数据
  // if (process.env.NODE_ENV === 'development') {
  //   return Promise.resolve(getConsumerBalanceData);
  // }
  return get<IConsumerBalanceInfo>('/canteen/staff/consumer/balance');
};

/**
 * 定餐首页-消费码-被扫码结果
 * https://soapi.sheincorp.cn/application/3694/routes/245573/doc
 * @returns
 */
export const getConsumerResultAPI = () => {
  // // 开发环境使用mock数据
  // if (process.env.NODE_ENV === 'development') {
  //   return Promise.resolve(getConsumerResultData);
  // }
  return get<IConsumerResultResponse>('/canteen/staff/consumer/result');
};
