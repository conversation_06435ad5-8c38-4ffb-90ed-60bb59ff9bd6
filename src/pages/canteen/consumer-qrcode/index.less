/* stylelint-disable max-nesting-depth */
.container {
  position: relative;
  display: flex;
  height: 100vh;
  padding: 12px;
  flex-direction: column;

  .code {
    display: flex;
    height: 320px;
    background-color: white;
    border-radius: 4px;
    align-items: center;
    flex-direction: column;

    .qrCode {
      margin: 48px 0 0;
    }

    .subDes {
      display: flex;
      flex-direction: row;
      width: 280px;
      align-items: center;
      justify-content: space-between;
      text-align: center;

      p {
        padding: 14px;
        font-size: 12px;
        color: #666c7c;
        flex-grow: 1;
      }
    }
  }

  .walletInfo {
    overflow-y: auto;
    background-color: #f7f8fa;
    border-radius: 4px;
    flex-grow: 1;

    .walletCard {
      display: flex;
      padding: 12px;
      border-radius: 4px;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .title {
        display: flex;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        align-items: center;
      }

      .balanceRow {
        display: flex;
      }

      .balanceAmount {
        margin-right: 6px;
        font-size: 18px;
        font-weight: 500;
        color: #141737;
        flex-grow: 1;
      }

      .detailButton {
        font-size: 20px;
        color: #666c7c;
      }
    }
  }
}

.balanceDetailDialog {
  padding: 10px 0;

  .totalBalance {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;

    .label {
      font-weight: 500;
      color: #666c7c;
    }

    .value {
      font-weight: 500;
      color: #141737;
    }
  }

  .detailItem {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;

    .accountName {
      color: #666c7c;
    }

    .accountBalance {
      color: #141737;
    }
  }

  .dialogButton {
    width: 100%;
    margin-top: 20px;
  }
}
