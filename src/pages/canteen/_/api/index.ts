import { get, post } from '@/utils';
import {
  ICanteenDishEvaluate,
  ICanteenParkItem,
  ICanteenPayRequestBody,
  ICanteenPayResponse,
  IConsumerBalanceInfo,
} from '../interfaces';
import { IOrderDetail } from '../interfaces/order';

/**
 * @description 获取园区食堂级联列表
 * @returns {Promise<ICanteenParkItem[]>} 园区食堂级联列表
 */
export const getParkCanteenTree = (): Promise<ICanteenParkItem[]> => {
  return get<ICanteenParkItem[]>('/canteen/staff/home/<USER>');
};

/**
 * @description 食堂支付
 * @param {ICanteenPayRequestBody} body 请求体
 * @returns {Promise<ICanteenPayResponse>} 响应体
 */
export const payCanteenOrder = (body: ICanteenPayRequestBody): Promise<ICanteenPayResponse> => {
  return post<ICanteenPayResponse>('/canteen/staff/order/pay', body);
};

/**
 * @description 取消订单
 * @param {string} orderNo 订单ID
 * @returns {Promise<void>} 响应体
 */
export const cancelCanteenOrder = (orderNo: string): Promise<void> => {
  return post<void>('/canteen/staff/order/cancel', { orderNo });
};

/**
 * @description 获取食堂菜品评价
 * @param {string} orderId 订单ID
 * @returns {Promise<ICanteenDishEvaluate>} 食堂菜品评价
 */
export const getCanteenDishEvaluate = (orderId: string): Promise<ICanteenDishEvaluate> => {
  return get<ICanteenDishEvaluate>('/canteen/staff/evaluation/detail', { orderId });
};

/**
 * @description 获取订单详情
 * @param {object} params
 * @param {string} params.orderId - 订单ID
 * @returns {Promise<IOrderDetail>}
 */
export const getOrderDetail = (params: { orderId: string }): Promise<IOrderDetail> => {
  return get('/canteen/staff/order/detail', params);
};

/**
 * @description 定餐首页-消费码-余额查询
 * @returns {Promise<IConsumerBalanceInfo>} 响应体
 */
export const getConsumerBalanceAPI = (): Promise<IConsumerBalanceInfo> => {
  return get<IConsumerBalanceInfo>('/canteen/staff/consumer/balance');
};
