.card {
  padding: 12px;
  background: #fff;
  border-radius: 4px;
}

.header {
  padding: 12px;
  background-color: #fff;

  .title {
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #141737;
  }

  .info {
    display: flex;
    align-items: center;
    margin-top: 6px;
    color: #999da8;

    &Value {
      margin-left: 4px;
      font-size: 14px;
      line-height: 22px;
      color: #666c7c;
    }
  }
}

.dishList {
  padding: 12px 0;
  border-top: 1px solid #e8ebf0;
  border-bottom: 1px solid #e8ebf0;
}

.dishItem {
  display: flex;
  align-items: flex-start;

  & + & {
    margin-top: 8px;
  }

  .dishImg {
    width: 48px;
    height: 48px;
    margin-right: 12px;
    border-radius: 4px;
    object-fit: cover;
  }

  .dishRight {
    flex: 1;
    overflow: hidden;
  }

  .dishInfo {
    display: flex;
    margin-bottom: 4px;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #141737;
    justify-content: space-between;
    align-items: center;
    gap: 8px;

    .dishName {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .dishPrice {
      white-space: nowrap;
    }
  }

  .dishCount {
    overflow: hidden;
    font-size: 14px;
    line-height: 22px;
    color: #666c7c;
  }
}

.total {
  margin-top: 12px;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: #141737;
  text-align: right;

  .totalPrice {
    margin-left: 4px;
    font-size: 16px;
    line-height: 24px;
  }
}
