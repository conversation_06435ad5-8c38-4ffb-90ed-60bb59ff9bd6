import React, { useMemo } from 'react';
import Icon from '@/_/components/Icon';
import { t } from '@shein-bbl/react';
import numeral from 'numeral';
import { IOrderDetailItem } from '../../interfaces/order';
import { formatDishDate } from '../../utils';
import styles from './index.less';

interface OrderCardProps {
  mealTimePeriodTypeName: string;
  date: string;
  canteenName: string;
  stallName: string;
  dishes?: IOrderDetailItem[];
}

/**
 * @description 订单卡片
 * @param {OrderCardProps} props 订单卡片props
 * @returns {React.ReactElement} 订单卡片
 */
const OrderCard = ({
  mealTimePeriodTypeName,
  date,
  canteenName,
  stallName,
  dishes,
}: OrderCardProps): React.ReactElement => {
  const { totalAmount } = useMemo(() => {
    const totalAmount =
      dishes?.reduce((acc, curr) => {
        const price =
          numeral(curr.dishNum || 0)
            .multiply(curr.dishPrice || 0)
            .value() || 0;
        return numeral(acc).add(price).value();
      }, 0) || 0;
    return { totalAmount };
  }, [dishes]);
  return (
    <div className={styles.card}>
      <div className={styles.header}>
        <div className={styles.title}>{mealTimePeriodTypeName}</div>
        <div className={styles.info}>
          <Icon name="m-schedule-shineout-fill" fontSize={16} />
          <span className={styles.infoValue}>{formatDishDate(date)}</span>
        </div>
        <div className={styles.info}>
          <Icon name="m-location-shineout-fill" fontSize={16} />
          <span className={styles.infoValue}>{canteenName}</span>
        </div>
        <div className={styles.info}>
          <Icon name="m-home-house-shineout-fill" fontSize={16} />
          <span className={styles.infoValue}>{stallName}</span>
        </div>
      </div>
      <div className={styles.dishList}>
        {dishes?.map((dish) => (
          <div className={styles.dishItem} key={JSON.stringify(dish)}>
            <img src={dish.dishPic} alt={dish.dishName} className={styles.dishImg} />
            <div className={styles.dishRight}>
              <div className={styles.dishInfo}>
                <div className={styles.dishName}>{dish.dishName}</div>
                <div className={styles.dishPrice}>
                  ¥
                  {numeral(dish.dishPrice || 0)
                    .multiply(dish.dishNum || 0)
                    .value()}
                </div>
              </div>
              <div className={styles.dishCount}>x{dish.dishNum}</div>
            </div>
          </div>
        ))}
      </div>
      <div className={styles.total}>
        <span>{t('合计')}</span>
        <span className={styles.totalPrice}>¥{totalAmount}</span>
      </div>
    </div>
  );
};

export default OrderCard;
