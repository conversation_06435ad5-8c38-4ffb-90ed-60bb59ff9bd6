import React from 'react';
import { useObjectState } from '@/_/hooks';
import classNames from 'classnames';
import { ImagePreviewer } from 'shineout-mobile';

interface IAttachmentListProps {
  data: string[];
  className?: string;
}

/**
 * @description ImageView
 * @param {IAttachmentListProps} props desc
 * @returns {React.ReactElement} ReactElement
 */
const ImageView = (props: IAttachmentListProps): React.ReactElement => {
  const { data, className } = props;
  const [state, setState] = useObjectState({
    showImagePreview: false,
    currentPreviewImage: '',
  });

  return (
    <>
      <div className={classNames('sm-upload', className)}>
        {(data || [])?.map((item, index) => (
          <div
            className="sm-upload-image"
            /* eslint-disable-next-line react/no-array-index-key */
            key={index}
            onClick={() =>
              setState({
                showImagePreview: true,
                currentPreviewImage: item,
              })
            }
          >
            <img src={item} alt="" />
          </div>
        ))}
      </div>
      <ImagePreviewer
        images={[state.currentPreviewImage]}
        visible={state.showImagePreview}
        onClose={() =>
          setState({
            showImagePreview: false,
          })
        }
      />
    </>
  );
};

export default ImageView;
