.container {
  display: flex;
  height: 100%;
  background: #fff;
  flex-direction: column;
}

.content {
  padding: 16px 24px;
  overflow-x: hidden;
  overflow-y: auto;
  flex: 1;

  .contentBody {
    display: flex;
    min-height: 100%;
    flex-direction: column;
    justify-content: flex-end;
  }

  &Empty {
    font-size: 36px;
    font-weight: 500;
    line-height: 44px;
    color: #b3b7c1;
  }

  .expression {
    font-size: 36px;
    font-weight: 500;
    line-height: 44px;
    color: #141737;
  }

  .result {
    margin-top: 16px;
    font-size: 36px;
    font-weight: 500;
    line-height: 44px;
    color: #f56c0a;
  }
}

.operation {
  padding: 8px;
  background: #f4f5f8;
}

.buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.btn {
  display: flex;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #141737;
  cursor: pointer;
  background: #fff;
  border: none;
  border-radius: 4px;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;

  &:active {
    background: #e8ebf0;
  }

  &Shortcut {
    height: 32px;
  }

  &Clear {
    flex: 1;
  }

  &Backspace {
    flex: 1;
    font-size: 24px;
  }

  &Plus {
    grid-row: span 2;
    font-size: 24px;
  }

  &Zero {
    grid-column: span 2;
  }

  &Confirm {
    color: #fff;
    background: #197afa;
    grid-row: span 3;

    &:active {
      background: #0b5bd4;
    }

    &Disabled {
      background: #94cdff;
    }
  }
}

.keypad {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(5, 48px);
  gap: 8px;
  width: 100%;
  margin: 0 auto;

  .deleteArea {
    grid-column: span 3;
    display: flex;
    gap: 8px;
  }
}
