import React, { useEffect, useMemo, useRef } from 'react';
import Icon from '@/_/components/Icon';
import { canteenQuickAmountStorage } from '@/_/utils/storage';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';
import numeral from 'numeral';
import styles from './index.less';

interface IAdditionCalculatorProps {
  onConfirm?: (v: number) => void;
  input?: string;
  setInput?: (v: string) => void;
}

/**
 * @description 格式化输入
 * @param {string} input
 * @returns {string} 格式化后的输入
 */
const formatInput = (input: string): string => {
  return input.replace(/\+/g, ' + ');
};

interface IKeypadGridItem {
  key: string;
  className?: string;
  label?: React.ReactNode;
  children?: IKeypadGridItem[];
}

const keypadGrid: IKeypadGridItem[] = [
  {
    key: 'delete',
    className: styles.deleteArea,
    children: [
      { key: 'clear', label: t('清空'), className: styles.btnClear },
      { key: 'backspace', label: <Icon name="delete-lift" />, className: styles.btnBackspace },
    ],
  },
  { key: '+', label: '+', className: styles.btnPlus },
  { key: '1', label: '1' },
  { key: '2', label: '2' },
  { key: '3', label: '3' },
  { key: '4', label: '4' },
  { key: '5', label: '5' },
  { key: '6', label: '6' },
  { key: 'confirm', label: t('确定') },
  { key: '7', label: '7' },
  { key: '8', label: '8' },
  { key: '9', label: '9' },
  { key: '0', label: '0', className: styles.btnZero },
  { key: '.', label: '.' },
];

/**
 * @description 加法计算器
 * @param {IAdditionCalculatorProps} props
 * @returns {React.ReactElement} 加法计算器
 */
const AdditionCalculator = ({
  onConfirm,
  input: rawInput,
  setInput,
}: IAdditionCalculatorProps): React.ReactElement => {
  const input = String(rawInput || '');
  const buttons = useMemo(() => {
    const amountList = canteenQuickAmountStorage.getItem();
    if (amountList) {
      return amountList.map(Number).filter(Boolean);
    }
    return [];
  }, []);

  /**
   * @description 快捷金额按钮点击
   * @param {number} val
   */
  const handleButtonClick = (val: number) => {
    setInput(input ? `${input}+${val}` : String(val));
  };

  /**
   * @description 键盘点击
   * @param {string} key
   */
  const handleKeyClick = (key: string) => {
    if (key === 'clear') {
      setInput('');
      return;
    }
    if (key === 'backspace') {
      setInput(input.slice(0, -1));
      return;
    }
    if (key === '+') {
      if (input && /[0-9.]$/.test(input)) setInput(input + '+');
      return;
    }
    if (key === '.') {
      if (!input) {
        setInput('0.');
      } else if (!input.split('+').pop()?.includes('.')) {
        // 判断最后一个加数是否已经有小数点
        setInput(input + '.');
      }
      return;
    }
    if (key === 'confirm') {
      setInput('');
      return;
    }
    setInput(input + key);
  };

  const result = useMemo(() => {
    const parts = input
      .split('+')
      .map((s) => s.trim())
      .filter(Boolean);
    let sum = numeral(0);
    parts.forEach((p) => {
      sum = sum.add(p);
    });
    return sum.value() ? numeral(sum.value()).format('0,0[.]00') : '';
  }, [input]);

  const contentRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  }, [input]);

  /**
   * @description 渲染键盘按钮
   * @param {IKeypadGridItem} item
   * @returns {React.ReactNode} 键盘按钮
   */
  const renderKeypadButton = (item: IKeypadGridItem): React.ReactNode => {
    if (item.children) {
      return (
        <div key={item.key} className={item.className}>
          {item.children.map((child) => renderKeypadButton(child))}
        </div>
      );
    }
    if (item.key === 'confirm') {
      return (
        <button
          key={item.key}
          className={classNames(styles.btn, styles.btnConfirm, {
            [styles.btnConfirmDisabled]: !result,
          })}
          onClick={() => {
            if (result) {
              onConfirm?.(Number(result));
            }
          }}
        >
          {t('确定')}
        </button>
      );
    }
    return (
      <button
        key={item.key}
        className={`${styles.btn} ${item.className || ''}`}
        onClick={() => handleKeyClick(item.key)}
      >
        {item.label}
      </button>
    );
  };

  return (
    <div className={styles.container}>
      {/* 主内容区 */}
      <div className={styles.content} ref={contentRef}>
        <div className={styles.contentBody}>
          {input ? (
            <>
              <div className={styles.expression}>{formatInput(input)}</div>
              {result && <div className={styles.result}>{result}</div>}
            </>
          ) : (
            <div className={styles.contentEmpty}>{t('请输入金额')}</div>
          )}
        </div>
      </div>
      <div className={styles.operation}>
        {/* 快捷金额按钮区 */}
        {buttons?.length ? (
          <div className={styles.buttons}>
            {buttons.map((val: number) => (
              <button
                key={val}
                className={`${styles.btn} ${styles.btnShortcut}`}
                onClick={() => handleButtonClick(val)}
              >
                ¥{val}
              </button>
            ))}
          </div>
        ) : null}
        {/* 键盘区 */}
        <div className={styles.keypad}>{keypadGrid.map((item) => renderKeypadButton(item))}</div>
      </div>
    </div>
  );
};

export default AdditionCalculator;
