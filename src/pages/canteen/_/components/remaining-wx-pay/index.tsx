import { cancelCanteenOrder } from '@/pages/canteen/_/api';
import { t } from '@shein-bbl/react';
import { useRequest } from 'ahooks';
import { <PERSON><PERSON>, Drawer } from 'shineout-mobile';
import styles from './index.less';

interface RemainingPayProps {
  visible: boolean;
  onConfirm: () => void;
  onCancelSuccess?: () => void;
  loading?: boolean;
  margin?: number;
  totalAmount?: number;
  walletPayAmount?: number;
  orderNo?: string;
}

/**
 * @description 微信支付差额
 * @param {RemainingPayProps} props 参数
 * @returns {React.ReactElement} RemainingPay
 */
const RemainingWXPay = ({
  visible,
  onCancelSuccess,
  onConfirm,
  margin,
  totalAmount,
  walletPayAmount,
  orderNo,
  loading,
}: RemainingPayProps): React.ReactElement => {
  const { runAsync: cancelOrder, loading: cancelOrderLoading } = useRequest(cancelCanteenOrder, {
    manual: true,
  });
  /**
   * @description 取消
   * @returns {void}
   */
  const handleCancel = (): void => {
    cancelOrder(orderNo).then(() => {
      onCancelSuccess?.();
    });
  };
  return (
    <Drawer round height="auto" visible={visible}>
      <div className={styles.remainingPayBox}>
        <div className={styles.remainingPayTitle}>
          {t('钱包可用余额不足，是否使用微信支付差额')}
          <span className={styles.remainingPayAmount}>¥{margin}</span>
        </div>
        <div className={styles.remainingPayList}>
          <div className={styles.remainingPayItem}>
            <span>{t('合计需支付')}</span>
            <span className={styles.remainingPayValue}>¥{totalAmount}</span>
          </div>
          <div className={styles.remainingPayItem}>
            <span>{t('钱包余额')}</span>
            <span className={styles.remainingPayValue}>¥{walletPayAmount}</span>
          </div>
          <div className={styles.remainingPayItem}>
            <span>{t('微信支付差额')}</span>
            <span className={styles.remainingPayDiff}>¥{margin}</span>
          </div>
        </div>
        <Button
          className={styles.remainingPayBtn}
          type="primary"
          loading={loading}
          onClick={onConfirm}
        >
          {t('使用微信支付差额')}
        </Button>
        <Button
          className={styles.remainingPayCancel}
          onClick={handleCancel}
          loading={cancelOrderLoading}
        >
          {t('取消')}
        </Button>
      </div>
    </Drawer>
  );
};

export default RemainingWXPay;
