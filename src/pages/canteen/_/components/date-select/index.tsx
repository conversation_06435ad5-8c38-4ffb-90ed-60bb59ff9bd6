import React, { useMemo } from 'react';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';
import moment from 'moment';
import styles from './index.less';

/**
 * @description 获取日期标签
 * @param {number} index 索引
 * @param {moment.Moment} date 日期
 * @returns {string} 日期标签
 */
const getLabel = (index: number, date: moment.Moment): string => {
  if (index === 0) return t('今天');
  if (index === 1) return t('明天');
  if (index === 2) return t('后天');
  const weekMap = [t('周日'), t('周一'), t('周二'), t('周三'), t('周四'), t('周五'), t('周六')];
  return weekMap[date.day()];
};

interface DateSelectProps {
  value: string;
  onChange: (value: string) => void;
}
/**
 * @description 日期选择器
 * @param {DateSelectProps} props 属性
 * @returns {React.ReactElement} 日期选择器
 */
const DateSelect = ({ value, onChange }: DateSelectProps): React.ReactElement => {
  const days = useMemo(() => {
    return Array.from({ length: 7 }).map((_, i) => {
      const date = moment().add(i, 'day');
      return {
        date: date.format('YYYY-MM-DD'),
        day: date.date(),
        label: getLabel(i, date),
      };
    });
  }, []);

  return (
    <div className={styles.dateSelect}>
      {days.map((item, idx) => (
        <div
          key={item.date}
          className={classNames(
            styles.dateItem,
            idx === 0 && styles.dateItemToday,
            value === item.date && styles.dateItemSelected,
          )}
          onClick={() => onChange(item.date)}
        >
          <div className={styles.dateDay}>{item.day}</div>
          <div>{item.label}</div>
        </div>
      ))}
    </div>
  );
};

export default DateSelect;
