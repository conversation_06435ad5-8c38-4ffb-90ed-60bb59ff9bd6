export interface ICanteenOptionItem {
  /** 食堂供应商id */
  id?: number;
  /** 食堂名称 */
  canteenName?: string;
}

export interface ICanteenParkItem {
  /** 园区id */
  parkId?: number;
  /** 园区名称 */
  parkName?: string;
  canteenVos: ICanteenOptionItem[];
}

export interface IDishListItem {
  /** id */
  id?: number;
  /** 菜品名称 */
  dishName?: string;
  /** 菜品图片 */
  dishPic?: string;
  /** 菜品价格 */
  dishPrice?: number;
  /** 月销 */
  salesAmount?: number;
}

export interface IDishListItemWithAmount extends IDishListItem {
  /** 加购数量 */
  buyCount: number;
}

/** 支付渠道类型 */
export enum ECanteenPayChannelType {
  /** 虚拟钱包 */
  VIRTUAL_WALLET = 1,
  /** 微信 */
  WECHAT = 2,
}

/** 终端类型 */
export enum ECanteenTerminalType {
  /** iOS */
  IOS = 1,
  /** Android */
  ANDROID = 2,
  /** Wap */
  WAP = 3,
}

export interface ICanteenPayRequestBody {
  /** 食堂订单号 */
  orderNo: string;
  /** 支付渠道类型 */
  payChannelType: ECanteenPayChannelType;
  /** 终端类型 */
  terminalType: ECanteenTerminalType;
}

export interface ICanteenPayResponse {
  /** 订单id */
  id?: number;
  /** 订单号 */
  orderNo?: string;
  /** 订单总金额 */
  totalAmount?: number;
  /** 钱包支付金额 */
  walletPayAmount?: number;
  /** 差额 */
  margin?: number;
  /** 微信支付单号 */
  weChatPayOrderNo?: string;
  /** 钱包支付单号 */
  walletOderNo?: string;
  /** 微信支付链接 */
  weChatPayUrl?: string;
}

export interface ICanteenDishEvaluate {
  /** 餐段类型名称 */
  mealTimePeriodTypeName?: string;
  /** yyyy-MM-dd */
  date?: string;
  /** 食堂名称 */
  canteenName?: string;
  /** 档口名称 */
  stallName?: string;
  /** 0为未评价，1-5已评价 */
  evaluateScore?: number;
  /** 评价内容 */
  evaluateContent?: string;
  /** 评价图片 */
  evaluatePic?: string[];
}

interface IConsumerBalanceInfoDetailItem {
  /** 账户名称 */
  accountName?: string;
  /** 账户金额 */
  accountBalance?: number;
}

export interface IConsumerBalanceInfo {
  /** 总余额 */
  total?: number;
  /** 详情 */
  detail?: IConsumerBalanceInfoDetailItem[];
}
