import { t } from '@shein-bbl/react';
import moment from 'moment';
const weekMap = [t('周日'), t('周一'), t('周二'), t('周三'), t('周四'), t('周五'), t('周六')];

/**
 * @description 格式化日期 4月11日(今天) 周一
 * @param {string} date 日期
 * @returns 格式化后的日期
 */
export const formatDishDate = (date: string) => {
  const day = [t('今天'), t('明天'), t('后天')].find((_, index) =>
    moment(date).isSame(moment().add(index, 'day'), 'day'),
  );
  // eslint-disable-next-line @shein-bbl/bbl/translate-i18n-byT
  return `${moment(date).format('M月DD日')}${day ? `(${day})` : ''} ${weekMap[moment(date).day()]}`;
};
