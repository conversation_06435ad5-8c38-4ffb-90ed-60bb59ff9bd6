import { useLocation } from 'react-router-dom';
import { getHostByEnv, queryStringParse, stringify } from '@/utils';
import { useRequest } from 'ahooks';
import { payCanteenOrder } from '../api';
import { ECanteenPayChannelType, ECanteenTerminalType, ICanteenPayResponse } from '../interfaces';

/**
 * @description 获取终端类型
 * @returns {ECanteenTerminalType} 终端类型
 */
const getTerminalType = (): ECanteenTerminalType => {
  const u = navigator.userAgent;
  const isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; // android终端
  const isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
  if (isAndroid) {
    return ECanteenTerminalType.ANDROID;
  }
  if (isIOS) {
    return ECanteenTerminalType.IOS;
  }
  return ECanteenTerminalType.WAP;
};

export function useCanteenPay() {
  const { runAsync: doPay, loading: payLoading } = useRequest(payCanteenOrder, {
    manual: true,
  });
  const location = useLocation();
  const query = queryStringParse(location.search) as Record<string, string>;

  /**
   * @description 钱包支付
   * @param orderNo 订单号
   * @returns {Promise<ICanteenPayResponse>}
   */
  const handlePayWallet = (orderNo: string): Promise<ICanteenPayResponse> => {
    return doPay({
      orderNo,
      payChannelType: ECanteenPayChannelType.VIRTUAL_WALLET,
      terminalType: getTerminalType(),
    });
  };

  /**
   * @description 微信支付
   * @param orderNo 订单号
   * @param params 参数
   * @returns {Promise<void>}
   */
  const handlePayWechat = (orderNo: string, params?: Record<string, any>): Promise<void> => {
    const queryString = stringify({ ...query, from: 'redirect', ...params });
    return doPay({
      orderNo,
      payChannelType: ECanteenPayChannelType.WECHAT,
      terminalType: getTerminalType(),
    }).then((res) => {
      window.location.replace(
        res.weChatPayUrl +
          `&redirect_url=${encodeURIComponent(
            `${getHostByEnv()}#${location.pathname}?${queryString}`,
          )}`,
      );
    });
  };

  return {
    payLoading,
    handlePayWallet,
    handlePayWechat,
  };
}
