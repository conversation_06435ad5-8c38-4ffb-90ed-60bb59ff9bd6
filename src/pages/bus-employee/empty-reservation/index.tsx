import { useCallback, useEffect, useState } from 'react';
import { usePageTitle } from '@/_/hooks';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import { useMount } from 'ahooks';
import { <PERSON><PERSON>, <PERSON>r, Dialog, Popover, Toast } from 'shineout-mobile';
import EmptyBus from '../components/_/EmptyBus';
import LoadingBus from '../components/_/LoadingBus';
import FormItem from '../components/AppointmentForm/components/FormItem';
import EmptyReservationItem from '../components/EmptyReservationItem';
import styles from './index.less';
import {
  IAreaLineItem,
  IAreaLineOptionsType,
  IEmptyReservationItem as EmptyReservationItemType,
} from './interfaces';
import { freeSeatReserve, getAreaLine, getLatestLine, getWorkingScheduleList } from './services';

const EmptyReservationPage = () => {
  usePageTitle(t('空座预订'));
  const [currentLineArr, setCurrentLineArr] = useState<string[]>([]);
  const [currentLine, setCurrentLine] = useState<IAreaLineItem>();
  const [areaLineOptions, setAreaLineOptions] = useState<IAreaLineOptionsType>([]);
  const [areaLineOptionsOriginal, setAreaLineOptionsOriginal] = useState<IAreaLineItem[]>([]);
  const [list, setList] = useState<EmptyReservationItemType[]>([]);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [popoverVisible, setPopoverVisible] = useState(false);

  const getLine = useCallback(() => {
    setLoading(true);
    getAreaLine()
      .then((lineList) => {
        setAreaLineOptionsOriginal(lineList);
        setAreaLineOptions(
          lineList.map((item) => ({
            id: 'p-' + item.id,
            name: item.name,
            children: item.children.map((c) => ({
              id: 'c-' + c.id,
              name: c.name,
            })),
          })),
        );
        getLatestLine().then((res) => {
          if (res?.lineStatus === 0 || res === null) {
            setVisible(true);
            return;
          }
          setCurrentLine({
            id: res.lineId,
            name: res.lineName,
          });
          const aid = lineList.find((item) => item.children.some((c) => c.id === res.lineId))?.id;
          setCurrentLineArr(['p-' + aid, 'c-' + res.lineId]);
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  useMount(() => {
    getLine();
  });

  useEffect(() => {
    if (currentLine?.id) {
      setLoading(true);
      getWorkingScheduleList({
        lineId: currentLine?.id,
      })
        .then((listRes) => {
          setList(listRes);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [currentLine?.id]);

  /**
   * @description handleClose
   * @returns {unknown} desc
   */
  const handleClose = () => {
    setVisible(false);
    setPopoverVisible(false);
    setCurrentLineArr([]);
  };

  useEffect(() => {
    if (visible) {
      requestAnimationFrame(() => {
        const e = document.querySelector('.sm-cascader-header-close');
        e.addEventListener('click', handleClose);
      });
    }
    return () => {
      const e = document.querySelector('.sm-cascader-header-close');
      e?.removeEventListener('click', handleClose);
    };
  }, [visible]);

  const handleReserve = useCallback(
    (id, scheduleTime) => {
      Dialog.confirm({
        title: t('信息确认'),
        message: (
          <div style={{ textAlign: 'left' }}>
            <div>
              {t('用车时间：')}
              {scheduleTime}
            </div>
            <div>
              {t('用车线路：')}
              {currentLine?.name}
            </div>
            <div>{t('抢座说明：①预订成功无法取消 ②如未扫码上车将被记录1次违约')} </div>
          </div>
        ),
        cancelButtonText: t('我再想想'),
        confirmButtonText: t('立即预订'),
        onOk: () => {
          freeSeatReserve(id).then(() => {
            Toast.success(t('预约成功'));
            setLoading(true);
            getWorkingScheduleList({
              lineId: currentLine?.id,
            })
              .then((listRes) => {
                setList(listRes);
              })
              .finally(() => {
                setLoading(false);
              });
          });
        },
      });
    },
    [currentLine?.id, currentLine?.name],
  );

  return (
    <div className={styles.container}>
      <Cascader
        visible={visible}
        style={{ marginBottom: 70 }}
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        title={
          <span>
            {t('请选择线路')}
            <span>
              <Icon type="primary" style={{ marginRight: '12px' }} name="info-circle" />
              <Popover
                position="top-left"
                visible={popoverVisible}
                onVisibleChange={(v) => setPopoverVisible(v)}
              >
                <div style={{ padding: 12, color: 'red' }}>
                  {t(
                    '说明：系统将默认为您展示可空座预订的线路（含座位已满），其他不可预订的线路自动过滤',
                  )}
                </div>
              </Popover>
            </span>
          </span>
        }
        keygen="id"
        format="id"
        renderItem="name"
        data={areaLineOptions}
        value={currentLineArr}
        disabled={(item) => item.status === 0}
        onChange={(v) => {
          setCurrentLineArr(v);
          if (v?.[1]) {
            const aid = areaLineOptions.findIndex((item) =>
              item.children.some((c) => c.id === v[1]),
            );
            setCurrentLine(
              areaLineOptionsOriginal[aid].children.find(
                (item) => item.id === Number(v[1].slice(2)),
              ),
            );
            setPopoverVisible(false);
            setVisible(false);
          }
          if (v?.[0]) {
            const c = areaLineOptions.find((item) => item.id === v[0])?.children;
            if ((c || []).length === 0) {
              setPopoverVisible(true);
            } else {
              setPopoverVisible(false);
            }
          }
        }}
      >
        <FormItem
          label={t('线路')}
          icon="m-route"
          isLink
          value={<div style={{ marginTop: 1 }}>{currentLine ? currentLine.name : t('请选择')}</div>}
          onClick={() => {
            setVisible(true);
          }}
        />
      </Cascader>

      {visible && (
        <div
          style={{
            width: '100%',
            maxWidth: '750px',
            height: 70,
            background: 'white',
            position: 'absolute',
            bottom: 0,
            left: 0,
            zIndex: 9999,
            padding: 12,
            border: null,
          }}
        >
          <Button type="default" style={{ width: '100%', maxWidth: '750px' }} onClick={handleClose}>
            {t('取消')}
          </Button>
        </div>
      )}

      {!loading &&
        list?.map((item) => (
          <EmptyReservationItem
            key={item.id}
            item={item}
            onReserve={(id) => {
              handleReserve(id, item.scheduleTime);
            }}
          />
        ))}

      {!loading && (
        <div className={styles.tips}>
          <p>{t('说明：')}</p>
          <p>{t('1、“空座预订”仅限距离发车前45分钟的班次')}</p>
          <p>{t('2、每人每天拥有两次“空座预订”机会')}</p>
        </div>
      )}

      {!loading && list.length === 0 && <EmptyBus />}
      {loading && <LoadingBus />}
    </div>
  );
};

export default EmptyReservationPage;
