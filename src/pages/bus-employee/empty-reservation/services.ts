import { get, post } from '@/utils';
import { IAreaLineItem, IEmptyReservationItem } from './interfaces';

/**
 * 空座预订-获取区域线路下拉选项（仅返回启用区域和线路）
 * https://soapi.sheincorp.cn/application/3694/routes/184582/doc
 * @returns
 */
export const getAreaLine = () => {
  return get<IAreaLineItem[]>('/bsms/staff/area/selectAreaLine');
};

/**
 * 空座预订-获取空座排班
 * https://soapi.sheincorp.cn/application/3694/routes/184586/doc
 * @param params
 * @returns
 */
export const getWorkingScheduleList = (params: { lineId: number }) => {
  return post<IEmptyReservationItem[]>('/bsms/staff/workingSchedule/selectWorkingSchedule', params);
};

/**
 * 空座预订
 * https://soapi.sheincorp.cn/application/3694/routes/184591/doc
 * @param id
 * @returns
 */
export const freeSeatReserve = (id: number) => {
  return get<boolean>('/bsms/staff/workingSchedule/freeSeatReserve', {
    id,
  });
};

/**
 * 空座预订-获取上次空座预订的线路
 * https://soapi.sheincorp.cn/application/3694/routes/185396/doc
 */
export const getLatestLine = () => {
  return get<{
    lineId: number;
    lineName: string;
    lineStatus?: number;
  }>('/bsms/staff/appointmentRecord/selectLastLine');
};
