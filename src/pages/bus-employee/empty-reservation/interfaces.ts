export interface IAreaLineItem {
  id: number;
  name: string;
  children?: IAreaLineItem[];
}

export enum ReserveTypeEnum {
  /** 可预定 */
  CAN_RESERVE = 0,
  /** 已预订 */
  RESERVED = 1,
  /** 座位已满 */
  SEAT_FULL = 2,
}

export interface IEmptyReservationItem {
  /** 排班id */
  id?: number;
  /** 班次 */
  scheduleTime?: string;
  /** 空闲座位 */
  freeSeat?: number;
  /** 预定类型：0-可预定，1-已预订，2-座位已满 */
  reserveType?: ReserveTypeEnum;
}

export type IAreaLineOptionsType = {
  id: string;
  name: string;
  children: {
    id: string;
    name: string;
  }[];
}[];
