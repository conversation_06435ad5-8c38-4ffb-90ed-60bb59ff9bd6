import { useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Page } from '@/_/components';
import Form from '@/_/components/Form';
import { usePageTitle } from '@/_/hooks';
import Row from '@/pages/bus-employee/components/_/Row';
import { EMPTY_STR } from '@/share';
import { t } from '@shein-bbl/react';
import { useMount, useRequest } from 'ahooks';
import { Button, Cell, Field, Icons, Rate, Toast } from 'shineout-mobile';
import ImageUpload from './components/image-upload';
import ImageView from './components/image-view';
import type { IRideRecordDetail, IRideRecordEvaluationReBody } from './interfaces';
import { getRideRecordDetail, submitRideRecordEvaluation } from './services';

const StarRate = Rate(<Icons name="no-star" />, <Icons name="star" />);

type IRideRecordEvaluationFormData = Omit<IRideRecordEvaluationReBody, 'id'>;

const TravelEvaluation = () => {
  usePageTitle(t('乘车评价'));
  const [detail, setDetail] = useState<IRideRecordDetail>({});
  const [formData, setFormData] = useState<IRideRecordEvaluationFormData>({});
  const [isScored, setIsScored] = useState(true);
  const { id } = useParams<'id'>();
  useMount(() => {
    if (!id) {
      Toast.fail(t('访问参数错误'));
      return;
    }
    getRideRecordDetail({ id }).then((res) => {
      setDetail(res);
      const isScored = res.score > 0;
      setIsScored(isScored);
      if (isScored) {
        setFormData({
          score: res.score,
          content: res.content || '',
          pictureList: res.pictureList || [],
        });
      }
    });
  });

  const navigate = useNavigate();
  const handleBack = () => {
    navigate('/bus-employee/travel-record', { replace: true });
  };

  const submitButtonRef = useRef<HTMLButtonElement>(null);
  const { loading, runAsync } = useRequest(submitRideRecordEvaluation, {
    manual: true,
  });

  /**
   * @description handleSubmit
   * @param {IRideRecordEvaluationFormData} values 参数
   * @returns {void}
   */
  const handleSubmit = (values: IRideRecordEvaluationFormData): void => {
    runAsync({
      ...values,
      id,
    }).then(() => {
      Toast.success(t('评价提交成功'));
      handleBack();
    });
  };

  return (
    <Page
      contentClassName="p-4 flex flex-col"
      footer={
        isScored ? (
          <Button style={{ width: '100%' }} onClick={handleBack}>
            {t('关闭')}
          </Button>
        ) : (
          <div className="flex gap-8">
            <Button className="flex-1" onClick={handleBack}>
              {t('取消')}
            </Button>
            <Button
              className="flex-1"
              type="primary"
              onClick={() => submitButtonRef.current?.click()}
              loading={loading}
            >
              {t('提交评价')}
            </Button>
          </div>
        )
      }
    >
      <div className="bg-white p-4 mb-4">
        <Row label={t('预约单号')} value={detail?.appointmentNum || EMPTY_STR} />
        <Row label={t('发车时间')} value={detail?.appointmentSchedule || EMPTY_STR} />
        <Row
          label={t('线路名称')}
          value={
            detail?.departureSiteName && detail?.destinationSiteName
              ? `${detail.departureSiteName}—${detail.destinationSiteName}`
              : EMPTY_STR
          }
        />
      </div>
      <Form values={formData} onSubmit={handleSubmit}>
        <Form.Field name="score" rules={{ required: t('请选择') }}>
          {({ value, onChange, errorMessage }) => (
            <Cell
              label={t('乘车评分')}
              required
              errorMessage={errorMessage}
              valueAlign="left"
              value={<StarRate value={value} onChange={onChange} disabled={isScored} />}
            />
          )}
        </Form.Field>
        <Form.Field name="content">
          {({ value, onChange, errorMessage }) => (
            <Field
              label={t('评价内容')}
              className="pt-0"
              type="textarea"
              placeholder={t('请输入')}
              showWordLimit
              maxLength={100}
              value={value}
              onChange={onChange}
              errorMessage={errorMessage}
              readonly={isScored}
              clearable={!isScored}
            />
          )}
        </Form.Field>
        <Form.Field name="pictureList">
          {({ value, onChange, errorMessage }) => (
            <Cell
              label={t('上传图片')}
              errorMessage={errorMessage}
              value={
                isScored ? (
                  <ImageView data={value} />
                ) : (
                  <ImageUpload value={value} onChange={onChange} />
                )
              }
            />
          )}
        </Form.Field>
        <button type="submit" ref={submitButtonRef} className="hidden" />
      </Form>
    </Page>
  );
};

export default TravelEvaluation;
