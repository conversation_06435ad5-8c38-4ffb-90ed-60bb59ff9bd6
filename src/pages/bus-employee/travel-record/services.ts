import { get, post } from '@/utils';
import {
  IRideRecordDetail,
  IRideRecordEvaluationReBody,
  IRideRecordListItem,
  RideStatusEnum,
} from './interfaces';

/**
 * 查询乘车记录
 * https://soapi.sheincorp.cn/application/3694/routes/184517/doc
 * @param params
 * @returns
 */
export const getRideRecordList = (params: {
  rideStatusList: RideStatusEnum[];
  pageNumber: number;
  pageSize: number;
}) => {
  return post<{
    total: number;
    rows: IRideRecordListItem[];
  }>('/bsms/staff/rideRecord/selectRideRecordList', params);
};

/**
 * 查询剩余可违约次数
 * https://soapi.sheincorp.cn/application/3694/routes/184525/doc
 * @returns
 */
export const getResidueDegree = () => {
  return get<number>('/bsms/staff/rideRecord/selectResidueDegree');
};

/**
 * @description 查询乘车记录评价详情
 * @param {object} params 乘车记录
 * @returns {IRideRecordDetail} 乘车记录评价详情
 */
export const getRideRecordDetail = (params: { id: string }): Promise<IRideRecordDetail> => {
  return get<IRideRecordDetail>('/bsms/staff/rideRecord/selectRideRecordDetail', params);
};

/**
 * @description 提交乘车记录评价
 * @param {IRideRecordEvaluationReBody} params 乘车记录评价
 * @returns {IRideRecordEvaluationReBody} 乘车记录评价
 */
export const submitRideRecordEvaluation = (
  params: IRideRecordEvaluationReBody,
): Promise<IRideRecordEvaluationReBody> => {
  return post<IRideRecordEvaluationReBody>('/bsms/staff/rideRecord/saveScore', params);
};
