import { t } from '@shein-bbl/react';
import { ScheduleTypeEnum } from '../appointment/interfaces';

export enum RideStatusEnum {
  // 待乘车
  WAITING = 0,
  // 已完成
  COMPLETED = 1,
  // 已违约
  BREACH = 2,
  // 车辆缺勤
  ABSENT = 3,
}

export const RideStatusOptions = [
  {
    name: t('全部'),
    value: null,
  },
  {
    name: t('已完成'),
    value: RideStatusEnum.COMPLETED,
  },
  {
    name: t('已违约'),
    value: RideStatusEnum.BREACH,
  },
  // {
  //   name: t('车辆缺勤'),
  //   value: RideStatusEnum.ABSENT,
  // },
];

export interface IRideRecordListItem {
  /** 预约单号 */
  appointmentNum?: string;
  /** yyyy-MM-dd HH:mm */
  appointmentSchedule?: string;
  /** 出发站点名称 */
  departureSiteName?: string;
  /** 目的地站点名称 */
  destinationSiteName?: string;
  /** 员工提交预约的时间 */
  submitTime?: string;
  submitTimeStr?: string;
  /** 车牌号 */
  licensePlate?: string;
  /** 0-白班上班，1-白班下班，2-夜班上班，3-夜班下班 */
  scheduleType?: ScheduleTypeEnum;
  /** '乘车状态；0-待乘车，1-已完成，2-已违约，3-车辆缺勤', */
  rideStatus?: RideStatusEnum;
  /** 乘车记录id */
  id?: number;
  /** 分数 */
  score?: number;
  /** 按钮仅保留7天，发车时间往后推7天，到期隐藏 */
  isAllowScore?: boolean;
}

interface IPictureListItem {
  /** 附件链接 */
  fileUrl?: string;
  /** 附件名称 */
  fileName?: string;
  /** ossKey */
  ossKey?: string;
}

type IPictureList = IPictureListItem[];

/** 乘车记录评价详情 */
export interface IRideRecordDetail {
  /** 乘车记录id */
  id?: number;
  /** '乘车状态 */
  rideStatus?: RideStatusEnum;
  /** 班次类型 */
  scheduleType?: ScheduleTypeEnum;
  /** 车牌号 */
  licensePlate?: string;
  /** 员工提交预约的时间 */
  submitTime?: string;
  /** 员工提交预约的时间 */
  submitTimeStr?: string;
  /** 目的地站点名称 */
  destinationSiteName?: string;
  /** 出发站点名称 */
  departureSiteName?: string;
  /** yyyy-MM-dd HH:mm */
  appointmentSchedule?: string;
  /** 预约单号 */
  appointmentNum?: string;
  /** 分数 */
  score?: number;
  /** 内容 */
  content?: string;
  /** 图片列表 */
  pictureList?: IPictureList;
}

/** 乘车记录评价 */
export interface IRideRecordEvaluationReBody {
  /** 乘车记录id */
  id: string;
  /** 分数 */
  score?: number;
  /** 评价内容 */
  content?: string;
  /** 图片列表 */
  pictureList?: IPictureList;
}
