import { useCallback, useMemo, useRef, useState } from 'react';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { useMount } from '@shein-lego/use';
import { NoticeBar, Tab } from 'shineout-mobile';
import EmptyBus from '../components/_/EmptyBus';
import LoadingBus from '../components/_/LoadingBus';
import TravelRecordItem from '../components/TravelRecordItem';
import useScrollBottom from '../hooks/useScrollBottom';
import styles from './index.less';
import { IRideRecordListItem, RideStatusEnum, RideStatusOptions } from './interfaces';
import { getResidueDegree, getRideRecordList } from './services';

const pageSize = 10;

/**
 * @description TravelRecord
 * @returns {unknown} desc
 */
const TravelRecord = () => {
  usePageTitle(t('乘车记录'));
  const [currentType, setCurrentType] = useState<RideStatusEnum>(undefined);
  const [list, setList] = useState<IRideRecordListItem[]>([]);
  const [degreeNum, setDegreeNum] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const needLoadMore = useMemo(() => total > pageNumber * pageSize, [pageNumber, total]);
  const lockScroll = useRef<boolean>(false);

  const handleLoadMore = useCallback(() => {
    if (lockScroll.current === true) {
      return;
    }
    lockScroll.current = true;
    setTimeout(() => {
      lockScroll.current = false;
    }, 100);
    setPageNumber(pageNumber + 1);
    setLoading(true);
    getRideRecordList({
      rideStatusList: currentType
        ? [currentType]
        : [RideStatusEnum.COMPLETED, RideStatusEnum.BREACH],
      pageNumber: pageNumber + 1,
      pageSize,
    })
      .then((res) => {
        setList([...list, ...res.rows]);
        setTotal(res.total);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [currentType, list, pageNumber]);

  const { onScroll, containerRef } = useScrollBottom({
    onLoadMore: handleLoadMore,
    needLoadMore,
    loading,
  });

  useMount(() => {
    getResidueDegree().then((res) => {
      setDegreeNum(res);
    });
  });

  useMount(() => {
    setLoading(true);
    setList([]);
    getRideRecordList({
      rideStatusList: currentType
        ? [currentType]
        : [RideStatusEnum.COMPLETED, RideStatusEnum.BREACH],
      pageNumber,
      pageSize,
    })
      .then((res) => {
        setList(res.rows);
        setTotal(res.total);
      })
      .finally(() => {
        setLoading(false);
      });
  });

  return (
    <div className={styles.container}>
      <Tab
        className={styles.nav}
        stack
        onChange={(v) => {
          setCurrentType(v);
          setPageNumber(1);
          setList([]);
          setLoading(true);
          getRideRecordList({
            rideStatusList: v ? [v] : [RideStatusEnum.COMPLETED, RideStatusEnum.BREACH],
            pageNumber: 1,
            pageSize,
          })
            .then((res) => {
              setList(res.rows);
              setTotal(res.total);
            })
            .finally(() => {
              setLoading(false);
            });
        }}
      >
        {RideStatusOptions.map((item) => (
          <Tab.Panel key={item.value} name={item.value} tab={item.name}></Tab.Panel>
        ))}
      </Tab>

      <NoticeBar
        style={{ color: '#197AFA', background: '#E3EDFA' }}
        icon="dark-label"
        text={
          <span>
            {t('本月剩余可违约次数 ')}
            <span style={{ color: 'red' }}>{degreeNum}</span>
            {t(' 次')}
          </span>
        }
        scrollable={false}
      />

      <div className={styles.content} ref={containerRef} onScroll={onScroll}>
        {list.map((item) => (
          <TravelRecordItem key={item.id} item={item} />
        ))}
        {loading && list.length === 0 && <LoadingBus text={t('加载中...')} />}
        {!loading && list.length === 0 && <EmptyBus text={t('暂无数据')} />}
      </div>
    </div>
  );
};

export default TravelRecord;
