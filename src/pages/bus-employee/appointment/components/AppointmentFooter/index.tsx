import { t } from '@shein-bbl/react';
import { Button } from 'shineout-mobile';
import styles from './index.less';

interface IProps {
  disabled?: boolean;
  onClick: () => void;
}
/**
 * @description AppointmentFooter
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const AppointmentFooter: React.FC<IProps> = (props) => {
  const { disabled = false, onClick } = props;

  return (
    <div className={styles.footer}>
      <Button onClick={onClick} disabled={disabled} type="primary" style={{ width: '100%' }}>
        {t('立即预约')}
      </Button>
    </div>
  );
};

export default AppointmentFooter;
