import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import { Button, Checkbox, Dialog } from 'shineout-mobile';
import {
  CommonStatusEnum,
  IAppointmentItem,
  ILineItem,
  IScheduleItem,
  ScheduleTypeEnum,
} from '../../interfaces';
import ShortArrow from './short-arrow.png';

interface IProps {
  formValue: IAppointmentItem;
  setFormValue: any;
  lineList: ILineItem[];
  scheduleList: IScheduleItem[];
  onClick: () => void;
}

const AppointmentModalSubmitContent: React.FC<IProps> = (props) => {
  const { formValue, setFormValue, lineList, scheduleList, onClick } = props;

  return (
    <div
      style={{
        textAlign: 'left',
        padding: '20px',
        width: 300,
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <div
        style={{
          fontSize: 16,
          fontWeight: 500,
          marginBottom: 20,
          textAlign: 'center',
        }}
      >
        {t('预约信息确认')}
      </div>
      <div
        style={{
          marginBottom: 10,
        }}
      >
        {formValue?.lineId !== undefined && formValue?.lineId !== null ? (
          <span>
            {/* <span style={{ color: '#666C7C', width: 50, display: 'inline-block' }}>
              {t('线路:')}
            </span> */}
            <span
              style={{
                fontSize: 18,
                fontWeight: 500,
              }}
            >
              {lineList.find((item) => item.id === formValue?.lineId)?.departureSiteName}
            </span>
            <img
              src={ShortArrow}
              style={{
                width: 14,
                height: 6,
                margin: '0 5px',
                position: 'relative',
                top: -3,
              }}
            />
            <span
              style={{
                fontSize: 18,
                fontWeight: 500,
              }}
            >
              {lineList.find((item) => item.id === formValue?.lineId)?.destinationSiteName}
            </span>
          </span>
        ) : (
          <span>-</span>
        )}
      </div>
      <div
        style={{
          marginBottom: 10,
        }}
      >
        <span style={{ color: '#666C7C', width: 50, display: 'inline-block' }}>{t('日期:')}</span>
        <span
          style={{
            fontWeight: 500,
          }}
        >
          {formValue?.startDate}
        </span>
      </div>
      <div
        style={{
          marginBottom: 10,
        }}
      >
        <span style={{ color: '#666C7C', width: 50, display: 'inline-block' }}>{t('班次:')}</span>
        <span
          style={{
            fontWeight: 500,
          }}
        >
          {scheduleList.find((item) => item.id === formValue?.scheduleId)?.scheduleTime}
        </span>
      </div>
      {[ScheduleTypeEnum.DAY_GO_OFF_WORK, ScheduleTypeEnum.NIGHT_GO_OFF_WORK].includes(
        formValue?.scheduleType,
      ) && (
        <>
          <div>
            <Checkbox
              label={
                <div
                  style={{
                    position: 'relative',
                    top: -1,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: '2px',
                  }}
                >
                  {t('匹配WMS排班')}
                  <Icon
                    name="question"
                    fontSize={16}
                    onClick={(e) => {
                      e.stopPropagation();
                      Dialog.alert({
                        title: t('什么是匹配WMS排班？'),
                        message: t(
                          '举例：小明预约18:20乘车班次，在预约截止前WMS为小明安排19:00下班，此时系统自动为员工重新匹配19:00以后的班次，匹配成功重新生成乘车订单，反之不生成。请注意通知班组长在截止时间前修改WMS排班，否则无法匹配',
                        ),
                      });
                    }}
                  />
                </div>
              }
              value={formValue?.matchWmsSchedule === CommonStatusEnum.YES}
              shape="square"
              onChange={(v) => {
                setFormValue({
                  ...formValue,
                  matchWmsSchedule: v ? CommonStatusEnum.YES : CommonStatusEnum.NO,
                });
              }}
            />
          </div>
          <div style={{ marginTop: 4 }}>
            <Checkbox
              label={
                <div
                  style={{
                    position: 'relative',
                    top: -1,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: '2px',
                  }}
                >
                  {t('预约人数不足时，顺延下一趟班次')}
                  <Icon
                    name="question"
                    fontSize={16}
                    onClick={(e) => {
                      e.stopPropagation();
                      Dialog.alert({
                        title: t('什么是顺延下一趟次？'),
                        message: t(
                          '举例：小明预约18:20乘车班次，由于发车人数不足，导致预约失败，此时系统自动为小明匹配18:20之后的车次，直至匹配成功/无班次可匹配。',
                        ),
                      });
                    }}
                  />
                </div>
              }
              value={formValue?.postpone === CommonStatusEnum.YES}
              shape="square"
              onChange={(v) => {
                setFormValue({
                  ...formValue,
                  postpone: v ? CommonStatusEnum.YES : CommonStatusEnum.NO,
                });
              }}
            />
          </div>
        </>
      )}
      <div style={{ marginTop: 18 }}>
        <Button round type="primary" style={{ width: '100%' }} onClick={onClick}>
          {t('确认')}
        </Button>
      </div>
    </div>
  );
};

export default AppointmentModalSubmitContent;
