import { get, post } from '@/utils';
import {
  AppointmentStatusEnum,
  IAppointmentItem,
  IAreaItem,
  ILatestAppointmentItem,
  ILineItem,
  IScheduleItem,
  ScheduleTypeEnum,
} from './interfaces';

/**
 * 提交预约
 * https://soapi.sheincorp.cn/application/3694/routes/184481/doc
 * @param params
 * @returns
 */
export const submitAppointment = (params: IAppointmentItem) => {
  return post<boolean>('/bsms/staff/appointmentRecord/submit', params);
};

/**
 * 区域列表
 * https://soapi.sheincorp.cn/application/3694/routes/184437/doc
 * @returns
 */
export const getAreaList = () => {
  return get<IAreaItem[]>('/bsms/staff/area/selectAreaList');
};

/**
 * 获取上次预约记录
 * https://soapi.sheincorp.cn/application/3694/routes/184438/doc
 * @returns
 */
export const getLatestAppointment = () => {
  return get<ILatestAppointmentItem>('/bsms/staff/appointmentRecord/selectLast');
};

/**
 * 查询线路列表
 * https://soapi.sheincorp.cn/application/3694/routes/184477/doc
 * @param params
 * @returns
 */
export const getLineList = (params: { areaId: number; scheduleType: number }) => {
  return post<ILineItem[]>('/bsms/staff/line/selectLineList', params);
};

/**
 * 查询班次列表
 * https://soapi.sheincorp.cn/application/3694/routes/184478/doc
 * @param params
 * @returns
 */
export const getScheDuleList = (params: { lineId: number; scheduleType: ScheduleTypeEnum }) => {
  return post<IScheduleItem[]>('/bsms/staff/schedule/selectScheduleList', params);
};

/**
 * 查询预约记录
 * https://soapi.sheincorp.cn/application/3694/routes/184485/doc
 * @param params
 * @returns
 */
export const getAppointmentList = (params: {
  appointmentStatus: AppointmentStatusEnum;
  pageNumber: number;
  pageSize: number;
}) => {
  return post<IAppointmentItem[]>(
    '/bsms/staff/appointmentRecord/selectAppointmentRecordList',
    params,
  );
};

/**
 * 取消预约
 * https://soapi.sheincorp.cn/application/3694/routes/184486/doc
 * @param params
 * @returns
 */
export const cancelAppointment = (params: { id: number }) => {
  return post<boolean>('/bsms/staff/appointmentRecord/cancel', params);
};

/**
 * 入口校验
 * @returns
 */
export const checkPassenger = () => {
  return get<boolean>('/bsms/staff/passenger/checkPassenger');
};
