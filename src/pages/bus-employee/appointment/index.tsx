import { MutableRefObject, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { useMount } from 'ahooks';
import { isNil } from 'lodash';
import { Dialog, Drawer, Toast } from 'shineout-mobile';
import AppointmentForm, { IRefs } from '../components/AppointmentForm';
import AppointmentHeader from '../components/AppointmentHeader';
import AppointmentFooter from './components/AppointmentFooter';
import AppointmentModalSubmitContent from './components/AppointmentModalSubmitContent';
import styles from './index.less';
import { CommonStatusEnum, IAppointmentItem, ILineItem, IScheduleItem } from './interfaces';
import { checkPassenger, getLatestAppointment, submitAppointment } from './services';

const TopMenus = [
  {
    name: t('预约记录'),
    path: '/bus-employee/appointment-record',
    // 紫色渐变
    gradient: ['#C4C4FF', '#955EEC'],
    icon: 'record',
  },
  {
    name: t('乘车记录'),
    path: '/bus-employee/travel-record',
    // 橘色渐变
    gradient: ['#FFDC95', '#FF6D28'],
    icon: 'm-bus-fill',
  },
  {
    name: t('空座预订'),
    path: '/bus-employee/empty-reservation',
    // 绿色渐变
    gradient: ['#BFFFC4', '#31C28B'],
    icon: 'pc-seat',
  },
  {
    name: t('乘车码'),
    path: '/bus-employee/ride-code',
    // 蓝色渐变
    gradient: ['#A0F8FF', '#5483E5'],
    icon: 'pc-vehicle-qr-code',
  },
];

const AppointmentPage = () => {
  usePageTitle(t('园区班车'));
  const formRef: MutableRefObject<IRefs | null> = useRef(null);
  const [submitConfirmVisible, setSubmitConfirmVisible] = useState(false);
  const [formValue, setFormValue] = useState<IAppointmentItem>();
  const [lineList, setLineList] = useState<ILineItem[]>([]);
  const [scheduleList, setScheduleList] = useState<IScheduleItem[]>([]);
  const navigate = useNavigate();
  const checkAuth = useRef(false);

  const initial = usePersistFn(() => {
    getLatestAppointment().then((res) => {
      if (res === null) {
        setFormValue({
          ...formValue,
          postpone: CommonStatusEnum.NO,
          matchWmsSchedule: CommonStatusEnum.YES,
        });
        return;
      }
      if (res?.areaStatus === CommonStatusEnum.YES) {
        setFormValue({
          ...formValue,
          areaId: res.areaId,
          postpone: res.postpone,
          matchWmsSchedule: res.matchWmsSchedule,
        });
        formRef.current?.changeCurrentArea({
          id: res.areaId,
          areaName: res.areaName,
          dayAppointmentDeadline: res.dayAppointmentDeadline,
          nightAppointmentDeadline: res.nightAppointmentDeadline,
        });
      } else {
        setFormValue({
          ...formValue,
          postpone: res.postpone,
          matchWmsSchedule: res.matchWmsSchedule,
        });
      }
    });
  });

  useMount(() => {
    checkPassenger().then((res) => {
      if (res) {
        checkAuth.current = true;
        initial();
      } else {
        Dialog.alert({
          title: t('权限提示'),
          message: t('您没有班车系统使用权限，如有疑问，请咨询“行政客服”！'),
          onOk: () => navigate('/'),
        });
      }
    });
  });

  const handleSubmitConfirm = usePersistFn(() => {
    setLineList(formRef.current?.getLineList());
    setScheduleList(formRef.current?.getScheduleList());
    setSubmitConfirmVisible(true);
  });

  const handleSubmit = usePersistFn(() => {
    submitAppointment(formValue)
      .then(() => {
        setFormValue(null);
        formRef.current?.changeCurrentArea(null);
        handleSubmitClose();
        Toast.success(t('预约成功'));
        initial();
      })
      .finally(() => {
        handleSubmitClose();
      });
  });

  /**
   * @description handleSubmitClose
   * @returns {unknown} desc
   */
  const handleSubmitClose = () => {
    setSubmitConfirmVisible(false);
    setLineList([]);
    setScheduleList([]);
  };

  if (!checkAuth.current) {
    return null;
  }

  console.log('formValue', formValue);

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <AppointmentHeader menus={TopMenus} />
        <AppointmentForm ref={formRef} formValue={formValue} setFormValue={setFormValue} />
      </div>
      <AppointmentFooter
        disabled={
          // !formValue?.areaId ||
          // !formValue?.lineId ||
          // !formValue?.startDate ||
          // !formValue?.endDate ||
          // !formValue?.scheduleId ||
          // formValue?.scheduleId === undefined
          !(
            !isNil(formValue?.areaId) &&
            !isNil(formValue?.lineId) &&
            !isNil(formValue?.startDate) &&
            !isNil(formValue?.endDate) &&
            !isNil(formValue?.scheduleId)
          )
        }
        onClick={handleSubmitConfirm}
      />
      <Drawer visible={submitConfirmVisible} onClose={handleSubmitClose} round maskCloseAble>
        <AppointmentModalSubmitContent
          formValue={formValue}
          setFormValue={setFormValue}
          lineList={lineList}
          scheduleList={scheduleList}
          onClick={handleSubmit}
        />
      </Drawer>
    </div>
  );
};

export default AppointmentPage;
