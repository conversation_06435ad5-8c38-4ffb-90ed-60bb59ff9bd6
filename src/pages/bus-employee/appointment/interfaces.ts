import { t } from '@shein-bbl/react';

export interface IAppointmentItem {
  /** 区域id */
  areaId: number;
  /** 预约开始日期 */
  startDate: string;
  /** 预约结束日期 */
  endDate: string;
  /** (0-白班上班，1-白班下班，2-夜班上班，3-夜班下班) */
  scheduleType: ScheduleTypeEnum;
  /** 线路id */
  lineId: number;
  /** 班次id */
  scheduleId: number;
  /** 是否顺延；0-否，1-是 */
  postpone: CommonStatusEnum;
  /** 是否匹配wms排班；0-否，1-是 */
  matchWmsSchedule: CommonStatusEnum;
}

export interface ILatestAppointmentItem {
  /** 区域id */
  areaId?: number;
  /** 区域名称 */
  areaName?: string;
  /** 0-禁用，1-启用 */
  areaStatus?: CommonStatusEnum;
  /** HH:mm */
  dayAppointmentDeadline?: string;
  /** HH:mm */
  nightAppointmentDeadline?: string;
  /** 是否顺延；0-否，1-是 */
  postpone?: CommonStatusEnum;
  /** 是否匹配wms排班；0-否，1-是 */
  matchWmsSchedule?: CommonStatusEnum;
}

export enum AppointmentStatusEnum {
  /** 待排班 */
  WAIT_SCHEDULE = 0,
  /** 预约成功 */
  APPOINTMENT_SUCCESS = 1,
  /** 已取消 */
  CANCELED = 2,
}

export interface IScheduleItem {
  /** 班次id */
  id?: number;
  /** HH:mm */
  scheduleTime?: string;

  disabled?: boolean;
}

export enum CommonStatusEnum {
  NO = 0,
  YES = 1,
}

export enum ScheduleTypeEnum {
  /** 白天上班 */
  DAY_GO_TO_WORK = 0,
  /** 白天下班 */
  DAY_GO_OFF_WORK = 1,
  /** 夜晚上班 */
  NIGHT_GO_TO_WORK = 2,
  /** 夜晚下班 */
  NIGHT_GO_OFF_WORK = 3,
}

export const ScheduleTypeEnumOptions = [
  { name: t('白班/上班'), id: ScheduleTypeEnum.DAY_GO_TO_WORK, icon: 'day', isMorning: true },
  { name: t('白班/下班'), id: ScheduleTypeEnum.DAY_GO_OFF_WORK, icon: 'day', isMorning: false },
  { name: t('夜班/上班'), id: ScheduleTypeEnum.NIGHT_GO_TO_WORK, icon: 'night', isMorning: false },
  { name: t('夜班/下班'), id: ScheduleTypeEnum.NIGHT_GO_OFF_WORK, icon: 'night', isMorning: true },
];

export type IAreaItem = {
  /** 区域id */
  id?: number;
  /** 区域名称 */
  areaName?: string;
  /** HH:mm */
  dayAppointmentDeadline?: string;
  /** HH:mm */
  nightAppointmentDeadline?: string;
  // 是否新班车开放区域
  openArea?: boolean;
  /** 0-禁用，1-启用 */
  areaStatus?: number;
};

export type ILineItem = {
  /** 线路id */
  id?: number;
  /** 出发站点名称 */
  departureSiteName?: string;
  /** 目的地站点名称 */
  destinationSiteName?: string;

  disabled?: boolean;
  lineStatus?: number;
  isLastLine?: boolean;
};
