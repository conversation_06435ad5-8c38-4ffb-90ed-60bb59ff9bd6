import { useRef } from 'react';

/**
 * @description useScrollBottom
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const useScrollBottom = ({ onLoadMore, needLoadMore, loading }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  /**
   * @description onScroll
   * @returns {unknown} desc
   */
  const onScroll = () => {
    if (containerRef.current && needLoadMore && !loading) {
      const { scrollHeight, scrollTop, clientHeight } = containerRef.current;
      const bottomDistance = scrollHeight - scrollTop - clientHeight;
      if (Math.floor(bottomDistance) < 1) {
        onLoadMore?.();
      }
    }
    return false;
  };

  return {
    containerRef,
    onScroll,
  };
};

export default useScrollBottom;
