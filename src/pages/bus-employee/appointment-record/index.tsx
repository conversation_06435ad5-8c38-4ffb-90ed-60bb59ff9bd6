import { useCallback, useMemo, useRef, useState } from 'react';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { useMount } from 'ahooks';
import { Tab, Toast } from 'shineout-mobile';
import EmptyBus from '../components/_/EmptyBus';
import LoadingBus from '../components/_/LoadingBus';
import AppointmentRecordItem from '../components/AppointmentRecordItem';
import useScrollBottom from '../hooks/useScrollBottom';
import styles from './index.less';
import { AppointmentStatusEnumOptions, IAppointmentItem } from './interfaces';
import { getAppointmentsRecordList } from './services';

/**
 * @description AppointmentRecordPage
 * @returns {unknown} desc
 */
const AppointmentRecordPage = () => {
  usePageTitle(t('预约记录'));
  const [currentType, setCurrentType] = useState(undefined);
  const [total, setTotal] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const pageSize = 10;
  const [list, setList] = useState<IAppointmentItem[]>([]);
  const [loading, setLoading] = useState(false);
  const needLoadMore = useMemo(() => total > pageNumber * pageSize, [pageNumber, total]);
  const lockScroll = useRef<boolean>(false);

  const handleLoadMore = useCallback(() => {
    if (lockScroll.current === true) {
      return;
    }
    lockScroll.current = true;
    setTimeout(() => {
      lockScroll.current = false;
    }, 100);
    setPageNumber(pageNumber + 1);
    setLoading(true);
    Toast.success(t('加载中...'));
    getAppointmentsRecordList({
      appointmentStatus: currentType,
      pageNumber: pageNumber + 1,
      pageSize,
    })
      .then((res) => {
        setList([...list, ...res.rows]);
        setTotal(res.total);
      })
      .finally(() => {
        setLoading(false);
        Toast.hide();
      });
  }, [currentType, list, pageNumber]);

  const { onScroll, containerRef } = useScrollBottom({
    onLoadMore: handleLoadMore,
    needLoadMore,
    loading,
  });

  const freshList = useCallback(() => {
    setList([]);
    setPageNumber(1);
    setLoading(true);
    getAppointmentsRecordList({
      appointmentStatus: currentType,
      pageNumber,
      pageSize,
    })
      .then((res) => {
        setList(res.rows);
        setTotal(res.total);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [currentType, pageNumber]);

  useMount(() => {
    setLoading(true);
    getAppointmentsRecordList({
      appointmentStatus: currentType,
      pageNumber,
      pageSize,
    })
      .then((res) => {
        setList(res.rows);
        setTotal(res.total);
      })
      .finally(() => {
        setLoading(false);
      });
  });

  const handleChangeStatus = useCallback((v) => {
    setCurrentType(v);
    setList([]);
    setPageNumber(1);
    setLoading(true);
    getAppointmentsRecordList({
      appointmentStatus: v,
      pageNumber: 1,
      pageSize,
    })
      .then((res) => {
        setList(res.rows);
        setTotal(res.total);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <div>
      <Tab
        className={styles.nav}
        stack
        onChange={(v) => {
          handleChangeStatus(v);
        }}
      >
        {AppointmentStatusEnumOptions.map((item) => (
          <Tab.Panel key={item.value} name={item.value} tab={item.name}>
            {null}
          </Tab.Panel>
        ))}
      </Tab>
      <div className={styles.content} ref={containerRef} onScroll={onScroll}>
        {list.map((item) => (
          <AppointmentRecordItem key={item.id} item={item} onFreshList={() => freshList()} />
        ))}
        {loading && list.length === 0 && <LoadingBus text={t('加载中...')} />}
        {!loading && list.length === 0 && <EmptyBus text={t('暂无数据')} />}
      </div>
    </div>
  );
};
export default AppointmentRecordPage;
