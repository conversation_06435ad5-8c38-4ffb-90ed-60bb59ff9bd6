import { t } from '@shein-bbl/react';
import { ScheduleTypeEnum } from '../appointment/interfaces';

export enum AppointmentStatusEnum {
  // 待排班
  WAITING_FOR_SCHEDULE = 0,
  // 预约成功
  APPOINTMENT_SUCCESS = 1,
  // 已取消
  CANCELED = 2,
}
export const AppointmentStatusEnumOptions = [
  {
    name: t('全部'),
    value: undefined,
  },
  {
    name: t('待排班'),
    value: AppointmentStatusEnum.WAITING_FOR_SCHEDULE,
  },
  {
    name: t('预约成功'),
    value: AppointmentStatusEnum.APPOINTMENT_SUCCESS,
  },
  {
    name: t('已取消'),
    value: AppointmentStatusEnum.CANCELED,
  },
];

/** 0-发车人数不足，1-员工取消，2-匹配排班变更，3-班次顺延变更，4-线路停用，5-临时乘客被禁用 */
export enum CancelReasonTypeEnum {
  // 发车人数不足
  INSUFFICIENT_PEOPLE = 0,
  // 员工取消
  STAFF_CANCELED = 1,
  // 匹配排班变更
  MATCH_SCHEDULE_CHANGE = 2,
  // 班次顺延变更
  SCHEDULE_CHANGE = 3,
  // 线路停用
  LINE_STOPPED = 4,
  // 临时乘客被禁用
  TEMPORARY_PASSENGER_DISABLED = 5,
}

export interface IAppointmentItem {
  /** 预约记录id */
  id?: number;
  /** 0-发车人数不足，1-员工取消，2-匹配排班变更，3-班次顺延变更，4-线路停用，5-临时乘客被禁用 */
  cancelReasonType?: CancelReasonTypeEnum;
  /** 0-白班上班，1-白班下班，2-夜班上班，3-夜班下班 */
  scheduleType?: ScheduleTypeEnum;
  /** 车牌号列表 */
  licensePlateList?: string[];
  /** 员工提交预约的时间 */
  submitTime?: string;
  /** 目的地站点名称 */
  destinationSiteName?: string;
  /** 出发站点名称 */
  departureSiteName?: string;
  /** yyyy-MM-dd HH:mm */
  appointmentSchedule?: string;
  /** 0-待排班，1-预约成功，2-已取消 */
  appointmentStatus?: AppointmentStatusEnum;
  /** 预约单号 */
  appointmentNum?: string;
  /** 是否可取消 */
  cancelability?: boolean;
  /** 取消原因 */
  cancelReason?: string;
}
