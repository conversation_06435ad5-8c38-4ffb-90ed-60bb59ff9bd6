import { post } from '@/utils';
import { AppointmentStatusEnum, IAppointmentItem } from './interfaces';

/**
 * 查询预约记录
 * https://soapi.sheincorp.cn/application/3694/routes/184485/doc
 * @param params
 * @returns
 */
export const getAppointmentsRecordList = (params: {
  appointmentStatus: AppointmentStatusEnum;
  pageNumber: number;
  pageSize: number;
}) => {
  return post<{
    total: number;
    rows: IAppointmentItem[];
  }>('/bsms/staff/appointmentRecord/selectAppointmentRecordList', params);
};
