import Icon from '@shein-components/Icon';

interface IProps {
  text: string;
}

/**
 * @description Empty
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const Empty: React.FC<IProps> = ({ text }) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
      <Icon name="pc-empty-multic" fontSize={108} />
      <p
        style={{
          color: '#aaa',
          textAlign: 'center',
          marginBottom: 20,
          marginTop: 10,
        }}
      >
        {text}
      </p>
    </div>
  );
};

export default Empty;
