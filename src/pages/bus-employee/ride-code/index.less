/* stylelint-disable max-nesting-depth */
.container {
  position: relative;
  display: flex;
  height: 100vh;
  padding: 12px;
  flex-direction: column;
  gap: 10px;

  .code {
    display: flex;
    height: 350px;
    background-color: white;
    border-radius: 4px;
    // justify-content: center;
    align-items: center;
    flex-direction: column;

    .title {
      margin: 10px;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.5;
    }

    .subDes {
      display: flex;
      flex-direction: row;
      width: 280px;
      align-items: center;
      justify-content: space-between;
      text-align: center;

      p {
        flex-grow: 1;
        padding: 14px;
      }
    }
  }

  .info {
    overflow-y: auto;
    flex-grow: 1;
    border-radius: 4px;

    .item {
      display: flex;
      padding: 12px;
      margin-bottom: 10px;
      background-color: white;
      border-radius: 4px;
      flex-direction: column;
      gap: 10px;

      .title {
        font-size: 14px;
        font-weight: 500;
        line-height: 1.5;
      }

      .panel {
        position: relative;
        display: flex;
        padding: 12px 10px;
        // margin-top: 5px;
        text-align: center;
        background-color: #eef2fe;
        border-radius: 8px;
        flex-direction: row;
        justify-content: center;
        align-items: center;

        .recent {
          position: absolute;
          right: 0;
          bottom: 0;
          z-index: 1;
          padding: 4px 8px;
          font-size: 10px;
          color: white;
          background-color: #f56c0a;
          border-radius: 4px;
        }

        .departure,
        .destination {
          display: flex;
          flex: 1 1 0;
          font-size: 18px;
          font-weight: 500;
          justify-content: center;
        }

        .bound {
          width: 100px;
          min-width: 100px;
          padding: 0 5px;

          .arrow {
            .img {
              display: inline-block;
              width: 90px;
              height: 10px;
              background-image: url('./long-arrow.png');
              background-repeat: no-repeat;
              background-size: contain;
            }
          }

          .date {
            color: #141737;
          }

          .time {
            font-size: 16px;
            font-weight: 500;
            color: #141737;
          }
        }
      }

      .boardingPoint {
        display: flex;
        flex-direction: row;
        align-items: center;

        .label {
          flex-basis: 80px;
          font-size: 14px;
          line-height: 1.5;
          color: #666c7c;

          &::after {
            margin-right: 5px;
            content: ':';
          }
        }

        .value {
          display: flex;
          padding: 3px 0;
          flex-grow: 1;
          font-size: 14px;
          color: #141737;
        }
      }

      .vehicle {
        align-items: center;
        display: flex;
        flex-direction: row;

        .label {
          display: flex;
          flex-basis: 80px;
          min-width: 80px;
          font-size: 14px;
          line-height: 1.5;
          color: #666c7c;
          justify-content: flex-start;
          align-self: flex-start;

          &::after {
            margin-right: 5px;
            content: ':';
          }
        }

        .value {
          display: flex;
          max-height: 140px;
          padding-top: 3px;
          overflow: auto;
          flex-grow: 1;
          flex-wrap: wrap;
          gap: 12px;

          .plate {
            padding: 6px 12px;
            line-height: 1.5;
            color: #141737;
            text-align: center;
            white-space: nowrap;
            background-color: #e8ebf0;
            border-radius: 4px;
            flex: 0 0 calc(50% - 30px);
          }
        }
      }

      .explain {
        display: flex;
        padding: 6px 12px;
        color: #f56c0a;
        background-color: #fff3e2;
        border-radius: 4px;
        flex-direction: row;

        .label {
          flex-basis: 50px;
          font-size: 14px;
          line-height: 1.5;
          color: #f56c0a;

          &::after {
            margin-right: 5px;
            content: ':';
          }
        }

        .value {
          font-size: 14px;
          line-height: 1.5;
          color: #f56c0a;
          flex-grow: 1;
        }
      }
    }
  }
}
