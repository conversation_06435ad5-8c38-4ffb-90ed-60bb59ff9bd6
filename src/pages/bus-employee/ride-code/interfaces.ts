export interface ITicketListItem {
  /** yyyy-MM-dd */
  rideDate?: string;
  /** HH:mm */
  scheduleTime?: string;
  /** 出发站点名称 */
  departureSiteName?: string;
  /** 目的地站点名称 */
  destinationSiteName?: string;
  /** 乘车记录id */
  id?: number;
  /** 0-否 1-是 */
  latestTag?: number;
  /** 上车地点 */
  pickUpPoint?: string;
  /** 车牌号列表 */
  licensePlateList?: string[];
  /** 排班信息 */
  // workingScheduleInfo?: IWorkingScheduleInfo;
}
// interface IWorkingScheduleInfo {
//   /** 上车地点 */
//   pickUpPoint?: string;
//   /** 车牌号列表 */
//   licensePlateList?: string[];
// }
