import { useCallback, useRef, useState } from 'react';
import { usePageTitle } from '@/_/hooks';
import Icon from '@shein-components/Icon';
import QRCode from '@shein-components/qr-code';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { useMount, useUnmount } from 'ahooks';
import { Button, Dialog } from 'shineout-mobile';
import Empty from './components/Empty';
import styles from './index.less';
import { ITicketListItem } from './interfaces';
import { getRideCode, getTicketList } from './services';

const INTERVAL_TIME = 60 * 1000;

const RideCodePage = () => {
  usePageTitle(t('乘车码'));
  const [loading, setLoading] = useState(false);
  const [codeValue, setCodeValue] = useState('');
  const intervalRef = useRef<any>();
  const [list, setList] = useState<ITicketListItem[]>([]);

  const startIntervalGetCode = usePersistFn(() => {
    intervalRef.current = setInterval(() => {
      handleGetRideCode();
    }, INTERVAL_TIME);
  });

  /**
   * @description handleGetRideCode
   * @returns {unknown} desc
   */
  const handleGetRideCode = () => {
    setLoading(true);
    getRideCode()
      .then((res) => {
        setCodeValue(res);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleGetTicketList = useCallback(() => {
    getTicketList().then((res) => {
      setList(res);
    });
  }, []);

  useMount(() => {
    handleGetTicketList();
    handleGetRideCode();
    startIntervalGetCode();
  });

  useUnmount(() => {
    clearInterval(intervalRef.current);
  });

  /**
   * @description handleFreshCode
   * @returns {unknown} desc
   */
  const handleFreshCode = () => {
    clearInterval(intervalRef.current);
    handleGetRideCode();
    startIntervalGetCode();
  };

  /**
   * @description handleLookQuestion
   * @returns {unknown} desc
   */
  const handleLookQuestion = () => {
    Dialog.alert({
      title: t('验票失败常见原因'),
      message: (
        <div>
          <p>{t('1、您没有成功预约此班次，或重复核销乘车码-请核对预约记录；')}</p>
          <p>{t('2、乘车码无效-请点击乘车码刷新后，再次尝试核销；')}</p>
          <p>{t('如有其他疑问，请联系“行政客服”，谢谢。')}</p>
        </div>
      ),
    });
  };

  return (
    <div className={styles.container}>
      <div className={styles.code}>
        <div className={styles.title}>{t('园区乘车码')}</div>
        <QRCode
          size={260}
          className={styles.qrCode}
          status={loading ? 'loading' : 'active'}
          value={codeValue}
          color={list.length === 0 ? '#F56C0A' : 'black'}
        />
        <div className={styles.subDes}>
          <p>
            <Button text type="primary" onClick={handleFreshCode}>
              {t('点击刷新二维码')}
            </Button>
          </p>
          <p>
            <Button type="default" text onClick={handleLookQuestion}>
              {t('验票失败')}
              <Icon type="info" name="question" />
            </Button>
          </p>
        </div>
      </div>
      <div className={styles.info}>
        {list.map((item, index) => (
          <div className={styles.item} key={item.id}>
            {index === 0 && <div className={styles.title}>{t('车票信息')}</div>}
            <div className={styles.panel}>
              {index === 0 && <div className={styles.recent}>{t('最近班车')}</div>}
              <div className={styles.departure}>{item.departureSiteName}</div>
              <div className={styles.bound}>
                <div className={styles.time}>{item.scheduleTime}</div>
                <div className={styles.arrow}>
                  <span className={styles.img} />
                </div>
                <div className={styles.date}>{item.rideDate}</div>
              </div>
              <div className={styles.destination}>{item.destinationSiteName}</div>
            </div>
            <div className={styles.boardingPoint}>
              <div className={styles.label}>{t('上车地点')}</div>
              <div className={styles.value}>{item?.pickUpPoint}</div>
            </div>
            <div className={styles.vehicle}>
              <div className={styles.label}>{t('可乘车辆')}</div>
              <div className={styles.value}>
                {item?.licensePlateList?.length > 0 ? (
                  item?.licensePlateList?.map((plate) => {
                    return (
                      <div key={plate} className={styles.plate}>
                        {plate}
                      </div>
                    );
                  })
                ) : (
                  <span style={{ color: 'grey' }}>{t('待安排车辆')}</span>
                )}
              </div>
            </div>
            {/* )} */}
            {/* {item.workingScheduleInfo === null && (
              <div>
                <Empty text={t('暂未排班，请留意排班信息')} />
              </div>
            )} */}
            {index === 0 && (
              // item.pickUpPoint !== undefined &&
              // item.pickUpPoint !== null &&
              // item.pickUpPoint !== '' &&
              <div className={styles.explain}>
                <div className={styles.label}>{t('说明')}</div>
                <div className={styles.value}>
                  <p>{t('1、以上车辆可任意选择乘坐')}</p>
                  <p>{t(' 2、一人一票，有序验票上车')}</p>
                </div>
              </div>
            )}
          </div>
        ))}

        {list.length === 0 && (
          <div className={styles.item}>
            <div className={styles.title}>{t('车票信息')}</div>
            <Empty text={t('您今天没有待乘坐的车票信息')} />
          </div>
        )}
      </div>
    </div>
  );
};

export default RideCodePage;
