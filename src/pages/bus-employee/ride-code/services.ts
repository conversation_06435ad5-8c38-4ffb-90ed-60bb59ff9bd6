import { get } from '@/utils/fetch';
import { ITicketListItem } from './interfaces';

/**
 * 获取乘车码
 * https://soapi.sheincorp.cn/application/3694/routes/184526/doc
 * @returns
 */
export const getRideCode = () => {
  return get<string>('/bsms/staff/rideRecord/selectRideCode');
};

/**
 * 获取车票列表
 * https://soapi.sheincorp.cn/application/3694/routes/184533/doc
 * @returns
 */
export const getTicketList = () => {
  return get<ITicketListItem[]>('/bsms/staff/rideRecord/selectTicketList');
};
