.container {
  position: relative;
  padding: 10px 20px;
  margin-bottom: 10px;
  overflow: hidden;
  background-color: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;

  .forbidIcon {
    /* stylelint-disable-next-line prettier/prettier */
    transition: all .3s ease-in-out;
    position: absolute;
    top: 15px;
    right: 15px;
    display: inline-block;
    width: 70px;
    height: 70px;
    margin-right: 5px;
    /* stylelint-disable-next-line prettier/prettier */
    opacity: .5;
    background-image: url('./<EMAIL>');
    background-size: contain;
    background-repeat: no-repeat;
  }

  .footer {
    display: flex;
    height: 30px;
    padding-top: 5px;
    margin-top: 5px;
    border-top: 1px dashed #ddd;
    flex-direction: row;

    .left {
      display: flex;
      font-size: 14px;
      color: #141737;
      flex-grow: 1;
      justify-content: flex-start;
      align-items: center;
    }

    .right {
      flex-grow: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 2px;
      // margin-right: 5px;
    }
  }
}

.violation {
  .row .value {
    color: #999 !important;
  }
}

.row {
  display: flex;
  padding: 5px 0;
}

.row .label {
  width: 68px;
  min-width: 68px;
  padding-top: 2px;
  margin-right: 10px;
  font-size: 14px;
  line-height: 1.5;
  color: #999;
}

.row .value {
  display: inline-block;
  margin: auto;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  flex-grow: 1;
}
