import { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AutoGapLabel from '@/_/components/AutoGapLabel';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';
import { isNil } from 'lodash';
import { Button, Tag } from 'shineout-mobile';
import { IRideRecordListItem, RideStatusEnum } from '../../travel-record/interfaces';
import ScheduleTypeItem from '../_/WorkTypeItem';
import CarPlate from '../CarPlate';
import RouteLineStation from '../RouteLineStation';
import styles from './index.less';

interface IProps {
  item: IRideRecordListItem;
}

interface IRowProps {
  label: string;
  value: React.ReactNode | string;
}

interface IStatusValueProps {
  status: RideStatusEnum;
}

interface IFooterProps {
  orderNo: string;
  toggle: boolean;
  doToggle: () => void;
  data: IRideRecordListItem;
}

/**
 * @description Row
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const Row: React.FC<IRowProps> = ({ label, value }) => {
  return (
    <div className={styles.row}>
      <AutoGapLabel className={styles.label} label={label} />
      <div className={styles.value}>{value}</div>
    </div>
  );
};

/**
 * @description StatusValue
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const StatusValue: React.FC<IStatusValueProps> = ({ status }) => {
  const StatusMap = {
    [RideStatusEnum.COMPLETED]: (
      <Tag color="#E1F5EA" textColor="#04C75F">
        {t('已完成')}
      </Tag>
    ),
    [RideStatusEnum.BREACH]: (
      <Tag color="#FAE9E9" textColor="#F96156">
        {t('已违约')}
      </Tag>
    ),
  };
  return <>{StatusMap[status]}</>;
};

/**
 * @description Footer
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const Footer = ({ toggle, doToggle, orderNo, data }: IFooterProps): React.ReactElement => {
  const { score, isAllowScore } = data;
  let scoreButton = null;
  // 跳转去评价
  const navigate = useNavigate();
  const handleScore = () => {
    navigate(`/bus-employee/travel-record/${data.id}`);
  };
  const hasScore = score > 0;
  if (hasScore || isAllowScore) {
    scoreButton = (
      <Button type="primary" text onClick={handleScore} style={{ marginRight: 6 }}>
        {hasScore ? t('查看评价') : t('评价')}
      </Button>
    );
  }

  return (
    <div className={styles.footer}>
      <div className={styles.left}>{orderNo}</div>
      <div className={styles.right}>
        {scoreButton}
        <Button type="primary" text onClick={doToggle}>
          {toggle ? t('收起') : t('展开')}
        </Button>
      </div>
    </div>
  );
};

/**
 * @description TravelRecordItem
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const TravelRecordItem: React.FC<IProps> = (props) => {
  const { item } = props;
  const [toggle, setToggle] = useState(false);
  const forbidRef = useRef<HTMLDivElement>(null);

  /**
   * @description handleToggle
   * @returns {unknown} desc
   */
  const handleToggle = () => {
    const el = forbidRef.current;
    if (el) {
      if (toggle) {
        el.style.width = '70px';
        el.style.height = '70px';
      } else {
        el.style.width = '110px';
        el.style.height = '110px';
      }
    }
    setToggle(!toggle);
  };

  return (
    <div
      className={classNames(
        styles.container,
        toggle ? styles.activated : null,
        item.rideStatus === RideStatusEnum.BREACH ? styles.violation : null,
      )}
    >
      {item.rideStatus === RideStatusEnum.BREACH && (
        <div className={styles.forbidIcon} ref={forbidRef}></div>
      )}
      <Row label={t('状态')} value={<StatusValue status={item.rideStatus} />} />
      <Row
        label={t('班次')}
        value={<div style={{ paddingTop: 2, fontWeight: 500 }}>{item.appointmentSchedule} </div>}
      />
      <Row
        label={t('线路')}
        value={<RouteLineStation station={[item.departureSiteName, item.destinationSiteName]} />}
      />
      {toggle && (
        <Row
          label={t('预约时间')}
          value={
            <div style={{ paddingTop: 2 }}>
              {isNil(item?.submitTimeStr) ? '--' : item?.submitTimeStr}
            </div>
          }
        />
      )}
      {toggle && (
        <Row
          label={t('上班类型')}
          value={
            <div style={{ paddingTop: 2, display: 'inline-block' }}>
              <ScheduleTypeItem scheduleType={item.scheduleType} />
            </div>
          }
        />
      )}
      {toggle && (
        <Row
          label={t('车牌号')}
          value={
            item.licensePlate !== null && item.licensePlate !== undefined ? (
              <CarPlate number={item.licensePlate} />
            ) : (
              <div style={{ display: 'flex', alignItems: 'center' }}>-</div>
            )
          }
        />
      )}

      <Footer orderNo={item?.appointmentNum} data={item} toggle={toggle} doToggle={handleToggle} />
    </div>
  );
};

export default TravelRecordItem;
