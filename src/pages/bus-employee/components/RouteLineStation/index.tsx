import Icon from '@/_/components/Icon';

/**
 * @description RouteLineStation
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const RouteLineStation: React.FC<{ station: string[] }> = ({ station }) => {
  return (
    <>
      <span
        style={{
          display: 'inline-block',
          marginRight: 4,
          marginTop: 2,
          fontWeight: 500,
          fontSize: 16,
        }}
      >
        {station[0]}
      </span>
      <Icon name="pc-path-arrow" fontSize={18} />
      <span
        style={{
          display: 'inline-block',
          marginTop: 2,
          marginLeft: 4,
          fontSize: 16,
          fontWeight: 500,
        }}
      >
        {station[1]}
      </span>
    </>
  );
};

export default RouteLineStation;
