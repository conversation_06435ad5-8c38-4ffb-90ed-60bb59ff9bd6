import { useMemo } from 'react';
import { ScheduleTypeEnumOptions } from '@/pages/bus-employee/appointment/interfaces';
import { t } from '@shein-bbl/react';
import moment from 'moment';
import AppointmentRadioValue, { IRadioVItem } from '../../../AppointmentRadioValue';
import FormItem from '../FormItem';

interface IProps {
  value: number;
  onChange: (v: number) => void;
  disabled?: boolean;
  dayAppointmentDeadline?: string;
  nightAppointmentDeadline?: string;
  startDate?: string;
}

/**
 * @description FormItemForWorkType
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const FormItemForWorkType: React.FC<IProps> = (props) => {
  const {
    value,
    onChange,
    disabled = false,
    dayAppointmentDeadline,
    nightAppointmentDeadline,
    startDate,
  } = props;

  const data: IRadioVItem[] = useMemo(() => {
    if (startDate === undefined) {
      return null;
    }
    return ScheduleTypeEnumOptions.map((item) => {
      switch (item.icon) {
        case 'day':
          const dayDeadline = item.isMorning
            ? moment(startDate + ' ' + dayAppointmentDeadline + ':00')
            : moment(startDate + ' ' + nightAppointmentDeadline + ':00');
          const baseDay = item.isMorning === true ? dayDeadline.subtract(1, 'day') : dayDeadline;
          return {
            ...item,
            disabled: moment().isAfter(baseDay),
            desc: `${t('截止')} ${baseDay.format('MM-DD HH:mm')}`,
          };
        case 'night':
          const nightDeadline = item.isMorning
            ? moment(startDate + ' ' + dayAppointmentDeadline + ':00')
            : moment(startDate + ' ' + nightAppointmentDeadline + ':00');
          const baseDay2 = item.isMorning ? nightDeadline.subtract(1, 'day') : nightDeadline;
          return {
            ...item,
            disabled: moment().isAfter(baseDay2),
            desc: `${t('截止')} ${baseDay2.format('MM-DD HH:mm')}`,
          };
        default:
          return {
            ...item,
            disabled: false,
            desc: '',
          };
      }
    });
  }, [dayAppointmentDeadline, nightAppointmentDeadline, startDate]);

  return (
    <FormItem
      label={t('上下班类型')}
      value={<AppointmentRadioValue data={data} value={value} onChange={onChange} />}
      icon={'pc-classes'}
      isColumnMode={true}
      disabled={disabled}
    />
  );
};

export default FormItemForWorkType;
