import { IScheduleItem } from '@/pages/bus-employee/appointment/interfaces';
import { t } from '@shein-bbl/react';
import AppointmentTimeClasses from '../../../AppointmentTimeClasses';
import FormItem from '../FormItem';

interface IProps {
  value: number;
  onChange: (v: number) => void;
  disabled?: boolean;
  scheduleList?: IScheduleItem[];
}

/**
 * @description FormItemTimeClasses
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const FormItemTimeClasses: React.FC<IProps> = (props) => {
  const { value, onChange, disabled = false, scheduleList } = props;

  return (
    <FormItem
      label={t('班次')}
      value={<AppointmentTimeClasses value={value} data={scheduleList} onChange={onChange} />}
      icon="pc-time"
      isColumnMode={true}
      disabled={disabled}
    />
  );
};

export default FormItemTimeClasses;
