import { useState } from 'react';
import Icon from '@/_/components/Icon';
import { ILineItem } from '@/pages/bus-employee/appointment/interfaces';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';
import { isNil } from 'lodash';
import { Drawer, Tag } from 'shineout-mobile';
import AppointmentRouteLineValue from '../../../AppointmentRouteLineValue';
import FormItem from '../FormItem';
import styles from './index.less';

interface IProps {
  value: number;
  onChange: (v: number) => void;
  disabled?: boolean;
  lineList: ILineItem[];
}

const FormItemRouteLine: React.FC<IProps> = (props) => {
  const { value, onChange, disabled = false, lineList } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const isHasValue = !disabled && isNil(value);
  const item = lineList.find((item) => item.id === value) || {
    departureSiteName: '',
    destinationSiteName: '',
  };

  return (
    <>
      <FormItem
        label={t('线路')}
        value={
          isHasValue ? (
            <div>{t('请选择路线')}</div>
          ) : (
            <div style={{ width: '100%', maxWidth: 750 }}>
              {item?.isLastLine && <Tag type="warning">{t('最近预约线路')}</Tag>}
              <div className={classNames(styles.item, styles.itemChecked)}>
                <span className={styles.from}> {item.departureSiteName}</span>
                <span className={styles.icon}>
                  <Icon name="pc-path-arrow" fontSize={24} />
                </span>
                <span className={styles.to}> {item.destinationSiteName}</span>
                <div className={styles.checkIcon}>
                  <Icon name="pc-check-fill" color="#4d90fe" fontSize={12} />
                </div>
              </div>
            </div>
          )
        }
        icon="gd-ts"
        isColumnMode={!isHasValue}
        isCoincidence={true}
        isLink={true}
        disabled={disabled}
        onClick={() => {
          if (!disabled) {
            setVisible(true);
          }
        }}
      />
      <Drawer
        visible={visible}
        position="bottom"
        round
        closeable
        maskCloseAble={false}
        onClose={() => {
          setVisible(false);
        }}
        className={styles.drawerContainer}
        // drawerClass={styles.drawerContainer}
      >
        <div style={{ overflow: 'auto', padding: '12px 12px 24px' }}>
          <div
            style={{
              textAlign: 'center',
              color: '#999',
              paddingTop: 8,
              fontSize: 16,
              fontWeight: 500,
            }}
          >
            {t('请选择路线')}
          </div>
          <AppointmentRouteLineValue
            value={value}
            data={lineList}
            onChange={(value) => {
              setVisible(false);
              onChange(value);
            }}
          />
        </div>
      </Drawer>
    </>
  );
};

export default FormItemRouteLine;
