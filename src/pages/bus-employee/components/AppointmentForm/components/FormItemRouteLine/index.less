.drawerContainer {
  :global(.sm-drawer) {
    height: 550px !important;
  }
}

.item {
  position: relative;
  display: flex;
  height: 42px;
  padding: 10px 12px;
  margin-top: 6px;
  line-height: 1;
  background-color: white;
  border: 1px solid #cccfd7;
  border-radius: 8px;
  flex: 0 0 100%;
  align-items: center;

  .from {
    font-weight: 500;
    text-align: center;
    flex-grow: 1;
    flex-basis: calc(50% - 24px);
  }

  .icon {
    width: 30px;
    color: #666;
    text-align: center;
    flex-shrink: 30px;
  }

  .to {
    font-weight: 500;
    text-align: center;
    flex-grow: 1;
    flex-basis: calc(50% - 24px);
  }

  .checkIcon {
    position: absolute;
    right: -1px;
    bottom: -1px;
  }
}

.itemChecked {
  color: #4d90fe;
  background-color: #e9f5fe;
  border: 1px solid #4d90fe;

  .icon {
    color: #4d90fe;
  }
}
