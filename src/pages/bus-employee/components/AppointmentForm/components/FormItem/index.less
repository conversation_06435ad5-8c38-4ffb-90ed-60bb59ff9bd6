.item {
  display: flex;
  padding: 12px;
  margin-bottom: 12px;
  overflow: hidden;
  background-color: white;
  border-radius: 8px;

  .label {
    display: flex;
    min-width: 100px;
    line-height: 1.5;
    flex-grow: 1;
    align-items: center;
    gap: 8px;
  }

  .value {
    display: flex;
    font-weight: 500;
    line-height: 1.5;
    flex-grow: 2;
    align-items: flex-start;
    justify-content: flex-end;
    gap: 5px;

    .valueText {
      flex-grow: 1;
      display: flex;
      justify-content: flex-end;
      word-break: break-all;
    }

    .icon {
      width: 20px;
    }
  }
}

.disabledItem {
  /* stylelint-disable-next-line prettier/prettier */
  opacity: .6;
}
