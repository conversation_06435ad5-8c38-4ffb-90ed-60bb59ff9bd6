import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { useMount } from 'ahooks';
import { isNil } from 'lodash';
import moment from 'moment';
import { ActionSheet, Calendar, Dialog, Drawer, NPicker } from 'shineout-mobile';
import {
  CommonStatusEnum,
  IAppointmentItem,
  IAreaItem,
  ILineItem,
  IScheduleItem,
  ScheduleTypeEnum,
} from '../../appointment/interfaces';
import { getAreaList, getLineList, getScheDuleList } from '../../appointment/services';
import FormItem from './components/FormItem';
import FormItemForWorkType from './components/FormItemForWorkType';
import FormItemRouteLine from './components/FormItemRouteLine';
import FormItemTimeClasses from './components/FormItemTimeClasses';
import styles from './index.less';

interface IProps {
  formValue: IAppointmentItem;
  setFormValue: React.Dispatch<React.SetStateAction<IAppointmentItem>>;
}

export interface IRefs {
  changeCurrentArea: (val: IAreaItem) => void;
  getLineList: () => ILineItem[];
  getScheduleList: () => IScheduleItem[];
}

enum DatePanelActiveEnum {
  SINGLE_DAY = 1,
  RANGE_DAY = 2,
}

/**
 * @description RenderDateValue
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const RenderDateValue: React.FC<{ value: IAppointmentItem }> = ({ value }) => {
  if (value?.startDate && value?.endDate) {
    if (value.startDate === value.endDate) {
      return <div>{value.startDate}</div>;
    }
    return (
      <div>
        {value.startDate} - {value.endDate}
      </div>
    );
  }
  if (value?.startDate) {
    return <div>{value.startDate}</div>;
  }
  return <span style={{ color: '#999DA8' }}>{t('请选择日期')}</span>;
};

const AppointmentForm: React.ForwardRefRenderFunction<IRefs, IProps> = (props, ref) => {
  const { formValue, setFormValue } = props;
  const [areaList, setAreaList] = useState<IAreaItem[]>([]);
  const [areaVisible, setAreaVisible] = useState<boolean>(false);
  const [datePanelActive, setDatePanelActive] = useState<DatePanelActiveEnum>();
  const [currentArea, setCurrentArea] = useState<IAreaItem>(null);
  const [lineList, setLineList] = useState<ILineItem[]>([]);
  const [scheduleList, setScheduleList] = useState<ILineItem[]>([]);

  const lineListOrderAndFilter = useMemo(() => {
    const filterLineList = lineList.filter((item) => item.lineStatus === 1);
    const timesMap = {};
    filterLineList.forEach((item) => {
      if (item.departureSiteName.toString() in timesMap) {
        timesMap[item.departureSiteName.toString()] += 1;
      } else {
        timesMap[item.departureSiteName.toString()] = 1;
      }
    });
    const orderLineList = filterLineList
      .sort((a, b) => a.departureSiteName.localeCompare(b.departureSiteName, 'zh'))
      .sort((a, b) => {
        return timesMap[b.departureSiteName] - timesMap[a.departureSiteName];
      });
    return orderLineList;
  }, [lineList]);

  const updateLine = usePersistFn(() => {
    if (!isNil(formValue?.areaId) && !isNil(formValue?.scheduleType)) {
      getLineList({
        areaId: formValue?.areaId,
        scheduleType: formValue?.scheduleType,
      }).then((res) => {
        setLineList(res);
        const lastLine = res.find((item) => item.isLastLine === true);
        if (!isNil(lastLine)) {
          setTimeout(() => {
            setFormValue({
              ...formValue,
              lineId: lastLine.id,
            });
          }, 0);
        } else {
          setTimeout(() => {
            setFormValue({
              ...formValue,
              lineId: undefined,
            });
          }, 0);
        }
      });
    }
  });

  // useEffect(() => {}, [formValue, formValue?.areaId, formValue?.scheduleType, setFormValue]);

  useEffect(() => {
    if (
      formValue?.lineId &&
      formValue?.scheduleType !== undefined &&
      formValue?.scheduleType !== null
    ) {
      getScheDuleList({
        lineId: formValue?.lineId,
        scheduleType: formValue?.scheduleType,
      }).then((res) => {
        setScheduleList(res);
      });
    }
  }, [formValue?.lineId, formValue?.scheduleType]);

  useMount(() => {
    getAreaList().then((res) => {
      setAreaList(res);
    });
  });

  const handleChangeCurrentArea = usePersistFn((val: IAreaItem) => {
    setCurrentArea(val);
  });

  const handleGetLineList = usePersistFn(() => {
    return lineList;
  });

  const handleGetScheduleList = usePersistFn(() => {
    return scheduleList;
  });

  useImperativeHandle(ref, () => {
    return {
      changeCurrentArea: handleChangeCurrentArea,
      getLineList: handleGetLineList,
      getScheduleList: handleGetScheduleList,
    };
  });

  return (
    <div className={styles.container}>
      {/* 区域 */}
      <FormItem
        empty={formValue?.areaId === undefined}
        isLink={true}
        icon={'pc-location'}
        label={t('区域')}
        value={
          currentArea?.areaName ? (
            <>{currentArea?.areaName}</>
          ) : (
            <span style={{ color: '#999DA8' }}>{t('请选择区域')}</span>
          )
        }
        onClick={() => {
          setAreaVisible(true);
        }}
      />
      {/* 预约日期 */}
      <ActionSheet
        className={styles.actionSheet}
        onSelect={(data) => {
          setDatePanelActive(data.id);
        }}
        onCancel={() => {
          //
        }}
        format="id"
        renderItem="name"
        data={[
          {
            id: DatePanelActiveEnum.SINGLE_DAY,
            name: t('单次预约'),
          },
          {
            id: DatePanelActiveEnum.RANGE_DAY,
            name: t('连续预约'),
          },
        ]}
        keygen="id"
      >
        <FormItem
          disabled={!formValue?.areaId}
          isLink={true}
          icon={'calendar-1'}
          label={t('预约日期')}
          empty={formValue?.startDate === undefined || formValue?.endDate === undefined}
          value={
            <>
              {formValue?.startDate === formValue?.endDate &&
                formValue?.startDate !== undefined &&
                formValue?.startDate === moment().format('YYYY-MM-DD') &&
                formValue?.endDate === moment().format('YYYY-MM-DD') && (
                  <Icon name="m-today-fill" color="#F56C0A" fontSize={20} />
                )}
              <RenderDateValue value={formValue} />
            </>
          }
        />
      </ActionSheet>
      {/* 上下班类型 */}
      <FormItemForWorkType
        disabled={!formValue?.endDate}
        value={formValue?.scheduleType}
        startDate={formValue?.startDate}
        dayAppointmentDeadline={currentArea?.dayAppointmentDeadline}
        nightAppointmentDeadline={currentArea?.nightAppointmentDeadline}
        onChange={(v) => {
          let newVals = {};
          // 如果是上班类型，“匹配WMS排班”和“顺延”字段不显示
          if (![ScheduleTypeEnum.DAY_GO_OFF_WORK, ScheduleTypeEnum.NIGHT_GO_OFF_WORK].includes(v)) {
            newVals = {
              matchWmsSchedule: CommonStatusEnum.NO,
              postpone: CommonStatusEnum.NO,
            };
          }
          setFormValue({
            ...formValue,
            ...newVals,
            scheduleId: undefined,
            scheduleType: v,
          });
          setTimeout(updateLine, 0);
        }}
      />
      {/* 线路 */}
      <FormItemRouteLine
        value={formValue?.lineId}
        disabled={formValue?.scheduleType === undefined || formValue?.scheduleType === null}
        lineList={lineListOrderAndFilter}
        onChange={(v) => {
          setFormValue({
            ...formValue,
            scheduleId: undefined,
            lineId: v,
          });
        }}
      />
      {/* 班次 */}
      <FormItemTimeClasses
        disabled={!formValue?.lineId}
        value={formValue?.scheduleId}
        scheduleList={scheduleList}
        onChange={(v) =>
          setFormValue({
            ...formValue,
            scheduleId: v,
          })
        }
      />

      {/* 地区修改弹窗 */}
      <Drawer
        visible={areaVisible}
        position="bottom"
        onClose={() => {
          setAreaVisible(false);
          setDatePanelActive(null);
        }}
      >
        <NPicker
          data={areaList.filter((area) => area.areaStatus === 1)}
          format="id"
          renderItem={'areaName'}
          onCancel={() => {
            setAreaVisible(false);
          }}
          onOk={(v) => {
            if (areaList.find((item) => item.id === v)?.openArea === false) {
              Dialog.alert({
                message: t(
                  '新班车应用目前处于推广阶段，您所在的区域可能暂未启用（预计在8月下旬开放），您可继续使用旧应用预约班车',
                ),
                onOk: () => {
                  // eslint-disable-next-line @shein-bbl/bbl/translate-i18n-byT
                  // window.location.href = '#小程序://S班车/1BKS6QZEVe0CCkn';
                },
              });
              return;
            }
            setAreaVisible(false);
            setFormValue({
              ...formValue,
              scheduleType: undefined,
              lineId: undefined,
              scheduleId: undefined,
              areaId: v,
            });
            setCurrentArea(areaList.find((item) => item.id === v));
            setTimeout(updateLine, 0);
          }}
        />
      </Drawer>
      {/* 预约日期选择弹窗 */}
      <Calendar
        round
        confirmButtonText={t('确认')}
        visible={datePanelActive === DatePanelActiveEnum.SINGLE_DAY}
        drawer={{ position: 'bottom', height: 486, closeable: true }} // 控制Drawer
        onCancel={() => setDatePanelActive(null)}
        onConfirm={(_val, formatValue) => {
          setFormValue({
            ...formValue,
            scheduleType: undefined,
            lineId: undefined,
            scheduleId: undefined,
            startDate: formatValue,
            endDate: formatValue,
          });
          setDatePanelActive(null);
        }}
        minDate={moment().toDate()}
        maxDate={moment().add(6, 'days').toDate()}
        defaultPosition={moment().toDate()}
      />
      {/* 预约日期选择弹窗 */}
      <Calendar
        round
        type="range"
        confirmButtonText={t('确认')}
        visible={datePanelActive === DatePanelActiveEnum.RANGE_DAY}
        drawer={{ position: 'bottom', height: 486, closeable: true }} // 控制Drawer
        onConfirm={(_val, formatValue) => {
          setFormValue({
            ...formValue,
            scheduleType: undefined,
            scheduleId: undefined,
            lineId: undefined,
            startDate: formatValue[0],
            endDate: formatValue[1],
          });
          setDatePanelActive(null);
        }}
        onCancel={() => setDatePanelActive(null)}
        minDate={moment().toDate()}
        maxDate={moment().add(6, 'days').toDate()}
        defaultPosition={moment().toDate()}
      />
    </div>
  );
};

export default forwardRef(AppointmentForm);
