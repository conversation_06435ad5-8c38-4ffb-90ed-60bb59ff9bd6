import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';

/**
 * @description EmptyBus
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const EmptyBus: React.FC<{
  text?: string;
  height?: number | string;
}> = ({ text = t('没有数据'), height = 300 }) => {
  return (
    <div
      style={{
        height: height,
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        gap: '20px',
      }}
    >
      <Icon name="pc-empty-multic" fontSize={80} />
      <p
        style={{
          color: 'gray',
        }}
      >
        {text}
      </p>
    </div>
  );
};
export default EmptyBus;
