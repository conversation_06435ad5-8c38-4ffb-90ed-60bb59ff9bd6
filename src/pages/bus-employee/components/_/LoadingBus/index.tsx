import { Loading } from 'shineout-mobile';

/**
 * @description LoadingBus
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const LoadingBus: React.FC<{
  text?: string;
}> = ({ text }) => {
  return (
    <div
      style={{
        width: '100%',
        height: 300,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        gap: '20px',
      }}
    >
      <Loading />
      <p style={{ color: 'gray' }}>{text}</p>
    </div>
  );
};
export default LoadingBus;
