import {
  ScheduleTypeEnum,
  ScheduleTypeEnumOptions,
} from '@/pages/bus-employee/appointment/interfaces';
import Icon from '@shein-components/Icon';
import classNames from 'classnames';
import styles from './index.less';

/**
 * @description ScheduleTypeItem
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const ScheduleTypeItem: React.FC<{
  scheduleType: ScheduleTypeEnum;
}> = ({ scheduleType }) => {
  const isDay = [ScheduleTypeEnum.DAY_GO_TO_WORK, ScheduleTypeEnum.DAY_GO_OFF_WORK].includes(
    scheduleType,
  );
  return (
    <div
      className={classNames(styles.container, isDay ? styles.containerDay : styles.containerNight)}
    >
      {isDay ? (
        <Icon name="m-sun-fill" color="#F6BB42" fontSize={16} />
      ) : (
        <Icon name="m-moon-fill" color="#4E4BE5" fontSize={16} />
      )}
      <span className={styles.name}>
        {ScheduleTypeEnumOptions.find((item) => item.id === scheduleType)?.name}
      </span>
    </div>
  );
};

export default ScheduleTypeItem;
