import styles from './index.less';

/**
 * @description Row
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const Row: React.FC<{ label: string; value: React.ReactNode | string }> = ({ label, value }) => {
  return (
    <div className={styles.row}>
      <div className={styles.label}>{label}</div>
      <div className={styles.value}>{value}</div>
    </div>
  );
};

export default Row;
