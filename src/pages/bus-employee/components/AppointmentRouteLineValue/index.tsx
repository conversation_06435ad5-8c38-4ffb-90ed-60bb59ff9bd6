import { useRef } from 'react';
import Icon from '@shein-components/Icon';
import { usePersistFn } from '@shein-lego/use';
import classNames from 'classnames';
import { ILineItem } from '../../appointment/interfaces';
import styles from './index.less';

interface IProps {
  value: number;
  onChange: (value: number) => void;
  data: ILineItem[];
}

type IAppointmentRouteLineSingleItemProps = {
  item: ILineItem;
  isChecked: boolean;
  showOnly?: boolean;
  onChange?: any;
};

/**
 * @description AppointmentRouteLineSingleItem
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
export const AppointmentRouteLineSingleItem: React.FC<IAppointmentRouteLineSingleItemProps> = (
  props,
) => {
  const { item, isChecked = false, showOnly = false, onChange } = props;
  return (
    <div
      key={item.id}
      className={classNames(styles.item, isChecked ? styles.itemChecked : null)}
      onClick={() => {
        if (item?.disabled === true || showOnly) return;
        if (!isChecked) {
          onChange?.(item.id);
        }
        if (isChecked) {
          onChange?.(null);
        }
      }}
    >
      <span className={styles.from}> {item.departureSiteName}</span>
      <span className={styles.icon}>
        <Icon name="pc-path-arrow" fontSize={24} />
      </span>
      <span className={styles.to}> {item.destinationSiteName}</span>

      {isChecked && (
        <div className={styles.checkIcon}>
          <Icon name="pc-check-fill" color="#4d90fe" fontSize={12} />
        </div>
      )}
    </div>
  );
};

const AppointmentRouteLineValue: React.FC<IProps> = (props) => {
  const { value, onChange, data } = props;
  const ref = useRef<HTMLDivElement>(null);
  const perviousValue = useRef<number>(null);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const _performAnimation = usePersistFn(() => {
    const el = ref.current;
    const pervious =
      data.find((item) => item.id === perviousValue.current)?.id !== undefined ? true : false;
    const current = data.find((item) => item.id === value)?.id !== undefined ? true : false;

    if (el) {
      if (current === pervious) {
        return;
      }
      if (current === false && pervious === true) {
        el.style.transform = 'translateX(calc(-100% - 20px)';
        el.style.opacity = '0';
      }
      if (current === true && pervious === false) {
        const clientWidth = document.documentElement.clientWidth;
        el.style.opacity = '0.6';
        el.style.transform = `translateX(calc(${Math.min(clientWidth, 750)}px - 120px))`;
      }
    }
  });

  return (
    <div className={styles.routeLineContainer}>
      <div className={styles.animation} ref={ref}>
        <div className={styles.car}></div>
      </div>
      {data.map((item) => {
        const isChecked = value === item.id;
        return (
          <AppointmentRouteLineSingleItem
            key={item.id}
            onChange={onChange}
            item={item}
            isChecked={isChecked}
          />
        );
      })}
    </div>
  );
};

export default AppointmentRouteLineValue;
