.routeLineContainer {
  position: relative;
  display: flex;
  margin-top: 10px;
  flex-wrap: wrap;
  flex-grow: 1;

  .animation {
    /* stylelint-disable-next-line prettier/prettier */
    position: absolute;
    top: -35px;
    opacity: 0;
    transform: translateX(calc(-100% - 20px));
    /* stylelint-disable-next-line prettier/prettier */
    transition: transform .5s, opacity .5s;

    .car {
      background-image: url('./gaotie.svg');
      background-size: contain;
      background-repeat: no-repeat;
      width: 150px;
      height: 150px;
    }
  }

  .item {
    position: relative;
    display: flex;
    height: 42px;
    padding: 10px 12px;
    margin-top: 10px;
    line-height: 1;
    background-color: white;
    border: 1px solid #cccfd7;
    border-radius: 8px;
    flex: 0 0 100%;
    align-items: center;

    .from {
      font-weight: 500;
      text-align: center;
      flex-grow: 1;
      flex-basis: calc(50% - 24px);
    }

    .icon {
      width: 30px;
      color: #666;
      text-align: center;
      flex-shrink: 30px;
    }

    .to {
      font-weight: 500;
      text-align: center;
      flex-grow: 1;
      flex-basis: calc(50% - 24px);
    }

    .checkIcon {
      position: absolute;
      right: -1px;
      bottom: -1px;
    }
  }

  .itemChecked {
    color: #4d90fe;
    background-color: #e9f5fe;
    border: 1px solid #4d90fe;

    .icon {
      color: #4d90fe;
    }
  }
}
