/* stylelint-disable max-nesting-depth */
.container {
  position: relative;
  display: flex;
  padding: 12px;
  margin-bottom: 12px;
  background-color: white;
  border-radius: 4px;
  flex-direction: column;
  gap: 10px;

  // .type {

  // }
  .number {
    display: flex;
    align-items: center;

    .label {
      width: 60px;
      font-size: 12px;
      color: #666c7c;

      &::after {
        content: ':';
      }
    }

    .value {
      font-weight: 500;
    }
  }

  .fullNumber {
    .value {
      color: #999da8;
    }
  }

  .footer {
    display: flex;

    .classes {
      display: flex;
      align-items: center;

      .label {
        width: 60px;
        font-size: 12px;
        color: #666c7c;
      }

      .value {
        font-weight: 500;
      }
    }
  }

  .action {
    display: flex;
    flex-grow: 1;
    justify-content: flex-end;
  }
}
