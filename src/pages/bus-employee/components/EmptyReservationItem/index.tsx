import AutoGapLabel from '@/_/components/AutoGapLabel';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';
import { Button, Tag } from 'shineout-mobile';
import {
  IEmptyReservationItem as EmptyReservationItemType,
  ReserveTypeEnum,
} from '../../empty-reservation/interfaces';
import styles from './index.less';

interface IProps {
  item: EmptyReservationItemType;
  onReserve: (id: number) => void;
}

interface ITypeValueProps {
  status: ReserveTypeEnum;
}

/**
 * @description TypeValue
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const TypeValue: React.FC<ITypeValueProps> = ({ status }) => {
  const statusMap = {
    [ReserveTypeEnum.CAN_RESERVE]: (
      <Tag color="#E3EDFA" textColor="#197AFA">
        {t('可预订')}
      </Tag>
    ),
    [ReserveTypeEnum.RESERVED]: (
      <Tag color="#E1F5EA" textColor="#04C75F">
        {t('已预订')}
      </Tag>
    ),
    [ReserveTypeEnum.SEAT_FULL]: (
      <Tag color="#FAE9E9" textColor="#F96156">
        {t('已满座')}
      </Tag>
    ),
  };
  return statusMap[status];
};

const IEmptyReservationItem: React.FC<IProps> = (props) => {
  const { item, onReserve } = props;
  return (
    <div className={styles.container}>
      <div className={styles.type}>
        <TypeValue status={item.reserveType} />
      </div>
      <div
        className={classNames(
          styles.number,
          item.reserveType === ReserveTypeEnum.SEAT_FULL ? styles.fullNumber : null,
        )}
      >
        {/* <span className={styles.label}>{t('班次')}</span> */}
        <AutoGapLabel className={styles.label} label={t('班次')} />
        <span className={styles.value}>{item.scheduleTime}</span>
      </div>
      {item.reserveType === ReserveTypeEnum.CAN_RESERVE && (
        <div className={styles.footer}>
          <div className={styles.classes}>
            {/* <span className={styles.label}>{t('空闲座位')}</span> */}
            <AutoGapLabel className={styles.label} label={t('空闲座位')} />
            <span className={styles.value}>{item.freeSeat}</span>
          </div>
          <div className={styles.action}>
            {item.reserveType === ReserveTypeEnum.CAN_RESERVE && (
              <Button type="primary" size="mini" onClick={() => onReserve(item.id)}>
                {t('立即预订')}
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default IEmptyReservationItem;
