import { useRef } from 'react';
import Icon from '@shein-components/Icon';
import { usePersistFn } from '@shein-lego/use';
import classNames from 'classnames';
import { IScheduleItem } from '../../appointment/interfaces';
import styles from './index.less';

interface IProps {
  value: number;
  onChange: (v: number) => void;
  data: IScheduleItem[];
}

/**
 * @description AppointmentTimeClasses
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const AppointmentTimeClasses: React.FC<IProps> = (props) => {
  const { value, onChange, data } = props;
  const ref = useRef<HTMLDivElement>(null);
  const perviousValue = useRef<number>(null);

  const performAnimation = usePersistFn(() => {
    const el = ref.current;
    const pervious =
      data.find((item) => item.id === perviousValue.current)?.id !== undefined ? true : false;
    const current = data.find((item) => item.id === value)?.id !== undefined ? true : false;
    if (el) {
      if (current === pervious) {
        return;
      }
      if (current === false && pervious === true) {
        el.style.transform = 'translateY(200px)';
        el.style.opacity = '0';
      }
      if (current === true && pervious === false) {
        el.style.transform = 'translateY(0px)';
        el.style.opacity = '0.4';
      }
    }
  });

  return (
    <div className={styles.timeClassesContainer}>
      <div className={styles.animation} ref={ref}>
        <div className={styles.clock}></div>
      </div>
      {data.map((item) => {
        const isChecked = value === item.id;
        return (
          <div
            key={item.id}
            className={classNames(
              styles.item,
              isChecked ? styles.itemChecked : null,
              item.disabled ? styles.itemDisabled : null,
            )}
            onClick={() => {
              if (item.disabled) return;
              perviousValue.current = value;
              if (!isChecked) {
                onChange(item.id);
              }
              if (isChecked) {
                onChange(null);
              }
              setTimeout(performAnimation, 0);
            }}
          >
            {item.scheduleTime}
            {isChecked && (
              <div className={styles.checkIcon}>
                <Icon name="pc-check-fill" color="#4d90fe" fontSize={12} />
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default AppointmentTimeClasses;
