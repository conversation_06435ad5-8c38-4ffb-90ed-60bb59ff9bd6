.radioContainer {
  position: relative;
  display: flex;
  margin-top: 10px;
  flex-wrap: wrap;
  flex-grow: 1;

  .animation {
    position: absolute;
    top: -170px;
    right: 110px;
    display: flex;
    // 透明度
    /* stylelint-disable-next-line prettier/prettier */
    opacity: 0;
    transform: rotate(45deg);
    flex-direction: column;
    gap: 400px;
    /* stylelint-disable-next-line prettier/prettier */
    transition: opacity .5s ease-out, transform .5s ease-out;

    .day {
      width: 150px;
      height: 150px;
      background-image: url('./day.svg');
      background-repeat: no-repeat;
      background-size: contain;
      transform: rotate(15deg);
    }

    .night {
      position: relative;
      top: -30px;
      right: -40px;
      width: 150px;
      height: 150px;
      transform: rotate(90deg);
      background-image: url('./night.svg');
      background-repeat: no-repeat;
      background-size: contain;
    }
  }

  .radioItem {
    position: relative;
    height: 42px;
    padding: 12px 6px;
    margin: 12px 12px 16px;
    font-size: 14px;
    line-height: 1;
    text-align: center;
    background-color: white;
    border: 1px solid #cccfd7;
    border-radius: 4px;
    flex: 0 0 calc(50% - 24px);
    align-items: center;

    .dayIcon {
      position: relative;
      top: 2px;
      display: inline-block;
      width: 14px;
      height: 14px;
      background-image: url('./day.svg');
      background-repeat: no-repeat;
      background-size: contain;
    }

    .nightIcon {
      position: relative;
      top: 2px;
      display: inline-block;
      width: 14px;
      height: 14px;
      background-image: url('./night.svg');
      background-repeat: no-repeat;
      background-size: contain;
    }

    .desc {
      position: absolute;
      bottom: -18px;
      width: 100%;
      font-size: 12px;
      color: #ccc;
      text-align: center;
    }

    .descDisabled {
      color: red !important;
    }

    .checkIcon {
      position: absolute;
      right: -1px;
      bottom: -1px;
    }
  }

  .radioChecked {
    color: #1890ff;
    background-color: #e9f5fe;
    border: 1px solid #1890ff;
  }

  .radioDisabled {
    color: #ccc;
    background-color: #eee;
    border: 1px solid #ddd;

    .desc {
      color: #ccc;
    }
  }
}
