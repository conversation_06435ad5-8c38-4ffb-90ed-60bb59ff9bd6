import { useRef } from 'react';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import classNames from 'classnames';
import styles from './index.less';

export interface IRadioVItem {
  id: number;
  name: string;
  icon: string;
  disabled: boolean;
  desc: string;
}

interface IProps {
  value: number;
  onChange: (value: number) => void;
  data: IRadioVItem[];
}

const AppointmentRadioValue: React.FC<IProps> = (props) => {
  const { value, onChange, data } = props;
  const ref = useRef<HTMLDivElement>(null);
  const perviousValue = useRef<number>(null);

  const performAnimation = usePersistFn(() => {
    const el = ref.current;
    const perviousDayOrNight = data.find((item) => item.id === perviousValue.current)?.icon;
    const currentDayOrNight = data.find((item) => item.id === value)?.icon;
    if (el) {
      if (currentDayOrNight === perviousDayOrNight) {
        return;
      }
      if ([currentDayOrNight, perviousDayOrNight].includes(undefined)) {
        if (currentDayOrNight === undefined && perviousDayOrNight === 'day') {
          // 消失
          el.style.transform = 'rotate(45deg)';
          el.style.opacity = '0';
        }
        if (currentDayOrNight === undefined && perviousDayOrNight === 'night') {
          // 消失
          el.style.transform = 'rotate(235deg)';
          el.style.opacity = '0';
        }
        if (perviousDayOrNight === undefined && currentDayOrNight === 'day') {
          // 显现
          el.style.opacity = '.5';
          el.style.transform = 'rotate(45deg)';
        }
        if (perviousDayOrNight === undefined && currentDayOrNight === 'night') {
          // 显现
          el.style.opacity = '.5';
          el.style.transform = 'rotate(235deg)';
        }
      }
      if (currentDayOrNight === 'day' && perviousDayOrNight === 'night') {
        // 夜转白 旋转180度
        el.style.transform = 'rotate(45deg)';
      }
      if (currentDayOrNight === 'night' && perviousDayOrNight === 'day') {
        // 白转夜
        el.style.transform = 'rotate(235deg)';
      }
    }
  });

  return (
    <div className={styles.radioContainer}>
      <div className={styles.animation} ref={ref}>
        <div className={styles.day}></div>
        <div className={styles.night}></div>
      </div>
      {data.map((item) => {
        const isChecked = item.id === value;
        return (
          <div
            key={item.id}
            onClick={() => {
              if (item.disabled) return;
              perviousValue.current = value;
              if (isChecked) {
                onChange(null);
              } else {
                onChange(item.id);
              }
              setTimeout(performAnimation, 0);
            }}
            className={classNames(
              styles.radioItem,
              isChecked ? styles.radioChecked : null,
              item.disabled ? styles.radioDisabled : null,
            )}
          >
            <span
              className={classNames(
                item.icon === 'day' ? styles.dayIcon : null,
                item.icon === 'night' ? styles.nightIcon : null,
              )}
            />
            <span>{item.name}</span>
            <div className={classNames(styles.desc, item.disabled ? styles.descDisabled : null)}>
              {item.disabled ? t('已截止') : item.desc}
            </div>
            {isChecked && (
              <div className={styles.checkIcon}>
                <Icon name="pc-check-fill" color="#4d90fe" fontSize={12} />
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default AppointmentRadioValue;
