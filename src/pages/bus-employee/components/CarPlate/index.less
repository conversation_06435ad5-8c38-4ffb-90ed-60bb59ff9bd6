.bluePlate {
  position: relative;
  display: inline-block;
  width: 70px;
  margin: 0 5px 5px 0;
  aspect-ratio: 2/1;
  background-image: url('./blue-plate.svg');
  background-repeat: round;

  .number {
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 9px;
    font-weight: bold;
    color: white;
    word-break: keep-all;
    white-space: nowrap;
    transform: translate(-50%, -50%);
  }
}

.greenPlate {
  position: relative;
  display: inline-block;
  width: 70px;
  margin: 0 5px 5px 0;
  aspect-ratio: 2/1;
  background-image: url('./green-plate.svg');
  background-repeat: round;

  .number {
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 9px;
    font-weight: bold;
    color: white;
    word-break: keep-all;
    white-space: nowrap;
    transform: translate(-50%, -50%);
  }
}
