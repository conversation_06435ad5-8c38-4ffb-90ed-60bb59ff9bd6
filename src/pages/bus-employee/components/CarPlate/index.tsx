import { useMemo } from 'react';
import styles from './index.less';

interface IProps {
  number: string;
}

const CarPlate: React.FC<IProps> = ({ number }) => {
  // 判断车牌是不是新能源
  const isNewEnergy = useMemo(() => {
    try {
      if (number.length > 7) {
        return true;
      } else {
        return false;
      }
    } catch (_error) {
      return false;
    }
  }, [number]);

  return (
    <div className={isNewEnergy ? styles.greenPlate : styles.bluePlate}>
      <span className={styles.number}>{number}</span>
    </div>
  );
};

export default CarPlate;
