.container {
  position: relative;
  display: flex;
  padding: 12px;
  background-color: white;
  flex-wrap: wrap;
  border-bottom: #eee 1px solid;

  .menu {
    display: flex;
    padding: 12px 0;
    flex: 0 0 25%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .icon {
      position: relative;
      z-index: 2;
      padding: 12px;
      overflow: hidden;
      border-radius: 50%;

      .shadow {
        position: absolute;
        z-index: -1;
        width: 30px;
        height: 30px;
        background-color: rgb(256 256 256 / 6%);
        border-radius: 50%;
        transform: translateX(30%) translateY(-50%);
      }
    }

    .name {
      color: #333e59;
    }
  }
}
