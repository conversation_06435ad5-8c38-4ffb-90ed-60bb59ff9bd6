import { useNavigate } from 'react-router-dom';
import Icon from '@shein-components/Icon';
import styles from './index.less';

interface IProps {
  menus: IMenusType[];
}

type IMenusType = { name: string; path: string; gradient: string[]; icon: string };

type IMenusItemType = { menu: IMenusType };

/**
 * @description MenusItem
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const MenusItem: React.FC<IMenusItemType> = ({ menu }) => {
  const { name, gradient, icon, path } = menu;
  const navigate = useNavigate();

  return (
    <div className={styles.menu} onClick={() => navigate(path)}>
      <div
        className={styles.icon}
        style={{
          background: `linear-gradient(135deg, ${gradient[0]}, ${gradient[1]})`,
        }}
      >
        <Icon name={icon} fontSize={28} color={'white'} />
        <div className={styles.shadow}></div>
      </div>
      <div className={styles.name}>{name}</div>
    </div>
  );
};

/**
 * @description AppointmentHeader
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const AppointmentHeader: React.FC<IProps> = (props) => {
  const { menus } = props;
  return (
    <div className={styles.container}>
      {menus.map((menu) => {
        return <MenusItem key={menu.path} menu={menu} />;
      })}
    </div>
  );
};

export default AppointmentHeader;
