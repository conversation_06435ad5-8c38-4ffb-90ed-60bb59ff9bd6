.container {
  padding: 10px 20px;
  margin-bottom: 10px;
  overflow: hidden;
  background-color: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;

  .footer {
    display: flex;
    height: 30px;
    padding-top: 5px;
    margin-top: 5px;
    border-top: 1px dashed #ddd;
    flex-direction: row;

    .left {
      display: flex;
      font-size: 14px;
      color: #141737;
      flex-grow: 1;
      justify-content: flex-start;
      align-items: center;
    }

    .right {
      flex-grow: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 2px;
      // margin-right: 5px;
    }
  }
}

.activated {
  // max-height: 450px;
}

.canceled {
  .row .value {
    color: #999;
  }
}

.row {
  display: flex;
  padding: 5px 0;
  align-items: center;
}

.row .label {
  display: inline-block;
  width: 65px;
  min-width: 65px;
  padding-top: 2px;
  margin-right: 10px;
  font-size: 14px;
  line-height: 1.5;
  color: #999;

  &::after {
    content: ':';
  }
}

.row .value {
  display: inline-block;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.5;
  color: #333;
  flex-grow: 1;
}
