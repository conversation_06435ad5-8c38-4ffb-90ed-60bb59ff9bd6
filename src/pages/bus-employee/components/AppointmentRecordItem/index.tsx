import { useState } from 'react';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';
import { Button, Dialog, Tag, Toast } from 'shineout-mobile';
import { cancelAppointment } from '../../appointment/services';
import { AppointmentStatusEnum, IAppointmentItem } from '../../appointment-record/interfaces';
import WorkTypeItem from '../_/WorkTypeItem';
import CarPlate from '../CarPlate';
import RouteLineStation from '../RouteLineStation';
import styles from './index.less';

interface IProps {
  item: IAppointmentItem;
  onFreshList: () => void;
}

interface IRowProps {
  label: string;
  value: React.ReactNode | string;
}

interface IFooterProps {
  orderNo: string;
  toggle: boolean;
  status: AppointmentStatusEnum;
  doToggle: () => void;
  doCancel: () => void;
  cancelability: boolean;
}

/**
 * @description Row
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const Row: React.FC<IRowProps> = ({ label, value }) => {
  return (
    <div className={styles.row}>
      <div className={styles.label}>{label}</div>
      <div className={styles.value}>{value}</div>
    </div>
  );
};

/**
 * @description StatusValue
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const StatusValue: React.FC<{ status: AppointmentStatusEnum }> = ({ status }) => {
  const StatusMap = {
    [AppointmentStatusEnum.WAITING_FOR_SCHEDULE]: (
      <Tag color="#E3EDFA" textColor="#197AFA">
        {t('待排班')}
      </Tag>
    ),
    [AppointmentStatusEnum.APPOINTMENT_SUCCESS]: (
      <Tag color="#E1F5EA" textColor="#04C75F">
        {t('预约成功')}
      </Tag>
    ),
    [AppointmentStatusEnum.CANCELED]: (
      <Tag color="#FAE9E9" textColor="#F96156">
        {t('已取消')}
      </Tag>
    ),
  };
  return <>{StatusMap[status]}</>;
};

/**
 * @description Footer
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Footer: React.FC<IFooterProps> = (props) => {
  const { toggle, status, doToggle, orderNo, doCancel, cancelability } = props;
  return (
    <div className={styles.footer}>
      <div className={styles.left}>{orderNo}</div>
      <div className={styles.right}>
        {status === AppointmentStatusEnum.WAITING_FOR_SCHEDULE && cancelability ? (
          <Button type="primary" size="mini" plain onClick={doCancel}>
            {t('取消预约')}
          </Button>
        ) : (
          <Button type="primary" text onClick={doToggle}>
            {toggle ? t('收起') : t('展开')}
          </Button>
        )}
      </div>
    </div>
  );
};

/**
 * @description IAppointmentRecordItem
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const IAppointmentRecordItem: React.FC<IProps> = (props) => {
  const { item, onFreshList } = props;
  const [toggle, setToggle] = useState(false);

  return (
    <div
      className={classNames(
        styles.container,
        toggle ? styles.activated : null,
        item.appointmentStatus === AppointmentStatusEnum.CANCELED ? styles.canceled : null,
      )}
    >
      <Row label={t('状态')} value={<StatusValue status={item.appointmentStatus} />} />
      <Row
        label={t('班次')}
        value={
          <div style={{ paddingTop: 2, fontWeight: 500, fontSize: 16 }}>
            {item.appointmentSchedule}{' '}
          </div>
        }
      />
      <Row
        label={t('线路')}
        value={<RouteLineStation station={[item.departureSiteName, item.destinationSiteName]} />}
      />
      {toggle && (
        <Row label={t('预约时间')} value={<div style={{ paddingTop: 2 }}>{item.submitTime}</div>} />
      )}
      {toggle && (
        <Row
          label={t('上班类型')}
          value={
            <div style={{ paddingTop: 2, display: 'block', width: 100 }}>
              {<WorkTypeItem scheduleType={item.scheduleType} />}
            </div>
          }
        />
      )}

      {toggle && item.appointmentStatus !== AppointmentStatusEnum.CANCELED && (
        <Row
          label={t('车牌号')}
          value={
            item.licensePlateList?.length === 0 || item.licensePlateList === null
              ? '-'
              : item.licensePlateList?.map((number) => <CarPlate key={number} number={number} />)
          }
        />
      )}
      {toggle && item.appointmentStatus === AppointmentStatusEnum.CANCELED && (
        <Row
          label={t('取消原因')}
          value={<div style={{ paddingTop: 2 }}>{item.cancelReason}</div>}
        />
      )}
      <Footer
        orderNo={item.appointmentNum}
        toggle={toggle}
        status={item.appointmentStatus}
        cancelability={item.cancelability}
        doToggle={() => setToggle(!toggle)}
        doCancel={() => {
          Dialog.confirm({
            title: t('取消预约'),
            message: (
              <div style={{ textAlign: 'left' }}>
                <div>
                  {t(' 预约单号： ')}
                  {item.appointmentNum}
                </div>
                <div>
                  {t(' 线路： ')}
                  {item.departureSiteName} - {item.destinationSiteName}
                </div>
                <div>
                  {t(' 班次： ')}
                  {item.appointmentSchedule}
                </div>
              </div>
            ),
            cancelButtonText: t('我再想想'),
            confirmButtonText: t('确认取消'),
            onOk: () => {
              cancelAppointment({
                id: item.id,
              }).then(() => {
                Toast.success(t('操作成功'));
                onFreshList();
              });
            },
          });
        }}
      />
    </div>
  );
};

export default IAppointmentRecordItem;
