import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Page } from '@/_/components';
import Icon from '@/_/components/Icon';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';
import { Button } from 'shineout-mobile';
import styles from './index.less';

/**
 * @description Main
 * @returns {unknown} desc
 */
const Main: React.FC = () => {
  const navigate = useNavigate();

  /**
   * @description jumpTo
   * @param {unknown} url desc
   * @returns {unknown} desc
   */
  const jumpTo = (url: string) => {
    navigate(url);
  };

  const menus = [
    {
      menuName: t('排班管理'),
      icon: 'pc-paysuccess-shineout-fill',
      iconClassname: styles.menuApply,
      path: '/bus-operate/schedule-task',
    },
    {
      menuName: t('任务管理'),
      icon: 'm-asset-inventory-multic',
      iconClassname: styles.menuTask,
      path: '/bus-operate/task-manage',
    },
  ];

  return (
    <Page
      title={t('班车运营')}
      footer={
        <Button type="default" round block onClick={() => navigate(-1)}>
          {t('返回')}
        </Button>
      }
    >
      <div className={styles.content}>
        {menus.map((menu) => (
          <div
            className={styles.menuItem}
            key={menu.menuName}
            onClick={menu.path ? () => jumpTo(menu.path) : undefined}
          >
            <div className={styles.menuLeft}>
              <div className={classNames(styles.menuIcon, menu.iconClassname)}>
                <Icon name={menu.icon} />
              </div>
              <div className={styles.menuName}>{menu.menuName}</div>
            </div>
            <div className={styles.menuRight}>
              <Icon name="m-big-arrowsign1-right-shineout" />
            </div>
          </div>
        ))}
      </div>
    </Page>
  );
};

export default Main;
