import { ECommonStatus } from '@/_/interfaces';
import { t } from '@shein-bbl/react';

export enum EAdditionType {
  /** 延后下班 */
  DELAY = 0,
  /** 提前下班 */
  ADVANCE = 1,
}

export const AdditionTypeOptions = [
  { label: t('提前下班'), value: EAdditionType.ADVANCE },
  { label: t('延后下班'), value: EAdditionType.DELAY },
];

export enum EScheduleTypeEnum {
  /** 白天上班 */
  DAY_GO_TO_WORK = 0,
  /** 白天下班 */
  DAY_GO_OFF_WORK = 1,
  /** 夜晚上班 */
  NIGHT_GO_TO_WORK = 2,
  /** 夜晚下班 */
  NIGHT_GO_OFF_WORK = 3,
}

export const ScheduleTypeEnumOptions = [
  { name: t('白班/上班'), id: EScheduleTypeEnum.DAY_GO_TO_WORK, icon: 'day', isMorning: true },
  { name: t('白班/下班'), id: EScheduleTypeEnum.DAY_GO_OFF_WORK, icon: 'day', isMorning: false },
  { name: t('夜班/上班'), id: EScheduleTypeEnum.NIGHT_GO_TO_WORK, icon: 'night', isMorning: false },
  { name: t('夜班/下班'), id: EScheduleTypeEnum.NIGHT_GO_OFF_WORK, icon: 'night', isMorning: true },
];

export interface IAddictionTaskParams {
  /** 线路id */
  lineId: number;
  /** 班次id */
  scheduleId: number;
  /** 车辆id 集合 */
  vehicleIds: number[];
  date: string;
  /** 加趟类型 */
  additionalType?: EAdditionType;
}

export type IAddictionTaskForm = Partial<
  IAddictionTaskParams & {
    /** 所属区域 */
    areaId: number;
    /** 上下班类型 */
    scheduleType: EScheduleTypeEnum;
  }
>;

export interface IAreaItem {
  id: number;
  areaName: string;
  areaCode: string;
  areaStatus: ECommonStatus;
}

export interface ILineItem {
  /** 线路id */
  lineId?: number;
  /** 线路名称 */
  lineName?: string;
}

export interface IScheduleItem {
  /** 班次id */
  id?: number;
  /** 班次时间 HH:mm */
  scheduleTime?: string;
  /** 班次类型 */
  scheduleType?: number;
}

/** 不能指派原因 */
export enum NoAssignReasonEnums {
  /** 任务冲突 */
  TASK_CONFLICT = 0,
  /** 司机休息 */
  DRIVER_REST,
  /** 车辆停用 */
  VEHICLE_STOP,
}

export interface IVehicleItem {
  /** id */
  id?: number;
  /** 车牌号 */
  licensePlate?: string;
  /** 座位数量 */
  seatNumber?: number;
  /** 任务数量 */
  taskCount?: number;
  /** 不能指派原因 */
  notAssignedReason?: NoAssignReasonEnums;
  /** 不能指派原因 */
  notAssignedReasonName?: string;
}
