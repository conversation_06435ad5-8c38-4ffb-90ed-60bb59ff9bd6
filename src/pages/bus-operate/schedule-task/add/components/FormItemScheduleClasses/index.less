.timeClassesContainer {
  position: relative;
  display: flex;
  margin-top: 10px;
  flex-wrap: wrap;
  flex-grow: 1;

  .animation {
    position: absolute;
    top: -90px;
    right: -60px;
    /* stylelint-disable-next-line prettier/prettier */
    opacity: .4;
    /* stylelint-disable-next-line prettier/prettier */
    transition: all .3s;
    transform: translateY(200px);

    .clock {
      background-image: url('./clock.svg');
      background-size: contain;
      background-repeat: no-repeat;
      width: 130px;
      height: 130px;
    }
  }

  .item {
    position: relative;
    display: flex;
    height: 42px;
    padding: 12px 8px;
    margin: 12px;
    line-height: 1;
    word-break: keep-all;
    background-color: white;
    border: 1px solid #cccfd7;
    border-radius: 6px;
    flex: 0 0 calc(25% - 24px);
    justify-content: center;
    align-items: center;

    .checkIcon {
      position: absolute;
      right: -1px;
      bottom: -1px;
    }
  }

  .itemChecked {
    color: #4d90fe;
    background-color: #e9f5fe;
    border: 1px solid #4d90fe;
  }

  .itemDisabled {
    color: #ccc;
    background-color: #666c7c;
    border: 1px solid #ddd;
  }
}
