import { useMemo, useRef } from 'react';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import classNames from 'classnames';
import moment from 'moment';
import { IScheduleItem } from '../../interfaces';
import FormItem from '../FormItem';
import styles from './index.less';

interface IProps {
  value: number;
  onChange: (v: number) => void;
  disabled?: boolean;
  options?: IScheduleItem[];
  date?: string;
}

const FormItemTimeClasses: React.FC<IProps> = (props) => {
  const { value, onChange, disabled = false, options, date } = props;
  const ref = useRef<HTMLDivElement>(null);

  const perviousValue = useRef<number>(null);

  const performAnimation = usePersistFn(() => {
    const el = ref.current;
    const pervious =
      options.find((item) => item.id === perviousValue.current)?.id !== undefined ? true : false;
    const current = options.find((item) => item.id === value)?.id !== undefined ? true : false;
    if (el) {
      if (current === pervious) {
        return;
      }
      if (current === false && pervious === true) {
        el.style.transform = 'translateY(200px)';
        el.style.opacity = '0';
      }
      if (current === true && pervious === false) {
        el.style.transform = 'translateY(0px)';
        el.style.opacity = '0.4';
      }
    }
  });

  const displayOptions = useMemo(() => {
    if (!date) {
      return options.map((item) => ({
        ...item,
        disabled: true,
      }));
    }
    const now = moment();
    return options.map((item) => {
      const time = moment(`${date} ${item.scheduleTime}`);
      return {
        ...item,
        // 只能选择未来的时间
        disabled: time.isSameOrBefore(now),
      };
    });
  }, [options, date]);

  return (
    <FormItem
      label={t('班次')}
      value={
        <div className={styles.timeClassesContainer}>
          <div className={styles.animation} ref={ref}>
            <div className={styles.clock}></div>
          </div>
          {displayOptions.map((item) => {
            const isChecked = value === item.id;
            return (
              <div
                key={item.id}
                className={classNames(
                  styles.item,
                  isChecked ? styles.itemChecked : null,
                  item.disabled ? styles.itemDisabled : null,
                )}
                onClick={() => {
                  if (item.disabled) return;
                  perviousValue.current = value;
                  if (!isChecked) {
                    onChange(item.id);
                  }
                  if (isChecked) {
                    onChange(null);
                  }
                  setTimeout(performAnimation, 0);
                }}
              >
                {item.scheduleTime}
                {isChecked && (
                  <div className={styles.checkIcon}>
                    <Icon name="pc-check-fill" color="#4d90fe" fontSize={12} />
                  </div>
                )}
              </div>
            );
          })}
        </div>
      }
      icon="pc-time"
      isColumnMode
      disabled={disabled}
    />
  );
};

export default FormItemTimeClasses;
