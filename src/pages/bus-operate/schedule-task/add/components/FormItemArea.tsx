import React, { useMemo } from 'react';
import { t } from '@shein-bbl/react';
import { useSetState, useUpdateEffect } from 'ahooks';
import { isNil } from 'lodash';
import { NPicker } from 'shineout-mobile';
import { IAreaItem } from '../interfaces';
import FormItem from './FormItem';

export const LOCAL_STORAGE_KEY_AREA = 'schedule_task_area';

interface IFormItemAreaProps {
  value?: number;
  onChange?: (value: number) => void;
  disabled?: boolean;
  options?: IAreaItem[];
}

/**
 * @description FormItemArea
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const FormItemArea: React.FC<IFormItemAreaProps> = (props) => {
  const { value, onChange, disabled, options } = props;
  const [state, setState] = useSetState({
    showPicker: false,
    innerValue: value,
  });

  useUpdateEffect(() => {
    setState({
      innerValue: value,
    });
  }, [value]);

  const currentArea = useMemo(() => {
    return options?.find((item) => item.id === value);
  }, [options, value]);

  return (
    <>
      <FormItem
        empty={isNil(value)}
        isLink
        icon="pc-location"
        label={t('区域')}
        value={
          isNil(value) ? (
            <span style={{ color: '#999DA8' }}>{t('请选择区域')}</span>
          ) : (
            <>{currentArea?.areaName}</>
          )
        }
        onClick={() => {
          if (!disabled) {
            setState({
              showPicker: true,
            });
          }
        }}
      />
      {options.length > 0 && (
        <NPicker
          drawer={{
            position: 'bottom',
            visible: state.showPicker,
          }}
          data={options}
          value={state.innerValue}
          renderItem="areaName"
          format="id"
          disabled={disabled}
          keygen="id"
          onChange={(val) =>
            setState({
              innerValue: val,
            })
          }
          onCancel={() =>
            setState({
              showPicker: false,
            })
          }
          onOk={(val: number) => {
            onChange?.(val);
            localStorage.setItem(LOCAL_STORAGE_KEY_AREA, val.toString());
            setState({
              showPicker: false,
            });
          }}
        />
      )}
    </>
  );
};

export default FormItemArea;
