import React, { useMemo } from 'react';
import { t } from '@shein-bbl/react';
import { isNil } from 'lodash';
import { Checkbox } from 'shineout-mobile';
import { IVehicleItem } from '../interfaces';
import FormItem from './FormItem';

interface IFormItemCarProps {
  value?: number[];
  onChange?: (value: number[]) => void;
  disabled?: boolean;
  options?: IVehicleItem[];
}

/**
 * @description FormItemCar
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const FormItemCar: React.FC<IFormItemCarProps> = (props) => {
  const { value, onChange, disabled, options } = props;

  const displayOptions = useMemo(() => {
    return options?.map((item) => ({
      label:
        `${item.licensePlate}${t('（今日任务数：')}${item.taskCount}）${item.seatNumber}` +
        t('座') +
        (item.notAssignedReasonName ? `（${item.notAssignedReasonName}）` : ''),
      value: item.id,
      disabled: !isNil(item.notAssignedReason),
    }));
  }, [options]);

  return (
    <>
      <FormItem
        empty={isNil(value)}
        isLink
        icon="pc-bus-fill"
        label={t('指派车辆')}
        disabled={disabled}
        value={
          <Checkbox.Group
            value={value}
            onChange={(v: number[]) => {
              onChange?.(v);
            }}
            disabled={(d) => disabled || d.disabled}
            data={displayOptions}
            renderItem={(item) => item.label}
            format="value"
            keygen="value"
            className="overflow-auto w-full"
            style={{ maxHeight: 164 }}
          />
        }
        isColumnMode
      />
    </>
  );
};

export default FormItemCar;
