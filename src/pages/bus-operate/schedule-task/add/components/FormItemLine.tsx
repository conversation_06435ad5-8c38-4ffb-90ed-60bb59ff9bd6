import React, { useMemo } from 'react';
import { t } from '@shein-bbl/react';
import { useSetState, useUpdateEffect } from 'ahooks';
import { isNil } from 'lodash';
import { NPicker } from 'shineout-mobile';
import { ILineItem } from '../interfaces';
import FormItem from './FormItem';

interface IFormItemLineProps {
  value?: number;
  onChange?: (value: number) => void;
  disabled?: boolean;
  options?: ILineItem[];
}

/**
 * @description FormItemLine
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const FormItemLine: React.FC<IFormItemLineProps> = (props) => {
  const { value, onChange, disabled, options } = props;
  const [state, setState] = useSetState({
    showPicker: false,
    innerValue: value,
  });

  useUpdateEffect(() => {
    setState({
      innerValue: value,
    });
  }, [value]);

  const currentLine = useMemo(() => {
    return options?.find((item) => item.lineId === value);
  }, [options, value]);

  return (
    <>
      <FormItem
        empty={isNil(value)}
        isLink
        icon="gd-ts"
        label={t('线路')}
        value={
          isNil(value) ? (
            <span style={{ color: '#999DA8' }}>{t('请选择路线')}</span>
          ) : (
            <>{currentLine?.lineName}</>
          )
        }
        onClick={() => {
          if (!disabled) {
            setState({
              showPicker: true,
            });
          }
        }}
        disabled={disabled}
      />
      <NPicker
        drawer={{
          position: 'bottom',
          visible: state.showPicker,
        }}
        title={t('请选择路线')}
        data={options}
        value={state.innerValue}
        renderItem="lineName"
        format="lineId"
        disabled={disabled}
        keygen="lineId"
        onChange={(val) =>
          setState({
            innerValue: val,
          })
        }
        onCancel={() =>
          setState({
            showPicker: false,
          })
        }
        onOk={(val: number) => {
          onChange?.(val);
          setState({
            showPicker: false,
          });
        }}
      />
    </>
  );
};

export default FormItemLine;
