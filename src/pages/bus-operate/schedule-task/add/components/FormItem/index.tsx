import { ReactNode } from 'react';
import Icon from '@shein-components/Icon';
import classNames from 'classnames';
import styles from './index.less';

interface IProps {
  label: ReactNode;
  value: ReactNode;
  icon: string;
  isLink?: boolean;
  isCoincidence?: boolean;
  isColumnMode?: boolean;
  disabled?: boolean;
  onClick?: () => void;
  empty?: boolean;
}

/**
 * @description FormItem
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const FormItem: React.FC<IProps> = (props) => {
  const {
    icon,
    label,
    value,
    isLink,
    isColumnMode,
    isCoincidence,
    disabled = false,
    onClick,
    empty = false,
  } = props;
  return (
    <div
      className={classNames(styles.item, disabled ? styles.disabledItem : null)}
      style={isColumnMode ? { flexDirection: 'column' } : null}
      onClick={() => onClick?.()}
    >
      <div className={styles.label}>
        <Icon name={icon} fontSize={16} color={'rgb(25, 122, 250)'} />
        <span>{label}</span>
        {isCoincidence && isColumnMode && (
          <div className={styles.icon} style={{ textAlign: 'right', flexGrow: 1 }}>
            {isLink && !disabled && (
              <Icon name="arr-right" fontSize={12} color={empty ? 'rgb(153, 157, 168)' : '#000'} />
            )}
          </div>
        )}
      </div>
      <div className={styles.value} style={isColumnMode ? { justifyContent: 'flex-start' } : null}>
        {!disabled && (
          <div
            className={styles.valueText}
            style={isColumnMode ? { justifyContent: 'flex-start' } : null}
          >
            {value}
          </div>
        )}
        {!isColumnMode && (
          <div className={styles.icon}>
            {isLink && !disabled && (
              <Icon name="arr-right" fontSize={12} color={empty ? 'rgb(153, 157, 168)' : '#000'} />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FormItem;
