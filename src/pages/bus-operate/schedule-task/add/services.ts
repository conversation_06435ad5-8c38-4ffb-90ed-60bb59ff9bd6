import { ECommonStatus } from '@/_/interfaces';
import { get, post } from '@/utils';
import {
  IAddictionTaskParams,
  IAreaItem,
  ILineItem,
  IScheduleItem,
  IVehicleItem,
} from './interfaces';

/**
 * 区域下启用的线路列表
 * @param areaId
 * @returns
 */
export const getLineByAreaId = (areaId: number) => {
  return get<ILineItem[]>('/bsms/manage/workingSchedule/selectAreaLineList', {
    areaId,
  });
};

/**
 * 查询线路下对应上下班类型的未来的班次
 * @param params
 * @returns
 */
export const getScheduleOptions = (params: {
  /** 线路id */
  lineId: number;
  /** 上下班类型 */
  scheduleType: number;
  date?: string;
}) => {
  return post<IScheduleItem[]>('/bsms/manage/workingSchedule/selectLineFutureSchedule', params);
};

/**
 * 获取当前班次+线路下的可用车辆
 * @param params
 * @returns
 */
export const getAvailableCarOptions = (params: {
  /** 班次 */
  scheduleId: number;
  /** 线路 */
  lineId: number;
  date?: string;
}) => {
  return post<IVehicleItem[]>('/bsms/manage/workingSchedule/getAvailableVehicle', params);
};

/**
 * 所属区域
 * */
export const getBelongArea = (data: { areaStatus?: ECommonStatus }) => {
  return post<IAreaItem[]>('/bsms/manage/workingSchedule/selectAreaList', data);
};

/**
 * 新增加趟任务
 */
export const newAddictionTask = (params: IAddictionTaskParams) => {
  return post<boolean>('/bsms/manage/workingSchedule/task/newAdditionalTask', params);
};
