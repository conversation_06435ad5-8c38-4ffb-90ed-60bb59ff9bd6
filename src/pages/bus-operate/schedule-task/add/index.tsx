import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Page } from '@/_/components';
import { usePageTitle } from '@/_/hooks';
import { ECommonStatus } from '@/_/interfaces';
import FormItem from '@/pages/bus-employee/components/AppointmentForm/components/FormItem';
import AppointmentRadioValue from '@/pages/bus-employee/components/AppointmentRadioValue';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import { useMount, useRequest, useSetState, useUpdateEffect } from 'ahooks';
import { isNil } from 'lodash';
import moment from 'moment';
import { Button, Cell, Radio, Toast } from 'shineout-mobile';
import FormItemArea, { LOCAL_STORAGE_KEY_AREA } from './components/FormItemArea';
import FormItemCar from './components/FormItemCar';
import FormItemLine from './components/FormItemLine';
import FormItemScheduleClasses from './components/FormItemScheduleClasses';
import {
  AdditionTypeOptions,
  EScheduleTypeEnum,
  IAddictionTaskForm,
  IAddictionTaskParams,
  IAreaItem,
  ILineItem,
  IScheduleItem,
  IVehicleItem,
  ScheduleTypeEnumOptions,
} from './interfaces';
import {
  getAvailableCarOptions,
  getBelongArea,
  getLineByAreaId,
  getScheduleOptions,
  newAddictionTask,
} from './services';

const DATE_OPTIONS = [
  { label: t('今天'), value: moment().format('YYYY-MM-DD') },
  { label: t('明天'), value: moment().add(1, 'day').format('YYYY-MM-DD') },
];

const ADDITIONAL_REQUIRED_IF_SCHEDULE_TYPE = [
  EScheduleTypeEnum.DAY_GO_OFF_WORK,
  EScheduleTypeEnum.NIGHT_GO_OFF_WORK,
];

/**
 * @description validateForm
 * @param {unknown} formValue desc
 * @returns {unknown} desc
 */
const validateForm = (formValue: IAddictionTaskForm) => {
  return ['lineId', 'scheduleId', 'vehicleIds', 'date', 'areaId', 'scheduleType'].every((key) => {
    if (key === 'vehicleIds') {
      return !isNil(formValue[key]) && formValue[key]?.length > 0;
    }
    if (
      key === 'scheduleType' &&
      ADDITIONAL_REQUIRED_IF_SCHEDULE_TYPE.includes(formValue.scheduleType)
    ) {
      return !isNil(formValue[key]) && !isNil(formValue.additionalType);
    }
    return !isNil(formValue[key]);
  });
};

const NewAdditionalTask: React.FC = () => {
  usePageTitle(t('通勤加趟'));
  const [formValue, setFormValue] = useSetState<IAddictionTaskForm>(() => {
    return {
      areaId: Number(localStorage.getItem(LOCAL_STORAGE_KEY_AREA)) || undefined,
    };
  });
  const [areaList, setAreaList] = useState<IAreaItem[]>([]);
  const [lineList, setLineList] = useState<ILineItem[]>([]);
  const [scheduleList, setScheduleList] = useState<IScheduleItem[]>([]);
  const [carOptions, setCarOptions] = useState<IVehicleItem[]>([]);

  const disabledLine = isNil(formValue.areaId) || isNil(formValue.scheduleType);
  useUpdateEffect(() => {
    if (!disabledLine) {
      getLineByAreaId(formValue.areaId).then(
        (res) => {
          setLineList(res);
        },
        () => {
          setLineList([]);
        },
      );
    }
  }, [formValue.scheduleType, formValue.areaId]);

  const disabledSchedule =
    isNil(formValue.lineId) || isNil(formValue.scheduleType) || isNil(formValue.date);
  useUpdateEffect(() => {
    if (!disabledSchedule) {
      getScheduleOptions({
        lineId: formValue.lineId,
        scheduleType: formValue.scheduleType,
        date: formValue.date,
      }).then(
        (res) => {
          setScheduleList(res);
        },
        () => {
          setScheduleList([]);
        },
      );
    }
  }, [formValue.lineId, formValue.scheduleType, formValue.date]);

  const disabledVehicle = isNil(formValue.lineId) || isNil(formValue.scheduleId);
  useUpdateEffect(() => {
    if (!disabledVehicle) {
      getAvailableCarOptions({
        lineId: formValue.lineId,
        scheduleId: formValue.scheduleId,
        date: formValue.date,
      }).then((res) => {
        setCarOptions(res || []);
      });
    }
  }, [formValue.date, formValue.lineId, formValue.scheduleId]);

  useMount(() => {
    getBelongArea({
      areaStatus: ECommonStatus.ENABLED,
    }).then((res) => {
      setAreaList(res);
    });
  });

  const navigate = useNavigate();
  const { runAsync: doSubmit, loading: submitLoading } = useRequest(newAddictionTask, {
    manual: true,
  });
  const handleSubmit = () => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { areaId: _, scheduleType: _2, ...reqParams } = formValue;
    doSubmit(reqParams as IAddictionTaskParams).then(() => {
      Toast.success(t('操作成功'));
      navigate('/bus-operate/task-manage', {
        replace: true,
      });
    });
  };

  return (
    <Page
      contentClassName="p-0"
      footer={
        <Button
          className="w-full"
          round
          type="primary"
          disabled={!validateForm(formValue)}
          loading={submitLoading}
          onClick={() => handleSubmit()}
        >
          {t('确认加趟')}
        </Button>
      }
    >
      <Cell
        label={t('发车日期')}
        center
        leftIcon={
          <div style={{ marginRight: 5 }}>
            <Icon name="calendar" color="#197AFA" fontSize={16} />
          </div>
        }
        value={
          <Radio.Group
            inline
            value={formValue.date}
            onChange={(v) => {
              setFormValue({
                date: v,
                scheduleId: undefined,
                vehicleIds: undefined,
              });
            }}
          >
            {DATE_OPTIONS.map((item) => (
              <Radio key={item.value} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </Radio.Group>
        }
      />
      <div className="relative" style={{ padding: 12 }}>
        <FormItemArea
          value={formValue.areaId}
          onChange={(v) => {
            setFormValue({
              areaId: v,
              lineId: undefined,
              scheduleId: undefined,
              vehicleIds: undefined,
            });
          }}
          options={areaList}
        />
        <FormItem
          label={t('上下班类型')}
          value={
            <AppointmentRadioValue
              data={ScheduleTypeEnumOptions.map((item) => {
                return {
                  ...item,
                  disabled: false,
                  desc: '',
                };
              })}
              value={formValue?.scheduleType}
              onChange={(v) => {
                setFormValue({
                  scheduleType: v,
                  additionalType: undefined,
                  lineId: undefined,
                  scheduleId: undefined,
                  vehicleIds: undefined,
                });
              }}
            />
          }
          icon="pc-classes"
          isColumnMode
        />
        {ADDITIONAL_REQUIRED_IF_SCHEDULE_TYPE.includes(formValue.scheduleType) && (
          <FormItem
            label={t('加趟类型')}
            value={
              <Radio.Group
                inline
                value={formValue.additionalType}
                onChange={(v) => {
                  setFormValue({
                    additionalType: v,
                  });
                }}
              >
                {AdditionTypeOptions.map((item) => (
                  <Radio key={item.value} value={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </Radio.Group>
            }
            icon="pc-sync-shineout"
          />
        )}
        <FormItemLine
          value={formValue.lineId}
          onChange={(v) => {
            setFormValue({
              lineId: v,
              scheduleId: undefined,
              vehicleIds: undefined,
            });
          }}
          options={lineList}
          disabled={disabledLine}
        />
        <FormItemScheduleClasses
          disabled={disabledSchedule}
          value={formValue.scheduleId}
          options={scheduleList}
          date={formValue.date}
          onChange={(v) =>
            setFormValue({
              scheduleId: v,
              vehicleIds: undefined,
            })
          }
        />
        <FormItemCar
          value={formValue.vehicleIds}
          disabled={disabledVehicle}
          onChange={(v) => {
            setFormValue({
              vehicleIds: v,
            });
          }}
          options={carOptions}
        />
      </div>
    </Page>
  );
};

export default NewAdditionalTask;
