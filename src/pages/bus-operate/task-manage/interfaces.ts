import { ECommutingTypeEnum, ETaskStatusEnum } from '@/_/interfaces';

interface IRouteLineItem {
  /** 线路id */
  id?: number;
  /** 线路名称 */
  name?: string;
  status?: number;
}

type IChildren = IRouteLineItem[];

export interface IRouteLintList {
  /** 区域id */
  id?: number;
  /** 区域名称 */
  name?: string;
  children?: IChildren;
  status?: number;
}

export interface IGetTaskManageListParams {
  lineId?: number;
  areaId?: number;
  commutingType?: ECommutingTypeEnum;
  scheduleDate?: string;
  pageSize?: number;
  pageNumber?: number;
}

/** 排班类型 */
export enum ETaskType {
  /** 常规任务 */
  NORMAL = 0,
  /** 加趟任务 */
  ADDITIONAL = 1,
}

export interface ITaskManageListItem {
  /** 任务id */
  id?: number;
  /** 任务编码 */
  taskNum?: string;
  /** 班次时间 */
  scheduleTime?: string;
  /** 路线名称 */
  lineName?: string;
  /** 司机名称 */
  driverName?: string;
  /** 车牌号 */
  licensePlate?: string;
  /** 座位数量 */
  seatNumber?: string;
  /** 车队名称 */
  carTeamName?: string;
  /** 任务状态 */
  taskStatusStr?: string;
  /** 任务状态 */
  taskStatus?: ETaskStatusEnum;
  /** 排班类型 */
  taskType?: ETaskType;
  /** 排班类型名称：0-常规任务；1-加趟任务 */
  taskTypeStr?: string;
}
