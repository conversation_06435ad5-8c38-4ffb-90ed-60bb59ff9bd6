.container {
  position: relative;
  padding: 12px;
  margin-bottom: 12px;
  background-color: white;
  border-radius: 4px;

  .header {
    display: flex;
    align-items: center;
    gap: 10px;

    .title {
      overflow: hidden;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #141737;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
    }

    .toggle {
      width: 40px;
      min-width: 40px;
      text-align: right;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 10px 0 0;
  }
}

.item {
  display: flex;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #666c7c;
  flex-direction: row;
  align-items: center;
  gap: 10px;

  .label {
    display: flex;
    width: 90px;
    min-width: 90px;
  }

  .value {
    display: flex;
    color: #141737;
    flex-grow: 1;
    align-items: flex-start;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
