import React, { ReactNode, useState } from 'react';
import AutoGapLabel from '@/_/components/AutoGapLabel';
import { ETaskStatusEnum, TaskStatusEnumOptions } from '@/_/interfaces';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';
import { Button, Dialog, Tag, Toast } from 'shineout-mobile';
import { ETaskType, ITaskManageListItem } from '../../interfaces';
import { cancelTask } from '../../services';
import styles from './index.less';

interface IProps {
  item: ITaskManageListItem;
  afterSubmit: () => void;
}

/**
 * @description 用于展示标签和值的通用组件
 * @param {object} props 组件属性
 * @returns {React.ReactElement} 返回一个包含标签和值的布局组件
 */
const Item: React.FC<{
  label: string;
  value: string | ReactNode;
  style?: React.CSSProperties;
}> = ({ label, value, style: customStyles }): React.ReactElement => {
  return (
    <div className={classNames(styles.item, customStyles)}>
      <AutoGapLabel className={styles.label} label={label} />
      <div className={styles.value}>{value}</div>
    </div>
  );
};

/**
 * @description 任务卡片组件，用于展示任务详情，支持展开/收起和取消任务功能
 * @param {IProps} props 组件属性
 * @returns {React.ReactElement} 返回一个任务卡片组件
 */
const TaskCard: React.FC<IProps> = (props): React.ReactElement => {
  const { item, afterSubmit } = props;
  const [toggle, setToggle] = useState(false);

  /**
   * @description 处理任务取消操作，弹出确认对话框，确认后调用取消接口
   * @returns {void} 无返回值
   */
  const handleCancel = (): void => {
    Dialog.confirm({
      message: t('任务取消后无法恢复，请确认是否继续取消？'),
      onOk: () => {
        cancelTask({ id: item.id }).then(() => {
          Toast.success(t('操作成功'));
          afterSubmit();
        });
      },
    });
  };

  const taskStatusObj = TaskStatusEnumOptions.find((i) => i.value === item.taskStatus);

  const allowCancel = toggle && item.taskStatus === ETaskStatusEnum.WAITING;

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.title}>
          <span>{item.taskNum}</span>
          <span>（{item.taskType === ETaskType.ADDITIONAL ? t('加趟') : t('常规')}）</span>
        </div>
        <Tag className="whitespace-nowrap" color={taskStatusObj?.color}>
          {taskStatusObj?.name}
        </Tag>
      </div>
      <div className={styles.content}>
        <Item label={t('班次')} value={item.scheduleTime} />
        <Item label={t('线路名称')} value={item.lineName} />
        {toggle && (
          <>
            <Item label={t('司机')} value={item.driverName} />
            <Item label={t('车牌号')} value={`${item.licensePlate}（${item.seatNumber}）`} />
            <Item label={t('车队名称')} value={item.carTeamName} />
          </>
        )}
      </div>
      <div className={classNames(styles.footer, allowCancel ? 'justify-between' : '')}>
        {allowCancel && (
          <Button text type="primary" onClick={handleCancel}>
            {t('任务取消')}
          </Button>
        )}
        {toggle ? (
          <Button text type="primary" onClick={() => setToggle(!toggle)}>
            {t('收起')}
          </Button>
        ) : (
          <Button text type="primary" onClick={() => setToggle(!toggle)}>
            {t('展开')}
          </Button>
        )}
      </div>
    </div>
  );
};

export default TaskCard;
