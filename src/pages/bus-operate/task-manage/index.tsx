import React, { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Page } from '@/_/components';
import { usePageTitle } from '@/_/hooks';
import EmptyBus from '@/pages/bus-employee/components/_/EmptyBus';
import LoadingBus from '@/pages/bus-employee/components/_/LoadingBus';
import DropdownMenuHeader from '@/pages/bus-fleet/schedule-task/components/DropdownMenuHeader';
import { t } from '@shein-bbl/react';
import { useInfiniteScroll, useMount, useUpdateEffect } from 'ahooks';
import moment from 'moment';
import { Button } from 'shineout-mobile';
import store from 'store2';
import DateFilterHeader from './components/DateFilterHeader';
import TaskCard from './components/TaskCard';
import { IGetTaskManageListParams, IRouteLintList, ITaskManageListItem } from './interfaces';
import { getAreaLine, getTaskManageList } from './services';

const AREA_KEY = '__bus_task_manage_areaId';
const LINE_KEY = '__bus_task_manage_lineId';
const PAGE_SIZE = 10;
/**
 * @description TaskManagePage
 * @returns {unknown} desc
 */
const TaskManagePage: React.FC = () => {
  usePageTitle(t('任务管理'));

  const [searchParams, setSearchParams] = useState<IGetTaskManageListParams>({
    scheduleDate: moment().format('YYYY-MM-DD'),
    areaId: store.local.get(AREA_KEY) || undefined,
    lineId: store.local.get(LINE_KEY) || undefined,
  });
  const contentRef = useRef<HTMLDivElement>(null);

  const { data, loading, loadingMore, noMore, reload } = useInfiniteScroll<{
    list: ITaskManageListItem[];
    total: number;
  }>(
    (d) => {
      const page = d ? Math.ceil(d.list.length / PAGE_SIZE) + 1 : 1;
      return getTaskManageList({
        pageNumber: page,
        pageSize: PAGE_SIZE,
        ...searchParams,
      })
        .then((res) => {
          return {
            list: res?.rows || [],
            total: res?.total || 0,
          };
        })
        .catch(() => {
          return {
            list: [],
            total: 0,
          };
        });
    },
    {
      target: contentRef,
      isNoMore: (d) => d && d?.list?.length === d?.total,
    },
  );

  const [routeLineList, setRouteLineList] = useState<IRouteLintList[]>([]);

  useUpdateEffect(() => {
    reload();
  }, [searchParams]);

  useMount(() => {
    getAreaLine().then((res) => {
      setRouteLineList(res);
    });
  });

  const navigate = useNavigate();

  return (
    <Page
      contentClassName="p-0 flex flex-col"
      footer={
        <Button
          type="primary"
          className="w-full"
          onClick={() => navigate('/bus-operate/schedule-task/add')}
        >
          {t('通勤加趟')}
        </Button>
      }
    >
      <DateFilterHeader
        date={searchParams?.scheduleDate}
        handleChange={(v) => {
          setSearchParams({
            ...searchParams,
            scheduleDate: v,
          });
        }}
      />
      <DropdownMenuHeader
        areaLineList={routeLineList}
        lineValue={searchParams?.lineId}
        areaValue={searchParams?.areaId}
        commutingType={searchParams?.commutingType}
        handleChangeArea={(v) => {
          setSearchParams({
            ...searchParams,
            areaId: v,
            lineId: undefined,
          });
          store.local.set(AREA_KEY, v || '');
          store.local.set(LINE_KEY, '');
        }}
        handleChangeLine={(v) => {
          if (searchParams?.areaId === undefined) {
            const aid = routeLineList.find((item) => item.children.some((i) => i.id === v))?.id;
            setSearchParams({
              ...searchParams,
              areaId: aid,
              lineId: v,
            });

            store.local.set(AREA_KEY, aid || '');
            store.local.set(LINE_KEY, v || '');
          } else {
            setSearchParams({
              ...searchParams,
              lineId: v,
            });

            store.local.set(LINE_KEY, v || '');
          }
        }}
        handleChangeCommutingType={(v) => {
          setSearchParams({
            ...searchParams,
            commutingType: v,
          });
        }}
        isAdmin
      />

      <div className="flex-1 min-h-0 overflow-auto p-[12px]" ref={contentRef}>
        {!loading &&
          data.list?.map((item) => (
            <TaskCard
              key={item.id}
              item={item}
              afterSubmit={() => {
                reload();
              }}
            />
          ))}
        {loadingMore && (
          <div className="text-center text-[#666c7c] text-[12px] mt-md">{t('加载中...')}</div>
        )}
        {noMore && data?.list?.length >= PAGE_SIZE && (
          <div className="text-center text-[#666c7c] text-[12px] mt-md">{t('没有更多了')}</div>
        )}
        {!loading && data.list?.length === 0 && <EmptyBus height="100%" />}
        {loading && <LoadingBus />}
      </div>
    </Page>
  );
};
export default TaskManagePage;
