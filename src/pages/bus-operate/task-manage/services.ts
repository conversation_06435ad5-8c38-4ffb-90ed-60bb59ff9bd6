import { get, post } from '@/utils';
import { IGetTaskManageListParams, IRouteLintList, ITaskManageListItem } from './interfaces';

/**
 * 排班任务管理-获取区域线路下拉选项（获取该车队绑定区域和线路，包含禁用）
 * https://soapi.sheincorp.cn/application/3694/routes/184314/doc
 * @returns
 */
export const getAreaLine = () => {
  return get<IRouteLintList[]>('/bsms/manage/workingSchedule/selectAreaLine');
};

/**
 * 任务管理-任务分页列表
 * https://soapi.sheincorp.cn/application/3694/routes/241176/doc
 */
export const getTaskManageList = (params: IGetTaskManageListParams) => {
  return post<{
    total: number;
    rows: ITaskManageListItem[];
  }>('/bsms/manage/task/selectTaskPage', params);
};

/**
 * 任务管理-任务取消
 * https://soapi.sheincorp.cn/application/3694/routes/241178/doc
 */
export const cancelTask = (params: { id: number }) => {
  return post('/bsms/manage/task/cancelTask', params);
};
