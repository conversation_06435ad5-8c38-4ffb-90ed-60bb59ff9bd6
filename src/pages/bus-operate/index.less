.content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.menuItem {
  display: flex;
  padding: 12px 16px;
  background-color: #fff;
  border-radius: 8px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.menuLeft {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
}

.menuIcon {
  display: flex;
  width: 45px;
  height: 45px;
  border-radius: 16px;
  align-items: center;
  justify-content: center;

  :global {
    .so-icon {
      font-size: 24px;
      color: #fff;
    }
  }
}

.menuName {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #35383d;
}

.menuRight {
  :global {
    .so-icon {
      font-size: 16px;
      color: #666c7c;
    }
  }
}

.menuAdmin {
  background: linear-gradient(135.91deg, #c4c4ff 6.84%, #955eec 93.69%);
}

.menuApply {
  background: linear-gradient(135.91deg, #6ce2ff 6.84%, #197afa 93.69%);
}

.menuFill {
  background: linear-gradient(139.14deg, #bfffc4 5.36%, #31c28b 93.72%);
}

.menuResign {
  background: linear-gradient(135.91deg, #ffdc95 6.84%, #ff6d28 93.69%);
}

.menuTask {
  :global {
    .so-icon {
      font-size: 55px;
    }
  }
}
