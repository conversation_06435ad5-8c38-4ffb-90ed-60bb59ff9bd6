import { post } from '@/utils';
import { ITaskItem } from '../my-task/interfaces';

/**
 * 查询我的历史任务
 * https://soapi.sheincorp.cn/application/3694/routes/185802/doc
 * @param params
 * @returns
 */
export const selectMyHistoryTaskPage = (params: {
  pageSize: number;
  pageNumber: number;
  scheduleDate: string;
}) =>
  post<{
    total: number;
    rows: ITaskItem[];
  }>('/bsms/driver/task/selectMyHistoryTaskPage', params);
