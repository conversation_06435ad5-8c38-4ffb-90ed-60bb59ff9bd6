import { useMemo, useState } from 'react';
import { usePageTitle } from '@/_/hooks';
import useScrollBottom from '@/pages/bus-employee/hooks/useScrollBottom';
import { usePersistFn } from '@shein-lego/use';
import { useMount } from 'ahooks';
import moment from 'moment';
import { Calendar, Cell } from 'shineout-mobile';
import HomeCard from '../home/<USER>/HomeCard';
import { TaskStatusEnum } from '../home/<USER>';
import Empty from '../my-task/components/Empty';
import { ITaskItem } from '../my-task/interfaces';
import { selectMyHistoryTaskPage } from './services';

const HistoryTaskPage = () => {
  usePageTitle('历史任务');
  const pageSize = 10;
  const [loading, setLoading] = useState(false);
  const [pageNumber, setPageNumber] = useState(1);
  const [total, setTotal] = useState(0);
  const needLoadMore = useMemo(() => total > pageNumber * pageSize, [pageNumber, total]);
  const [data, setData] = useState<ITaskItem[]>([]);
  const [scheduleDate, setScheduleDate] = useState<string>(moment().format('YYYY-MM-DD'));
  const [scheduleDateVisible, setScheduleDateVisible] = useState(false);

  const handleLoadMore = usePersistFn(() => {
    if (loading) {
      return;
    }
    setLoading(true);
    selectMyHistoryTaskPage({
      scheduleDate,
      pageNumber: pageNumber + 1,
      pageSize,
    }).then((res) => {
      setData([...data, ...res.rows]);
      setPageNumber(pageNumber + 1);
      setTotal(res.total);
      setLoading(false);
    });
  });

  const { onScroll, containerRef } = useScrollBottom({
    onLoadMore: handleLoadMore,
    needLoadMore,
    loading,
  });

  const handleRefresh = usePersistFn(() => {
    setLoading(true);
    selectMyHistoryTaskPage({
      scheduleDate,
      pageNumber: 1,
      pageSize,
    })
      .then((res) => {
        setData(res.rows);
        setPageNumber(1);
        setTotal(res.total);
      })
      .finally(() => {
        setLoading(false);
      });
  });

  useMount(() => {
    setLoading(true);
    selectMyHistoryTaskPage({
      scheduleDate,
      pageNumber,
      pageSize,
    })
      .then((res) => {
        setData(res.rows);
        setTotal(res.total);
      })
      .finally(() => {
        setLoading(false);
      });
  });

  const handleChangeScheduleDate = usePersistFn((date: string) => {
    setScheduleDate(date);
    setPageNumber(1);
    setLoading(true);
    selectMyHistoryTaskPage({
      scheduleDate: date,
      pageNumber: 1,
      pageSize,
    })
      .then((res) => {
        setData(res.rows);
        setTotal(res.total);
      })
      .finally(() => {
        setLoading(false);
      });
  });

  return (
    <>
      <Cell
        label="日期"
        isLink
        value={scheduleDate}
        onClick={() => {
          setScheduleDateVisible(true);
        }}
      />
      <Calendar
        round
        visible={scheduleDateVisible}
        confirmButtonText="确认"
        maxDate={moment().toDate()}
        defaultValue={moment(scheduleDate).toDate()}
        defaultPosition={moment(scheduleDate).toDate()}
        drawer={{ position: 'bottom', height: 486, closeable: true }} // 控制Drawer
        onConfirm={(val, formatValue) => {
          console.log(val, formatValue);
          setScheduleDateVisible(false);
          handleChangeScheduleDate(formatValue);
        }}
        onCancel={() => setScheduleDateVisible(false)}
      />

      <div
        ref={containerRef}
        onScroll={onScroll}
        style={{
          maxHeight: 'calc(100vh - 44px)',
          overflow: 'auto',
          paddingBottom: 12,
        }}
      >
        {data?.map((item) => {
          return (
            <HomeCard
              key={item.id}
              showHeader={true}
              showFooter={false}
              disabled={item.taskStatus === TaskStatusEnum.CANCELED}
              showExtraInfo={true}
              tagMode="deep"
              boardBackground="gray"
              timeTextColor="black"
              cardBackground="white"
              item={item}
              afterSubmit={handleRefresh}
            />
          );
        })}
      </div>

      {data?.length === 0 && !loading && <Empty />}
    </>
  );
};
export default HistoryTaskPage;
