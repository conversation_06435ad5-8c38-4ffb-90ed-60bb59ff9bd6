import { ReactNode, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import AutoGapLabel from '@/_/components/AutoGapLabel';
import { usePageTitle } from '@/_/hooks';
import Icon from '@shein-components/Icon';
import { useMemoizedFn, useMount, useSetState } from 'ahooks';
import moment from 'moment';
import { Button, NoticeBar, Tag } from 'shineout-mobile';
import useIntervalUpdateTaskStatus from '../../_/hooks/useIntervalUpdateTaskStatus';
import useAction from '../../home/<USER>/useAction';
import { TaskStatusEnum, TaskStatusEnumOptions } from '../../home/<USER>';
import { ITaskItem } from '../interfaces';
import styles from './index.less';
import { getCheckAheadTime, getTaskDetail } from './services';

/**
 * @description Item
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const Item: React.FC<{ label: string; value: ReactNode | string }> = ({ label, value }) => {
  return (
    <>
      <div className={styles.item}>
        <AutoGapLabel className={styles.label} label={label} />
        <div className={styles.value}>{value}</div>
      </div>
    </>
  );
};

const TaskDetail = () => {
  usePageTitle('任务详情');
  const [data, setData] = useSetState<ITaskItem>(null);
  const { id } = useParams();
  const { start, clear } = useIntervalUpdateTaskStatus();

  const afterSubmit = useMemoizedFn(() => {
    getTaskDetail({ taskId: Number(id) }).then((res) => {
      setData(res);
      if (res !== null) {
        clear();
        start(res.id, res.taskStatus, (status) => {
          setData({
            ...data,
            taskStatus: status,
          });
        });
      }
    });
  });
  const { handleCheck, handleArrival, handleStart } = useAction({ afterSubmit });
  const [configMins, setConfigMins] = useState(40);

  const isBeforeBegin20Min = useMemo(() => {
    return moment(data?.estimatedStartTime).subtract(configMins, 'minutes').isBefore(moment());
  }, [configMins, data?.estimatedStartTime]);

  const isBeforeBegin20MinAndFirstMission =
    !isBeforeBegin20Min && data?.first && data?.taskStatus === TaskStatusEnum.WAITING;

  const init = useMemoizedFn(() => {
    getCheckAheadTime()
      .then((minutes) => {
        console.log('minutes', minutes);
        setConfigMins(minutes);
      })
      .finally(() => {
        getTaskDetail({ taskId: Number(id) }).then((res) => {
          setData(res);
          if (res !== null) {
            start(res.id, res.taskStatus, (status) => {
              setData({
                taskStatus: status,
              });
            });
          }
        });
      });
  });

  useMount(() => {
    init();
  });

  if (data === null) {
    return null;
  }

  return (
    <div>
      {isBeforeBegin20MinAndFirstMission && (
        <NoticeBar
          className={styles.noteBar}
          icon="dark-label"
          text="当前任务未开始"
          scrollable={false}
        />
      )}
      <div className={styles.boardCard}>
        <div className={styles.board}>
          <div className={styles.departure}>{data.departureSiteName}</div>
          <div className={styles.signal}>
            <div className={styles.time}>{data.scheduleTime}</div>
            <div className={styles.arrow}></div>
            <div className={styles.date}>{data.scheduleDate}</div>
          </div>
          <div className={styles.destination}>{data.destinationSiteName}</div>
        </div>
      </div>
      <div
        className={styles.detailCard}
        style={{
          height: isBeforeBegin20MinAndFirstMission ? 'calc(100vh - 184px)' : 'calc(100vh - 140px)',
        }}
      >
        <Item
          label="状态"
          value={
            <Tag
              textColor={'white'}
              color={TaskStatusEnumOptions.find((op) => op.value === data.taskStatus)?.color}
            >
              {TaskStatusEnumOptions.find((op) => op.value === data.taskStatus)?.name}
            </Tag>
          }
        />
        <Item label="任务编号" value={data.taskNum} />
        <Item label="发车时间" value={data.estimatedStartTime} />
        <Item label="预计抵达" value={data.estimatedArriveTime} />
        {![TaskStatusEnum.BREACH_OF_CONTRACT, TaskStatusEnum.CANCELED].includes(
          data.taskStatus,
        ) && <Item label="实际抵达" value={data.finishTime} />}
        <Item label="上车地点" value={data.pickUpPoint} />
        <Item label="上下班类型" value={data.commutingTypeName} />
        <Item label="车牌号" value={data.licensePlate} />
        <Item label="车辆座位" value={data.seatNumber} />
        {![TaskStatusEnum.BREACH_OF_CONTRACT, TaskStatusEnum.CANCELED].includes(
          data.taskStatus,
        ) && <Item label="上车人数" value={data.passengersNum} />}

        <div className={styles.btnGroup}>
          {/* 待出发 + 20min前 */}
          {data.taskStatus === TaskStatusEnum.WAITING && !isBeforeBegin20Min && data?.first && (
            <>
              <Button type="primary" className={styles.btn} disabled>
                已到达上车点（开始验票）
              </Button>
              <div className={styles.warning}>
                <Icon name="odec-info-fill" color="#F56C0A" fontSize={14} />
                当前任务还未开始
              </div>
            </>
          )}
          {/* 待出发 + 20min内 */}
          {data.taskStatus === TaskStatusEnum.WAITING && isBeforeBegin20Min && data?.first && (
            <>
              <Button type="primary" className={styles.btn} onClick={() => handleCheck(data.id)}>
                已到达上车点（开始验票）
              </Button>
            </>
          )}

          {/* 验票中 */}
          {data.taskStatus === TaskStatusEnum.CHECKING && (
            <Button type="primary" className={styles.btn} onClick={() => handleStart(data.id)}>
              开始出发
            </Button>
          )}
          {/* 进行中 */}
          {data.taskStatus === TaskStatusEnum.IN_PROGRESS && (
            <Button type="primary" className={styles.btn} onClick={() => handleArrival(data.id)}>
              到达目的地
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
export default TaskDetail;
