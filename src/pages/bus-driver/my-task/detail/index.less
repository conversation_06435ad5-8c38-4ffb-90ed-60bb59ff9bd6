.noteBar {
  color: #ffa940;
  background: #faf2e7;
}

.boardCard {
  display: flex;
  padding: 12px;
  background-color: white;
  flex-direction: row;
}

.board {
  display: flex;
  padding: 12px;
  background-color: #eef2fe;
  border-radius: 8px;
  flex-grow: 1;

  .departure,
  .destination {
    display: flex;
    font-size: 24px;
    font-weight: 900;
    color: #141737;
    justify-content: center;
    align-items: center;
    flex: 1 1 0;
    text-align: center;
  }

  .signal {
    display: flex;
    width: 100px;
    min-width: 100px;
    padding: 0 5px;
    text-align: center;
    flex-direction: column;
    gap: 8px;

    .time {
      font-size: 20px;
      font-weight: bold;
      color: #f75229;
    }

    .arrow {
      background-image: url('./long-arrow.png');
      background-repeat: no-repeat;
      background-size: contain;
      height: 10px;
    }

    .date {
      font-size: 14px;
      color: #141737;
    }
  }
}

.detailCard {
  position: relative;
  display: flex;
  padding: 12px;
  margin: 12px;
  background-color: white;
  border-radius: 8px;
  flex-direction: column;
  gap: 18px;
}

.item {
  display: flex;
  flex-direction: row;
  align-items: center;
  // padding: 12px 0;

  .label {
    width: 78px;
    min-width: 78px;
    color: #666c7c;
  }

  .value {
    color: #141737;
  }
}

.btnGroup {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;

  .btn {
    width: 100%;
  }

  .warning {
    display: flex;
    justify-content: center;
    margin-top: 6px;
  }
}
