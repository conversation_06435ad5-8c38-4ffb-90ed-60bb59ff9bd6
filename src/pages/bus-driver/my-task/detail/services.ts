import { get } from '@/utils';
import { ITaskItem } from '../interfaces';

/**
 * 任务详情
 * https://soapi.sheincorp.cn/application/3694/routes/185796/doc
 * @param params
 * @returns
 */
export const getTaskDetail = (params: { taskId: number }) => {
  return get<ITaskItem>('/bsms/driver/task/detail', params);
};

/**
 * 查询可以提前多少分钟检票
 * https://soapi.sheincorp.cn/application/3694/routes/203707/doc
 */
export const getCheckAheadTime = () => {
  return get<number>('/bsms/driver/task/selectCheckAheadOfTime', {});
};
