import { useMemo, useState } from 'react';
import { usePageTitle } from '@/_/hooks';
import useScrollBottom from '@/pages/bus-employee/hooks/useScrollBottom';
import { usePersistFn } from '@shein-lego/use';
import { useMount } from 'ahooks';
import useIntervalUpdateTaskStatus from '../_/hooks/useIntervalUpdateTaskStatus';
import HomeCard from '../home/<USER>/HomeCard';
import { TaskStatusEnum } from '../home/<USER>';
import Empty from './components/Empty';
import { getCheckAheadTime } from './detail/services';
import { ITaskItem } from './interfaces';
import { getMyTaskList } from './services';

/**
 * @description MyTaskPage
 * @returns {unknown} desc
 */
const MyTaskPage = () => {
  usePageTitle('我的任务');
  const [loading, setLoading] = useState(false);
  const pageSize = 10;
  const [pageNumber, setPageNumber] = useState(1);
  const [total, setTotal] = useState(0);
  const needLoadMore = useMemo(() => total > pageNumber * pageSize, [pageNumber, total]);
  const [configMins, setConfigMins] = useState(40);
  const { start, clear } = useIntervalUpdateTaskStatus();

  const handleLoadMore = usePersistFn(() => {
    if (loading) {
      return;
    }
    setLoading(true);
    getMyTaskList({
      pageNumber: pageNumber + 1,
      pageSize,
    })
      .then((res) => {
        setData([...data, ...res.rows]);
        setPageNumber(pageNumber + 1);
        setTotal(res.total);
      })
      .finally(() => {
        setLoading(false);
      });
  });

  const { onScroll, containerRef } = useScrollBottom({
    onLoadMore: handleLoadMore,
    needLoadMore,
    loading,
  });

  const handleStart = usePersistFn((res) => {
    start(res.rows[0].id, res.rows[0].taskStatus, (status) => {
      setData(
        data.map((item) => {
          if (item.id === res.rows[0].id) {
            return {
              ...item,
              taskStatus: status,
            };
          }
          return item;
        }),
      );
    });
  });

  const [data, setData] = useState<ITaskItem[]>([]);

  const handleRefresh = usePersistFn(() => {
    setLoading(true);
    clear();
    getMyTaskList({
      pageNumber: 1,
      pageSize,
    })
      .then((res) => {
        setData(res.rows);
        setPageNumber(1);
        setTotal(res.total);
        if ((res?.rows ?? []).length > 0) {
          handleStart(res);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  });

  useMount(() => {
    setLoading(true);
    getCheckAheadTime()
      .then((minutes) => {
        console.log('minutes', minutes);
        setConfigMins(minutes);
      })
      .finally(() => {
        getMyTaskList({
          pageNumber,
          pageSize,
        })
          .then((res) => {
            setData(res.rows);
            setTotal(res.total);
            if ((res?.rows ?? []).length > 0) {
              handleStart(res);
            }
          })
          .finally(() => {
            setLoading(false);
          });
      });
  });

  return (
    <>
      <div
        ref={containerRef}
        onScroll={onScroll}
        style={{ maxHeight: '100vh', overflow: 'auto', paddingBottom: 12 }}
      >
        {data?.map((item, index) => {
          return (
            <HomeCard
              key={item.id}
              showHeader={true}
              showFooter={index === 0 ? true : false}
              disabled={item.taskStatus === TaskStatusEnum.CANCELED}
              showExtraInfo={true}
              tagMode="deep"
              timeTextColor={item.taskStatus === TaskStatusEnum.WAITING ? 'orange' : 'black'}
              boardBackground="gray"
              cardBackground="white"
              item={item}
              afterSubmit={handleRefresh}
              configMins={configMins}
            />
          );
        })}
      </div>
      {data.length === 0 && !loading && <Empty />}
    </>
  );
};
export default MyTaskPage;
