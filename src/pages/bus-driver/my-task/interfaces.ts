import { TaskStatusEnum } from '../home/<USER>';

export interface ITaskItem {
  /** 任务id */
  id?: number;
  /** 任务编码 */
  taskNum?: string;
  /** 车辆id */
  vehicleId?: number;
  /** 车牌号 */
  licensePlate?: string;
  /** 座位数量 */
  seatNumber?: number;
  /** 0-待出发；1-验票中；2-进行中；3-已完成，4-缺勤 */
  taskStatus?: TaskStatusEnum;
  /** 如：2024:03:01 */
  scheduleDate?: string;
  /** 出发站点id */
  departureSiteId?: number;
  /** 出发站点名称 */
  departureSiteName?: string;
  /** 目的站点id */
  destinationSiteId?: number;
  /** 目的站点名称 */
  destinationSiteName?: string;
  /** 如：12:13 */
  scheduleTime?: string;
  /** 预计开始时间 */
  estimatedStartTime?: string;
  /** 预计出发时间 */
  estimatedDepartureTime?: string;
  /** 预计到达时间 */
  estimatedArriveTime?: string;
  /** 0-白班上班；1-白班下班；2-夜班上班；3-夜班下班 */
  commutingType?: string;
  /** 上下班类型名称 */
  commutingTypeName?: string;
  /** 实际抵达时间 */
  finishTime?: string;
  /** 乘客数量 */
  passengersNum?: number;

  /** 上车地点 */
  pickUpPoint?: string;
  /** 是否第一顺位任务 */
  first?: boolean;
}
