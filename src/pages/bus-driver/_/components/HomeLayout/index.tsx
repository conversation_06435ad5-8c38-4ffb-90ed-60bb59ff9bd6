import { useNavigate } from 'react-router-dom';
import Icon from '@shein-components/Icon';
import { Tabbar } from 'shineout-mobile';
import styles from './index.less';

type IHomeLayoutProps = {
  menuCurrent?: HomeLayoutTabEnum;
};

export enum HomeLayoutTabEnum {
  HOME_PAGE,
  PERSONAL_CENTRA,
}

const menuMap = [
  {
    type: HomeLayoutTabEnum.HOME_PAGE,
    name: '首页',
    icon: <Icon name="pc-home" />,
    activeIcon: <Icon name="pc-home-fill" />,
    url: '/bus-driver/home',
  },
  {
    type: HomeLayoutTabEnum.PERSONAL_CENTRA,
    name: '我的',
    icon: <Icon name="pc-people-circle" />,
    activeIcon: <Icon name="pc-people-circle-fill" />,
    url: '/bus-driver/personal',
  },
];
const { Item } = Tabbar;

/**
 * @description HomeLayout
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const HomeLayout: React.FC<IHomeLayoutProps> = (props) => {
  const { children, menuCurrent } = props;
  const navigate = useNavigate();

  return (
    <div className={styles.container}>
      <div className={styles.content}>{children}</div>
      <div className={styles.footer}>
        <Tabbar
          active={menuMap.find((item) => item.type === menuCurrent)?.url}
          onChange={(v) => {
            navigate(v);
          }}
        >
          {menuMap.map((item) => {
            return (
              <Item key={item.type} name={item.url} icon={item.icon} activeIcon={item.activeIcon}>
                {item.name}
              </Item>
            );
          })}
        </Tabbar>
      </div>
    </div>
  );
};

export default HomeLayout;
