.container {
  position: relative;
  width: 100%;
  height: 100vh;
  max-width: 750px;
  padding-bottom: 70px;

  .content {
    width: 100%;
    height: calc(100vh - 70px);
    overflow-x: hidden;
    overflow-y: auto;
  }

  .footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 70px;
    max-width: 750px;
    min-width: 375px;
    padding: 10px 0;
    background-color: white;
    border-top: 1px solid #f0f0f0;
  }
}
