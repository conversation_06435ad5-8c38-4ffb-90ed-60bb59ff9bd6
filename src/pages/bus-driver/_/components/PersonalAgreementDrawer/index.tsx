import React, { ForwardRefRenderFunction, useState } from 'react';
import { Drawer } from 'shineout-mobile';
import { IAgreementRef } from './interfaces';
import styles from './style.less';

/**
 * @description PersonalAgreementDrawer
 * @param {unknown} _ desc
 * @param {unknown} ref desc
 * @returns {unknown} desc
 */
const PersonalAgreementDrawer: ForwardRefRenderFunction<IAgreementRef> = (_, ref) => {
  const [visible, setVisible] = useState(false);
  /**
   * @description handleOpen
   * @returns {unknown} desc
   */
  const handleOpen = () => {
    setVisible(true);
  };

  React.useImperativeHandle(ref, () => ({
    open: handleOpen,
  }));

  return (
    <Drawer
      visible={visible}
      closeable
      maskCloseAble={false}
      onClose={() => setVisible(false)}
      className={styles.container}
      style={{
        overflow: 'auto',
      }}
    >
      <div className={styles.title}>{'个人信息保护说明'}</div>
      <div className={styles.content}>
        <h4>重要提示</h4>
        1、本服务页面用于向SHEIN园区班车合作方（“您”）提供班车业务协同服务（“服务”），由广州希音供应链管理有限公司（“我们”）提供技术支持。我们将按本说明收集和处理您的个人信息。请您在提供个人信息前仔细阅读。一旦您在页面上提交了相关信息，即视为您同意本说明的全部内容。
        <br />
        <br />
        若您不是SHEIN园区班车合作方，请不要使用本服务。
        <br />
        <br />
        2、如您对我们收集、处理您的个人信息有任何问题，您可通过以下方式联系我们。
        <br />
        邮箱: <EMAIL>
        <br />
        地址：广州市番禺区南村镇金坑广兴红棉路8号1栋301房、1栋401房、1栋501房
        <br />
        <br />
        <h4>尊敬的用户：</h4>
        1、本服务及本说明由广州希音供应链管理有限公司（“我们”）提供。
        <br />
        <br />
        2、我们将在以下场景及法律法规许可的范围内收集、处理您的个人信息：
        <br />
        <br />
        <table style={{ fontSize: 12 }}>
          <tr>
            <td>序号</td>
            <td>个人信息字段</td>
            <td>处理目的及处理方式</td>
          </tr>
          <tr>
            <td>1</td>
            <td>手机号、验证码</td>
            <td>用于注册账户，以使用服务</td>
          </tr>
          <tr>
            <td>2</td>
            <td>姓名、手机号</td>
            <td>用于司机信息管理</td>
          </tr>
          <tr>
            <td>3</td>
            <td>手机号</td>
            <td>发送相关通知、提醒等</td>
          </tr>
        </table>
        <br />
        请您确保所提交的信息真实、准确、完整、有效，并已获得相应个人信息主体的同意。
        您有权拒绝提供信息，但您将无法使用服务。
        <br />
        <br />
        3、您同意我们可在本说明范围内自行或委托、安排第三方对您的个人信息进行收集和/或处理，这些第三方包括但不限于我们的技术服务商、SHEIN园区管理方等。我们会要求这些第三方采取必要的安全措施保护您的个人信息。
        <br />
        <br />
        4、我们将采取有效的技术措施及内部管理措施保障您的个人信息安全，依照相关法律规定收集、处理您提交的信息。
        <br />
        <br />
        5、我们会在达成本说明所述目的所需的期限内保留您的个人信息，除非法律有强制的留存要求。在超出保留期间后，我们会根据适用法律的要求删除或匿名化处理您的个人信息，如删除或匿名化个人信息从技术上难以实现的，我们会应当停止除存储和采取必要的安全保护措施之外的处理。
        <br />
        <br />
        6、如您有任何投诉、建议或咨询，或有任何基于法律法规规定的权利请求，例如查询、修改、删除、撤回授权等，请通过以下方式联系我们：
        <br />
        邮箱: <EMAIL>
        <br />
        地址：广州市番禺区南村镇金坑广兴红棉路8号1栋301房、1栋401房、1栋501房
      </div>
    </Drawer>
  );
};

export default React.forwardRef(PersonalAgreementDrawer);
