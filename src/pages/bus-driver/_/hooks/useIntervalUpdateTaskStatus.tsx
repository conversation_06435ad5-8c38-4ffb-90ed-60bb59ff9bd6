import { useRef } from 'react';
import { useMemoizedFn, useUnmount } from 'ahooks';
import { TaskStatusEnum } from '../../home/<USER>';
import { getTaskStatus } from '../../home/<USER>';

const INTERVAL_TIME = 5000;
/**
 * @description useIntervalUpdateTaskStatus
 * @param {unknown} beforeStopInterval desc
 * @returns {unknown} desc
 */
const useIntervalUpdateTaskStatus: (beforeStopInterval?: () => void) => {
  start: (
    id: number,
    taskStatus: TaskStatusEnum,
    onChange: (status: TaskStatusEnum) => void,
  ) => void;
  clear: () => void;
} = (beforeStopInterval) => {
  console.log('useIntervalUpdateTaskStatus', beforeStopInterval);
  const updateStatusInterval = useRef(null);
  const handleClear = useMemoizedFn(() => {
    if (updateStatusInterval.current) {
      clearTimeout(updateStatusInterval.current);
    }
  });

  /**
   * @description updateTaskStatus
   * @param {unknown} id desc
   * @param {unknown} taskStatus desc
   * @param {unknown} onChange desc
   * @returns {unknown} desc
   */
  const updateTaskStatus = (
    id: number,
    taskStatus: TaskStatusEnum,
    onChange: (status: TaskStatusEnum) => void,
  ) => {
    if (
      [TaskStatusEnum.WAITING, TaskStatusEnum.IN_PROGRESS, TaskStatusEnum.CHECKING].includes(
        taskStatus,
      )
    ) {
      updateStatusInterval.current = setTimeout(() => {
        getTaskStatus({ taskId: id })
          .then((data) => {
            onChange(data?.taskStatus);
            console.log('updateTaskStatus.then', id, data?.taskStatus);
            updateTaskStatus(id, data?.taskStatus, onChange);
          })
          .catch((e) => {
            console.log('updateTaskStatus.catch', id, e);
            handleClear();
          });
      }, INTERVAL_TIME);
    } else {
      beforeStopInterval?.();
      handleClear();
    }
  };

  useUnmount(() => {
    handleClear();
  });

  return {
    start: updateTaskStatus,
    clear: handleClear,
  };
};

export default useIntervalUpdateTaskStatus;
