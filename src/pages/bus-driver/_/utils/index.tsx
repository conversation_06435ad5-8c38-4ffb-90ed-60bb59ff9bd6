/**
 * 将对象的键名从驼峰式转换为下划线式
 * @param {Record<string, any>} obj - 需要转换键名的对象
 * @returns {Record<string, any>} 转换后的新对象
 */
export function camelToUnderlineForObject(obj: Record<string, any>): Record<string, any> {
  const newObj = new Object();
  for (const key in obj) {
    newObj[key.replace(/([A-Z])/g, '_$1').toLowerCase()] = obj[key];
  }
  return newObj;
}

const BSMS_TOKEN_KEY = 'BSMS_DRIVER_FRONT_TOKEN';

// 过期时间30天
const ExpirationTime = 30 * 24 * 60 * 60 * 1000;

/**
 * 设置BSMS驾驶员前端token的cookie
 * @param {string} token - 需要设置的token值
 * @returns {void}
 */
export function setCookieForBsmsDriverToken(token: string): void {
  const expires = new Date();
  expires.setTime(expires.getTime() + ExpirationTime);
  document.cookie = `${BSMS_TOKEN_KEY}=${token || ''};expires=${expires.toUTCString()};path=/`;
}

/**
 * 获取BSMS驾驶员前端token的cookie值
 * @returns {string} 获取到的token值，如果不存在则返回空字符串
 */
export function getCookieForBsmsDriverToken(): string {
  const name = `${BSMS_TOKEN_KEY}=`;
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    const c = ca[i].trim();
    if (c.indexOf(name) === 0) {
      return c.substring(name.length, c.length);
    }
  }
  return '';
}

/**
 * 检查是否存在有效的BSMS驾驶员前端token
 * @returns {boolean} 如果存在有效token则返回true，否则返回false
 */
export function checkIsBsmsDriverTokenLogin(): boolean {
  return document.cookie.indexOf(BSMS_TOKEN_KEY) !== -1 && getCookieForBsmsDriverToken() !== '';
}

/**
 * 移除BSMS驾驶员前端token的cookie
 * @returns {void}
 */
export function removeCookieForBsmsDriverToken(): void {
  document.cookie = `${BSMS_TOKEN_KEY}=;expires=${new Date(0).toUTCString()};path=/`;
}
