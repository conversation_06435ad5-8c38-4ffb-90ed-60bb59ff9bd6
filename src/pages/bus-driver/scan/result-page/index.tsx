import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import Icon from '@shein-components/Icon';
import { useMount } from 'ahooks';
import { Button } from 'shineout-mobile';
import { IDriverScanResult } from '../interfaces';

/**
 * @description ScanResultPage
 * @returns {unknown} desc
 */
const ScanResultPage = () => {
  usePageTitle('验票结果');
  const location = useLocation();
  const navigate = useNavigate();
  const urlQuery = new URLSearchParams(location.search);
  const result = urlQuery.get('result');
  const [error, setError] = useState(false);
  const [resultObj, setResultObj] = useState<IDriverScanResult>();

  console.log('result', result);
  console.log('resultObj', resultObj);

  useMount(() => {
    try {
      const res = JSON.parse(decodeURIComponent(result)) as IDriverScanResult;
      setResultObj(res);
      setError(false);
    } catch (_error) {
      setError(true);
    }
  });

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          flexGrow: 1,
          padding: 50,
        }}
      >
        {!error ? (
          <div
            style={{ display: 'flex', justifyContent: 'center', flexDirection: 'column', gap: 20 }}
          >
            {resultObj?.scanResult ? (
              <div
                style={{
                  textAlign: 'center',
                }}
              >
                <Icon name="pc-all-multic" fontSize={64} />
                <div
                  style={{
                    color: 'rgb(34, 185, 115)',
                    fontWeight: 500,
                    fontSize: 22,
                    lineHeight: '40px',
                  }}
                >
                  验票成功
                </div>
              </div>
            ) : (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  flexDirection: 'column',
                  gap: 20,
                }}
              >
                <div
                  style={{
                    textAlign: 'center',
                  }}
                >
                  <Icon name="pc-error-multic" fontSize={58} />
                  <div
                    style={{
                      color: 'rgb(255, 77, 80)',
                      fontWeight: 500,
                      fontSize: 22,
                      lineHeight: '40px',
                    }}
                  >
                    验票失败
                  </div>
                </div>
              </div>
            )}
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '10px',
                fontSize: 16,
              }}
            >
              <div>
                <span>员工姓名：</span> <span>{resultObj?.staffName ?? '-'}</span>
              </div>
              <div>
                <span>员工工号：</span> <span>{resultObj?.workNum ?? '-'}</span>
              </div>
              {!resultObj?.scanResult && (
                <div
                  style={{
                    color: 'rgb(255, 77, 80)',
                  }}
                >
                  <span>失败原因：</span> <span>{resultObj?.failReason}</span>
                </div>
              )}
              {resultObj?.scanResult && (
                <div
                  style={{
                    color: 'rgb(25, 122, 250)',
                  }}
                >
                  <span>当前车辆剩余座位：</span> <span>{resultObj?.spareSeat}</span>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div
            style={{
              textAlign: 'center',
            }}
          >
            <Icon name="pc-error-multic" fontSize={58} />
            <div
              style={{
                color: 'rgb(255, 77, 80)',
                fontWeight: 500,
                fontSize: 22,
                lineHeight: '40px',
              }}
            >
              参数错误
            </div>
          </div>
        )}
      </div>
      <div
        style={{
          height: 200,
          minHeight: 200,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 30,
        }}
      >
        <Button type="primary" plain onClick={() => navigate('/bus-driver/home')}>
          返回首页
        </Button>
        <Button type="primary" onClick={() => navigate('/bus-driver/scan')}>
          继续检票
        </Button>
      </div>
    </div>
  );
};

export default ScanResultPage;
