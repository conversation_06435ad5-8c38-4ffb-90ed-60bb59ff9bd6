import { useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import LoadingBus from '@/pages/bus-employee/components/_/LoadingBus';
import { useMount, usePersistFn } from '@shein-lego/use';
import { Button } from 'shineout-mobile';
import useWeixinScan, { IAfterScanFn, StatusResultEnum } from './hooks/useWeixinScan';
import { scanRideCodeByDriver } from './services';

/**
 * @description DriverScanPage
 * @returns {unknown} desc
 */
const DriverScanPage = () => {
  const navigate = useNavigate();
  const firstEnter = useRef(true);

  const handleAfterScan: IAfterScanFn = usePersistFn((status, errMsg, result) => {
    console.log(status, errMsg, result);
    switch (status) {
      case StatusResultEnum.SUCCESS:
        scanRideCodeByDriver({
          qrCode: result,
        }).then((res) => {
          navigate(
            '/bus-driver/scan/result-page?result=' + encodeURIComponent(JSON.stringify(res)),
          );
        });
        break;
      case StatusResultEnum.ERR_INVOKE_WECHAT:
        break;
      case StatusResultEnum.ERR_PROCESS:
        break;
      case StatusResultEnum.ERR_WEIXIN_CHECK:
        break;
      default:
        firstEnter.current = false;
        break;
    }
  });
  const { startScan, loading } = useWeixinScan(handleAfterScan);

  useMount(() => {
    startScan();
  });

  return (
    <>
      {!loading && firstEnter.current === false && (
        <div
          style={{
            height: '100vh',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Button type="primary" onClick={() => startScan()}>
            重新扫码
          </Button>
        </div>
      )}
      {loading && <LoadingBus />}
    </>
  );
};

export default DriverScanPage;
