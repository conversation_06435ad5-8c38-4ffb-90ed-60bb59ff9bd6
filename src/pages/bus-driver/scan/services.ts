import { get, post } from '@/utils';
import { IDriverScanResult, ISdkSignatureProperty } from './interfaces';

/**
 * 获取微信签名
 * https://soapi.sheincorp.cn/application/3694/routes/194050/doc
 * @param param
 * @returns
 */
export const getWeixinSdkSignature = (params: { url: string }) => {
  return get<ISdkSignatureProperty>('/weixin/bsms/driver/createJsapiSignature', params);
};

/**
 * 司机扫码检票
 * https://soapi.sheincorp.cn/application/3694/routes/193678/doc
 * @param params
 * @returns
 */
export const scanRideCodeByDriver = (params: { qrCode: string }) => {
  return post<IDriverScanResult>('/bsms/driver/scanRideCode', params);
};
