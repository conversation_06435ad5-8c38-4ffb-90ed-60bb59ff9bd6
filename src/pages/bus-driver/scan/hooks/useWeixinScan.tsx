import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { isWeiXin } from '@/pages/access-control/utils';
import { usePersistFn } from '@shein-lego/use';
import { Toast } from 'shineout-mobile';
import { getWeixinSdkSignature } from '../services';

export enum StatusResultEnum {
  SUCCESS = 1,
  /** SDK调用失败 */
  ERR_INVOKE_WECHAT = -1,
  /** 逻辑处理问题 */
  ERR_PROCESS = 0,
  // /** SDK方法调用失败｜ check功能失败 */
  ERR_WEIXIN_CHECK = -2,
}
export type IAfterScanFn = (status: StatusResultEnum, errMsg: string, result?: string) => void;

const useWeixinScan = (afterScan: IAfterScanFn) => {
  const isSupportWeixinSdk = isWeiXin();
  const [loading, setLoading] = useState(true);
  const [scanning, setScanning] = useState(true);
  const navigate = useNavigate();

  const startScan = usePersistFn(() => {
    console.log('startScan');
    setLoading(true);
    if (!isSupportWeixinSdk) {
      Toast.fail('请使用微信打开');
      setLoading(false);
      navigate('/bus-driver/home');
      return;
    }
    getWeixinSdkSignature({ url: location.href.split('#')[0] })
      .then((config) => {
        console.log('res.config', config);
        setScanning(true);
        wx.config({
          debug: false,
          ...config,
          jsApiList: ['scanQRCode'],
        });
        wx.ready(() => {
          /** 需要检测的JS接口列表，这一步非必要 */
          wx.checkJsApi({
            jsApiList: ['scanQRCode'],
            success: function (res: Record<string, any>): void {
              console.log('wx.checkJsApi.scanQRCode', res);
              handleDoScan();
            },
          });
        });
        wx.error((res) => {
          console.log('wx.error', res);
          afterScan(StatusResultEnum.ERR_WEIXIN_CHECK, res);
        });
      })
      .finally(() => {
        setLoading(false);
      });
  });

  const handleDoScan = usePersistFn(() => {
    try {
      let scanSuccess = false;
      wx.scanQRCode({
        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
        scanType: ['qrCode'], // 可以指定扫二维码还是一维码，默认二者都有
        success: function (res: { resultStr: string }): void {
          console.log('scanQRCode.res', res);
          try {
            scanSuccess = true;
            const result = res.resultStr;
            console.log('result', result);
            console.log('扫码成功，返回结果');
            // 扫码成功，返回结果
            afterScan(StatusResultEnum.SUCCESS, '', result);
          } catch (err) {
            Toast.fail(err);
            console.log('扫码返回格式错误');
            // 扫码失败，返回失败原因，扫码返回格式错误
            afterScan(StatusResultEnum.ERR_PROCESS, err);
          }
          setScanning(false);
        },
        complete(): void {
          if (!scanSuccess) {
            navigate('/bus-driver/home', {
              replace: true,
            });
          }
        },
      });
    } catch (error) {
      Toast.fail('微信调用失败');
      console.log('微信调用失败:' + error);
      //   // 扫码失败，返回失败原因，微信调用失败
      afterScan(StatusResultEnum.ERR_INVOKE_WECHAT, '微信调用失败:' + error);
    }
  });

  return {
    loading,
    scanning,
    startScan,
  };
};

export default useWeixinScan;
