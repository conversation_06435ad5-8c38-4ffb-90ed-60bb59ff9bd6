import { useRef } from 'react';
import { usePageTitle } from '@/_/hooks';
import Icon from '@shein-components/Icon';
import QRCode from '@shein-components/qr-code';
import { useMount, useSetState, useUnmount } from 'ahooks';
import { IDriverCodeInfo } from './interfaces';
import { getDriverCodeInfo } from './services';

/**
 * @description VehicleBind
 * @returns {unknown} desc
 */
const VehicleBind: React.FC = () => {
  usePageTitle('车辆绑定');

  const [data, setData] = useSetState<IDriverCodeInfo>(null);
  const intervalRef = useRef<NodeJS.Timeout>();

  /**
   * @description startInterval
   * @returns {unknown} desc
   */
  const startInterval = () => {
    intervalRef.current = setInterval(() => {
      getDriverCodeInfo().then((res) => {
        setData({
          deviceCode: res.deviceCode,
          alreadyBind: res.alreadyBind,
        });
      });
    }, 1000 * 5);
  };

  useMount(() => {
    getDriverCodeInfo().then((res) => {
      setData(res);
      if (!res.alreadyBind) {
        startInterval();
      }
    });
  });

  useUnmount(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  });

  return (
    <div style={{ padding: 16 }}>
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: 4,
          padding: 12,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        {data?.alreadyBind ? (
          <div>
            <div
              style={{
                margin: '50px 0',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Icon name="pc-search empty-multic" fontSize={98} />
            </div>
            <div>
              <div
                style={{
                  fontWeight: 500,
                  textAlign: 'center',
                }}
              >
                当前车辆已绑定设备
              </div>
              <div
                style={{
                  textAlign: 'center',
                  margin: '12px 0',
                }}
              >
                绑定设备：{data?.deviceCode.slice(-8)}
              </div>
            </div>
          </div>
        ) : (
          <div>
            <p style={{ color: '#EB4242', textAlign: 'center', marginBottom: 12, fontWeight: 500 }}>
              当前车辆未绑定设备
            </p>
            <QRCode size={220} value={data?.qrCode} />
          </div>
        )}
      </div>

      <div
        style={{
          color: '#F56C0A',
          backgroundColor: '#FFF3E2',
          borderRadius: 4,
          padding: 12,
          display: 'flex',
          flexDirection: 'column',
          marginTop: 12,
          lineHeight: '1.5',
        }}
      >
        <p style={{ fontSize: 16, fontWeight: 500, marginBottom: 6 }}>绑定说明</p>
        <p>1、初次登陆请确认当前车辆已绑定设备，若未绑定将无法执行任务</p>
        <p>2、若要换绑设备，则需先在后台将车辆与设备解除绑定，再进行新的绑定操作</p>
        <p>3、绑定方式：车载设备处扫【绑定二维码】即可</p>
      </div>
    </div>
  );
};

export default VehicleBind;
