import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import { COOKIE_KEY_OBJ, removeCookie } from '@/utils';
import { useMount } from 'ahooks';
import { Cell, Toast } from 'shineout-mobile';
import HomeLayout, { HomeLayoutTabEnum } from '../_/components/HomeLayout';
import { removeCookieForBsmsDriverToken } from '../_/utils';
import { SendPhoneMessageTypeEnum } from '../login/interfaces';
import { logout } from '../login/services';
import { IDriverInfo } from './interfaces';
import { getDriverPersonalInfo } from './services';

/**
 * @description checkLicensePlateEmpty
 * @param {unknown} value desc
 * @returns {unknown} desc
 */
const checkLicensePlateEmpty = (value: string | undefined | null) => {
  const EMPTY = '--';
  if (!value) {
    return EMPTY;
  }
  if (value === '') return EMPTY;
  return value;
};

/**
 * @description PersonalPage
 * @returns {unknown} desc
 */
const PersonalPage = () => {
  usePageTitle('我的信息');
  const navigate = useNavigate();
  const [details, setDetails] = useState<IDriverInfo>(null);
  useMount(() => {
    getDriverPersonalInfo().then((res) => {
      setDetails(res);
    });
  });

  return (
    <>
      <HomeLayout menuCurrent={HomeLayoutTabEnum.PERSONAL_CENTRA}>
        <Cell label="车队名" value={details?.carTeamName} />
        <Cell label="管理员" value={details?.managerName} />
        <Cell label="账号" value={details?.driverPhone} />
        <Cell label="绑定车辆" value={checkLicensePlateEmpty(details?.licensePlate)} />
        <div
          onClick={() => {
            logout({
              type: SendPhoneMessageTypeEnum.DRIVER,
            }).then(() => {
              removeCookieForBsmsDriverToken();
              removeCookie(COOKIE_KEY_OBJ.siamToken);
              navigate('/bus-driver/login');
              Toast.success('退出登录');
            });
          }}
          style={{
            marginTop: 20,
            padding: 12,
            backgroundColor: '#fff',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          退出登录
        </div>
      </HomeLayout>
    </>
  );
};

export default PersonalPage;
