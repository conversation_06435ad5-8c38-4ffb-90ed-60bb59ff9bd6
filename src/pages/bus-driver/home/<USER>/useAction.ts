import { useState } from 'react';
import { Dialog, Toast } from 'shineout-mobile';
import { beginCheck, setArrive, setOut } from '../services';

const useAction = (props: {
  afterSubmit?: () => void;
}): {
  handleCheck: (id: number) => void;
  handleStart: (id: number) => void;
  handleArrival: (id: number) => void;
} => {
  const { afterSubmit } = props;

  // 跳过开始验票地理位置校验
  const [skipCheckLocation, setSkipCheckLocation] = useState(false);
  // 跳过到达地理位置校验
  const [skipArrivalLocation, setSkipArrivalLocation] = useState(false);

  /** 开始验票 */
  const handleCheck = (id: number) => {
    beginCheck({
      id,
      skipLocationCheck: skipCheckLocation,
    })
      .then(() => {
        Toast.success('操作成功');
        // 刷新页面
        afterSubmit?.();
      })
      .catch((e) => {
        if (e?.code === '090040001') {
          Dialog.confirm({
            title: '开始验票',
            message: '系统判断您当前位置不在班次起点站位置，是否仍要开始验票',
            onOk: () => {
              setSkipCheckLocation(true);
              beginCheck({
                id,
                skipLocationCheck: true,
              })
                .then(() => {
                  Toast.success('操作成功');
                  // 刷新页面
                  afterSubmit?.();
                })
                .catch((e) => {
                  Toast.fail(e?.msg);
                });
            },
            onCancel: () => {
              setSkipCheckLocation(false);
            },
          });
        } else {
          Toast.fail(e?.msg);
        }
      });
  };

  /** 开始出发 */
  const handleStart = (id: number) => {
    setOut({ id }).then(() => {
      Toast.success('操作成功');
      // 刷新页面
      afterSubmit?.();
    });
  };

  /** 到达目的地 */
  const handleArrival = (id: number) => {
    setArrive({
      id,
      skipLocationCheck: skipArrivalLocation,
    })
      .then(() => {
        Toast.success('操作成功');
        // 刷新页面
        afterSubmit?.();
      })
      .catch((e) => {
        if (e?.code === '090040002') {
          Dialog.confirm({
            title: '到达目的地',
            message: '系统判断您当前位置不在班次终点站位置，是否仍要到达目的地',
            onOk: () => {
              setSkipArrivalLocation(true);
              setArrive({
                id,
                skipLocationCheck: true,
              })
                .then(() => {
                  Toast.success('操作成功');
                  // 刷新页面
                  afterSubmit?.();
                })
                .catch((e) => {
                  Toast.fail(e?.msg);
                });
            },
            onCancel: () => {
              setSkipArrivalLocation(false);
            },
          });
        } else {
          Toast.fail(e?.msg);
        }
      });
  };

  return {
    handleCheck,
    handleStart,
    handleArrival,
  };
};

export default useAction;
