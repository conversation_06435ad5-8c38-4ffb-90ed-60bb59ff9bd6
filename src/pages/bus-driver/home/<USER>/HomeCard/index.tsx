import { useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import AutoGapLabel from '@/_/components/AutoGapLabel';
import { ITaskItem } from '@/pages/bus-driver/my-task/interfaces';
import Icon from '@shein-components/Icon';
import classNames from 'classnames';
import moment from 'moment';
import { Button, Tag } from 'shineout-mobile';
import useAction from '../../hooks/useAction';
import { TaskStatusEnum, TaskStatusEnumOptions } from '../../interfaces';
import styles from './index.less';

interface IHomeCardProps {
  className?: string;
  item: ITaskItem | null;
  boardBackground?: 'white' | 'blue' | 'gray' | 'red';
  cardBackground?: 'gradient' | 'white';
  tagMode?: 'deep' | 'light' | 'hide';
  timeTextColor?: 'orange' | 'black';
  showHeader?: boolean;
  showFooter?: boolean;
  showExtraInfo?: boolean;
  disabled?: boolean;
  onClick?: (item: ITaskItem) => void;
  afterSubmit?: () => void;
  isHome?: boolean;
  configMins?: number;
}

/**
 * @description BoardCard
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const BoardCard: React.FC<
  Pick<IHomeCardProps, 'item' | 'boardBackground' | 'disabled' | 'timeTextColor'>
> = (props) => {
  const { disabled = false, item, boardBackground = 'white', timeTextColor = 'orange' } = props;
  const boardBackgroundColorMap = {
    white: '#ffffff',
    blue: '#EEF2FE',
    gray: '#F4F5F8',
    red: '#FDE8E8',
  };
  return (
    <div
      className={styles.board}
      style={{
        backgroundColor: disabled
          ? '#F4F5F8'
          : boardBackgroundColorMap[boardBackground] ?? '#ffffff',
        color: '#999DA8',
      }}
    >
      <div
        className={styles.departure}
        style={
          disabled
            ? {
                color: '#999DA8',
              }
            : {}
        }
      >
        {item.departureSiteName}
      </div>
      <div className={styles.signal}>
        <div
          className={styles.time}
          style={{
            color: disabled ? '#999DA8' : timeTextColor === 'orange' ? '#F56C0A' : 'black',
          }}
        >
          {item.scheduleTime}
        </div>
        <div className={styles.arrow}></div>
        <div
          className={styles.date}
          style={
            disabled
              ? {
                  color: '#999DA8',
                }
              : {}
          }
        >
          {item.scheduleDate}
        </div>
      </div>
      <div
        className={styles.destination}
        style={
          disabled
            ? {
                color: '#999DA8',
              }
            : {}
        }
      >
        {item.destinationSiteName}
      </div>
    </div>
  );
};

const HomeCard: React.FC<IHomeCardProps> = (props) => {
  const {
    item,
    className: classNameParent,
    cardBackground = 'gradient',
    showHeader = true,
    showFooter = true,
    showExtraInfo = true,
    tagMode = 'deep',
    afterSubmit,
    isHome = false,
    configMins,
  } = props;
  const navigate = useNavigate();
  const divRef = useRef<HTMLDivElement>(null);
  const { handleCheck, handleStart, handleArrival } = useAction({ afterSubmit });

  const isEmpty = useMemo(() => {
    return item === null || item === undefined ? true : false;
  }, [item]);

  console.log(
    "moment().add(configMins, 'minutes').isBefore(moment(item.estimatedStartTime))",
    moment().add(configMins, 'minutes').isBefore(moment(item?.estimatedStartTime)),
  );

  console.log(
    "moment().add(configMins, 'minutes').isAfter(moment(item.estimatedStartTime))",
    moment().add(configMins, 'minutes').isAfter(moment(item?.estimatedStartTime)),
  );
  return (
    <div
      ref={divRef}
      className={classNames(styles.container, classNameParent)}
      style={{
        background: cardBackground === 'white' && 'white',
      }}
      onClick={() => {
        if (item === null || item === undefined) return;
        navigate('/bus-driver/my-task/detail/' + item?.id);
      }}
    >
      {showHeader && !isEmpty && (
        <div className={styles.header}>
          <div>
            {isHome && item.taskStatus === TaskStatusEnum.WAITING && <span>即将开始的任务: </span>}
            {item.taskNum}
          </div>
          <div>
            {tagMode !== 'hide' && (
              <Tag
                color={
                  tagMode === 'deep'
                    ? TaskStatusEnumOptions.find((op) => op.value === item.taskStatus)?.color
                    : TaskStatusEnumOptions.find((op) => op.value === item.taskStatus)?.lightColor
                }
                textColor={
                  tagMode === 'deep'
                    ? 'white'
                    : TaskStatusEnumOptions.find((op) => op.value === item.taskStatus)?.color
                }
              >
                {TaskStatusEnumOptions.find((op) => op.value === item.taskStatus)?.name}
              </Tag>
            )}
            <Icon name="arr-right" color="gray" className="ml-[10px]" />
          </div>
        </div>
      )}
      {!isEmpty ? (
        <BoardCard {...props} />
      ) : (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'column',
            gap: '12px',
            padding: '50px 0',
          }}
        >
          <Icon name="pc-search empty-blue-multic" fontSize={78} />
          <p style={{ color: '#999DA8' }}>暂无排班任务，休息一会~</p>
        </div>
      )}
      {showExtraInfo && !isEmpty && (
        <div className={styles.extraInfo}>
          <div className={styles.row}>
            <AutoGapLabel className={styles.label} label="上车地点" />
            <div className={styles.value}>{item.pickUpPoint}</div>
          </div>
          <div className={styles.row}>
            <AutoGapLabel className={styles.label} label="车牌号" />
            <div className={styles.value}>{item.licensePlate}</div>
          </div>
        </div>
      )}
      {showFooter && !isEmpty && (
        <div className={styles.footer}>
          {/* 待出发 + xxx min前 */}
          {item.taskStatus === TaskStatusEnum.WAITING &&
            moment().add(configMins, 'minutes').isBefore(moment(item?.estimatedStartTime)) && (
              <>
                <Button type="primary" className={styles.btn} disabled>
                  已到达上车点（开始验票）
                </Button>
                <div className={styles.warning}>
                  <Icon name="odec-info-fill" color="#F56C0A" fontSize={14} />
                  当前任务还未开始
                </div>
              </>
            )}
          {/* 待出发 + xxx min内 */}
          {item.taskStatus === TaskStatusEnum.WAITING &&
            moment().add(configMins, 'minutes').isAfter(moment(item?.estimatedStartTime)) && (
              <>
                <Button
                  type="primary"
                  className={styles.btn}
                  onClick={(e) => {
                    handleCheck(item.id);
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                >
                  已到达上车点（开始验票）
                </Button>
              </>
            )}
          {/* 验票中 */}
          {item.taskStatus === TaskStatusEnum.CHECKING && (
            <Button
              type="primary"
              className={styles.btn}
              onClick={(e) => {
                handleStart(item.id);
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              开始出发
            </Button>
          )}
          {/* 进行中 */}
          {item.taskStatus === TaskStatusEnum.IN_PROGRESS && (
            <Button
              type="primary"
              className={styles.btn}
              onClick={(e) => {
                handleArrival(item.id);
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              到达目的地
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default HomeCard;
