:root {
  --driver-task-card-bg-rotate: 164deg;
}

.container {
  position: relative;
  z-index: 1;
  padding: 12px;
  margin: 12px 12px 0;
  background: linear-gradient(
    var(--driver-task-card-bg-rotate),
    rgb(91 214 225 / 50%) -11.07%,
    rgb(130 195 247 / 30%) 26.35%,
    rgb(95 215 225 / 0%) 68.39%
  );
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px 0 #020b181a;

  .header {
    display: flex;
    padding-left: 4px;
    margin-bottom: 12px;
    justify-content: space-between;
    align-items: center;
    gap: 8px;

    .orderNum {
      font-size: 14px;
      color: #141737;
    }
  }

  .board {
    display: flex;
    padding: 12px;
    background-color: white;
    border-radius: 8px;

    .departure,
    .destination {
      display: flex;
      font-size: 22px;
      font-weight: 900;
      color: #141737;
      justify-content: center;
      align-items: center;
      flex: 1 1 0;
      text-align: center;
    }

    .signal {
      display: flex;
      width: 100px;
      min-width: 100px;
      padding: 0 5px;
      text-align: center;
      flex-direction: column;

      .time {
        font-size: 20px;
        font-weight: bold;
        color: #f75229;
      }

      .arrow {
        background-image: url('./long-arrow.png');
        background-repeat: no-repeat;
        background-size: contain;
        height: 10px;
      }

      .date {
        font-size: 14px;
        color: #141737;
      }
    }
  }

  .extraInfo {
    display: flex;
    margin-top: 12px;
    margin-bottom: 8px;
    gap: 8px;
    flex-direction: column;

    .row {
      display: flex;

      .label {
        width: 68px;
        min-width: 68px;
        padding-left: 4px;
        color: #666c7c;

        /* stylelint-disable-next-line max-nesting-depth */
        // &::after {
        //   content: ':';
        // }
      }
    }
  }

  .footer {
    display: flex;
    flex-direction: column;
    align-items: center;

    .btn {
      width: 100%;
    }

    .warning {
      margin-top: 6px;
      font-weight: 400;
      color: #f56c0a;
    }
  }
}
