import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import Icon from '@shein-components/Icon';
import { useMount, usePersistFn } from '@shein-lego/use';
import { useMemoizedFn } from 'ahooks';
import { isNil } from 'lodash';
import { Tag } from 'shineout-mobile';
import HomeLayout, { HomeLayoutTabEnum } from '../_/components/HomeLayout';
import useIntervalUpdateTaskStatus from '../_/hooks/useIntervalUpdateTaskStatus';
import { getCheckAheadTime } from '../my-task/detail/services';
import HomeCard from './components/HomeCard';
import styles from './index.less';
import { IHomeInfo } from './interfaces';
import { getHomeInfo } from './services';

/**
 * @description BusDriverHome
 * @returns {unknown} desc
 */
const BusDriverHome = () => {
  usePageTitle('首页');
  const navigate = useNavigate();
  const [data, setData] = useState<IHomeInfo>(null);
  const [configMins, setConfigMins] = useState(40);

  const init = useMemoizedFn(() => {
    getCheckAheadTime()
      .then((minutes) => {
        console.log('minutes', minutes);
        setConfigMins(minutes);
      })
      .finally(() => {
        getHomeInfo().then((res) => {
          setData(res);
          if (res?.firstTask !== null) {
            start(res?.firstTask?.id, res?.firstTask?.taskStatus, handleChangeStatus);
          }
        });
      });
  });

  const beforeStartInterval = useMemoizedFn(() => {
    init();
  });
  const { start, clear } = useIntervalUpdateTaskStatus(beforeStartInterval);

  const ActionMap = [
    {
      img: 'order management',
      name: '我的任务',
      icon: 'triangle-right',
      url: '/bus-driver/my-task',
      tip: data?.pendingCount,
    },
    {
      img: 'm-time-fill',
      name: '历史任务',
      icon: 'triangle-right',
      url: '/bus-driver/history-task',
    },
    {
      img: 'm-time-fill',
      name: '人工检票',
      icon: 'triangle-right',
      url: '/bus-driver/scan',
    },
    {
      img: 'm-bus-fill',
      name: '绑定车辆',
      icon: 'triangle-right',
      url: '/bus-driver/vehicle-bind',
      tip: data?.bindDevice ? null : '未绑定',
    },
  ];

  const handleChangeStatus = useMemoizedFn((status) => {
    setData({
      ...data,
      firstTask: {
        ...data?.firstTask,
        taskStatus: status,
      },
    });
  });

  useMount(() => {
    init();
  });

  const freshHome = usePersistFn(() => {
    clear();
    getHomeInfo().then((res) => {
      setData(res);
      if (res?.firstTask !== null) {
        start(res?.firstTask?.id, res?.firstTask?.taskStatus, handleChangeStatus);
      }
    });
  });

  if (isNil(data)) {
    return null;
  }

  return (
    <HomeLayout menuCurrent={HomeLayoutTabEnum.HOME_PAGE}>
      <div className={styles.container}>
        <HomeCard
          className={styles.homeCard}
          configMins={configMins}
          isHome={true}
          timeTextColor="orange"
          boardBackground="white"
          item={data?.firstTask ?? null}
          afterSubmit={freshHome}
        />

        {ActionMap.map((item) => (
          <div
            key={item.name}
            className={styles.actionBtn}
            onClick={() => {
              navigate(item.url);
            }}
          >
            <div className={styles.icon}>
              <Icon
                name={item.img}
                color="white"
                style={{
                  opacity: 0.8,
                }}
                fontSize={18}
              />
            </div>
            <span className={styles.title}>{item.name}</span>
            {item?.tip !== 0 && item?.tip !== undefined && item?.tip !== null && (
              <Tag className={styles.tip} shape="round" type="danger">
                {item.tip}
              </Tag>
            )}
            <Icon name={item.icon} className={styles.right} fontSize={12} />
          </div>
        ))}
      </div>
    </HomeLayout>
  );
};

export default BusDriverHome;
