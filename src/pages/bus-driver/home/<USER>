import { get, post } from '@/utils';
import { IHomeInfo, TaskStatusEnum } from './interfaces';

/**
 * 司机首页信息
 * https://soapi.sheincorp.cn/application/3694/routes/191586/doc
 * @returns
 */
export const getHomeInfo = () => {
  return get<IHomeInfo>('/bsms/driver/home');
};

/**
 * 开始检票
 * https://soapi.sheincorp.cn/application/3694/routes/186227/doc
 * @param params
 * @returns
 */
export const beginCheck = (params: { id: number; skipLocationCheck: boolean }) => {
  return post('/bsms/driver/task/beginCheck', params, {
    showErrorMsg: false,
  });
};

/**
 * 查询当前任务状态
 * https://soapi.sheincorp.cn/application/3694/routes/196701/doc
 * @param params
 * @returns
 */
export const getTaskStatus = (params: { taskId: number }) => {
  return get<{
    taskStatus: TaskStatusEnum;
  }>('/bsms/driver/task/taskStatus', params);
};

/**
 * 开始进行
 * https://soapi.sheincorp.cn/application/3694/routes/191519/doc
 * @returns
 */
export const setOut = (params: { id: number }) => {
  return post('/bsms/driver/task/setOut', params);
};

/**
 * 到达目的地
 * https://soapi.sheincorp.cn/application/3694/routes/191520/doc
 * @param params
 * @returns
 */
export const setArrive = (params: { id: number; skipLocationCheck: boolean }) => {
  return post('/bsms/driver/task/arrive', params, {
    showErrorMsg: false,
  });
};
