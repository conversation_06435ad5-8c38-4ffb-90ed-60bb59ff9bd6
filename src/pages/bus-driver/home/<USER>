.container {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: white;

  .homeCard {
    margin-bottom: 30px;
  }

  .actionBtn {
    position: relative;
    left: 6%;
    z-index: 1;
    display: flex;
    width: 88%;
    padding: 10px;
    margin-bottom: 16px;
    cursor: pointer;
    background-color: white;
    border: 1px solid #f5f6fb;
    border-radius: 1rem;
    box-shadow: rgb(20 23 55/10%) 1rem 1rem 2rem 0;
    flex-direction: row;
    align-items: center;

    .icon {
      position: relative;
      left: 1rem;
      display: block;
      display: flex;
      width: 3rem;
      height: 3rem;
      font-size: 2.4rem;

      /* stylelint-disable-next-line prettier/prettier */
      background: linear-gradient(213.99deg, #5bd6e1 11.62%, #82c3f7 54.07%, #5fd7e1 92.36%);
      border-radius: 4px;
      /* stylelint-disable-next-line prettier/prettier */
      opacity: .8;
      align-items: center;
      justify-content: center;
    }

    .tip {
      // background-color: #eb4242;
      // border-radius: 5px;
      margin-right: 5px;
      // padding: 3px 5px;
      font-size: 12px;
      color: white;
    }

    .title {
      position: relative;
      top: 0;
      left: 2rem;
      display: block;
      font-size: 1.4rem;
      color: #333e59;
      flex-grow: 1;
    }

    .right {
      position: relative;
      display: block;
      font-size: 3rem;
      /* stylelint-disable-next-line prettier/prettier */
      color: #999da8;
    }
  }
}
