import { ITaskItem } from '../my-task/interfaces';

export enum TaskStatusEnum {
  /* 待出发 */
  WAITING = 0,
  /* 验票中 */
  CHECKING = 1,
  /* 进行中 */
  IN_PROGRESS = 2,
  /* 已完成 */
  COMPLETED = 3,
  /* 缺勤 */
  BREACH_OF_CONTRACT = 4,
  /* 已取消 */
  CANCELED = 5,
}

export const TaskStatusEnumMap = {
  [TaskStatusEnum.WAITING]: '待出发',
  [TaskStatusEnum.CHECKING]: '验票中',
  [TaskStatusEnum.IN_PROGRESS]: '进行中',
  [TaskStatusEnum.COMPLETED]: '已完成',
  [TaskStatusEnum.BREACH_OF_CONTRACT]: '缺勤',
  [TaskStatusEnum.CANCELED]: '已取消',
};

export const TaskStatusEnumOptions = [
  {
    value: TaskStatusEnum.WAITING,
    name: '待出发',
    color: '#F56C0A',
    lightColor: '#FFF3E2',
  },
  {
    value: TaskStatusEnum.CHECKING,
    name: '验票中',
    color: '#00A85F',
  },
  {
    value: TaskStatusEnum.IN_PROGRESS,
    name: '进行中',
    color: '#197AFA',
    lightColor: '#E9F5FE',
  },
  {
    value: TaskStatusEnum.COMPLETED,
    name: '已完成',
    color: '#197AFA',
    lightColor: '#E9F5FE',
  },
  {
    value: TaskStatusEnum.BREACH_OF_CONTRACT,
    name: '缺勤',
    color: '#EB4242',
    lightColor: '#F96156',
  },
  {
    value: TaskStatusEnum.CANCELED,
    name: '已取消',
    color: '#999DA8',
    lightColor: '#F96156',
  },
];

export interface IHomeInfo {
  /** 最早的任务 */
  firstTask?: ITaskItem;
  /** true/false */
  bindDevice?: boolean;
  /** 我的任务（未完成数量） */
  pendingCount?: number;
}
