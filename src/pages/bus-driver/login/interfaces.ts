export interface ICreateAuthTokenParams {
  /** 极客验证流水号 */
  challenge: string;
  /** 0-车队，1-司机 */
  type: SendPhoneMessageTypeEnum;
  /** 短信验证码 */
  figureCode: string;
  /** 手机号码 */
  phone: string;
}

export interface IGetRegisterResponse {
  /** 验证唯一 */
  cid?: string;
  /** 验证流水号 */
  challenge?: string;
  /** 验证类型 */
  type?: string;
  /** 静态资源列表 */
  staticServers?: string[];
  /** fullpage JS 路径 */
  fullpage?: string;
  /** click Js 路径 */
  click?: string;
  /** slide Js 路径 */
  slide?: string;
  /** 验证服务器地址 */
  apiServer?: string;
  /** 是否可用 */
  enabled?: boolean;
}

export interface ISendPhoneMessageParams {
  /** 验证流水号 */
  challenge: string;
  /** 用户手机号 */
  phone: string;
  /** 0-车队，1-司机 */
  type: SendPhoneMessageTypeEnum;
}

/** 0-车队，1-司机 */
export enum SendPhoneMessageTypeEnum {
  CAR_CREW = 0,
  DRIVER = 1,
}

interface IButtonListItem {
  /** 按键名称 */
  name?: string;
  /** 按键编码 */
  code?: string;
}

type IButtonList = IButtonListItem[];

export interface IMenuAuthListItem {
  /** 按键权限列表 */
  buttonList?: IButtonList;
  /** 菜单名称 */
  name?: string;
  /** 菜单路径 */
  url?: string;
}

export interface IPageAuthListItem {
  /** 名称 */
  name?: string;
  /** 图标 */
  icon?: string;
  /** 路径 */
  path?: string;
  /** 子节点 */
  children?: IPageAuthListItem[];
}
