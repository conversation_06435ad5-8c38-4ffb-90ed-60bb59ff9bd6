import {
  // get,
  post,
} from '@/utils';
import {
  ICreateAuthTokenParams,
  IGetRegisterResponse,
  // IMenuAuthListItem,
  // IPageAuthListItem,
  ISendPhoneMessageParams,
  SendPhoneMessageTypeEnum,
} from './interfaces';

/**
 * 司机或车队管理员登录
 * https://soapi.sheincorp.cn/application/3694/routes/184210/doc
 * @param params
 * @returns
 */
export const createAuthToken = (params: ICreateAuthTokenParams) => {
  return post<{
    bsms_car_team_token?: string;
    bsms_driver_token?: string;
    siamToken?: string;
  }>('/bsms/carTeam/auth/createAuthToken', params);
};

/**
 * 司机或车队端退出登录
 * https://soapi.sheincorp.cn/application/3694/routes/185573/doc
 * @returns
 */
export const logout = (params: { type: SendPhoneMessageTypeEnum }) => {
  return post<string>('/bsms/carTeam/auth/logout', params);
};

/**
 * 极验初始化验证请求
 * https://soapi.sheincorp.cn/application/3694/routes/184211/doc
 * @returns
 */
export const gtRegister = () => {
  return post<IGetRegisterResponse>('/bsms/carTeam/auth/gtRegister');
};

/**
 * 发送登录验证码短信通知
 * https://soapi.sheincorp.cn/application/3694/routes/184219/doc
 * @param params
 * @returns
 */
export const sendPhoneMessage = (params: ISendPhoneMessageParams) => {
  return post<boolean>('/bsms/carTeam/auth/sendMessage', params, {
    showErrorMsg: false,
  });
};

// /**
//  * 获取用户菜单按键权限列表
//  * https://soapi.sheincorp.cn/application/3694/routes/184221/doc
//  * @returns
//  */
// export const getUserPageButtonAuthList = () => {
//   return get<IMenuAuthListItem[]>('/bsms/carTeam/auth/getUserPageButtonAuthList');
// };

// /**
//  * 获取用户菜单权限列表
//  * https://soapi.sheincorp.cn/application/3694/routes/184222/doc
//  * @returns
//  */
// export const getUserPageAuthList = () => {
//   return get<IPageAuthListItem[]>('/bsms/carTeam/auth/getUserPageAuthList');
// };
