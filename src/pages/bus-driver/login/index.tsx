import { MutableRefObject, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import { COOKIE_KEY_OBJ, setCookie } from '@/utils';
import Icon from '@shein-components/Icon';
import '@/_/assets/js/gt';
import { usePersistFn } from '@shein-lego/use';
import { Button, Checkbox, Dialog, Field, Toast } from 'shineout-mobile';
import PersonalAgreementDrawer from '../_/components/PersonalAgreementDrawer';
import { IAgreementRef } from '../_/components/PersonalAgreementDrawer/interfaces';
import { camelToUnderlineForObject, setCookieForBsmsDriverToken } from '../_/utils';
import useTimeCountDown from './hooks/useTimeCountDown';
import HeaderBannerImg from './img/header-banner.png';
import styles from './index.less';
import { SendPhoneMessageTypeEnum } from './interfaces';
import { createAuthToken, gtRegister, sendPhoneMessage } from './services';

const BusManagementLoginPage: React.FC = () => {
  usePageTitle('SHEIN班车司机任务平台');

  const navigator = useNavigate();
  const { time, isCounting, startCountDown } = useTimeCountDown(60);

  const gtRef = useRef<any>(null);
  const validatorInputRef = useRef<any>(null);

  const [phone, setPhone] = useState<string>('');
  const [code, setCode] = useState<string>('');
  const [challenge, setChallenge] = useState<string>('');
  const [codeVerityLoading, setCodeVerityLoading] = useState<boolean>(false);
  const [isCheckReadProtocol, setIsCheckReadProtocol] = useState(false);

  const isCanSendCode = phone.length === 11 && !codeVerityLoading;
  const isCanNextStep = phone.length === 11 && code.length === 6 && challenge !== '';

  const personalAgreementRef: MutableRefObject<IAgreementRef | null> = useRef(null);

  // 图形验证回调
  const gtHandle = (gt) => {
    gt.onReady(() => {
      gtRef.current = gt;
      gtRef.current.verify();
    })
      .onSuccess(() => {
        const result = gtRef.current?.getValidate();
        if (!result) {
          Toast.fail('请完成验证');
        }
        setChallenge(result?.geetest_challenge);
        handleSendPhoneMessageCode(result?.geetest_challenge);
      })
      .onError(() => {
        console.log('onError');
        setCodeVerityLoading(false);
      })
      .onClose(() => {
        console.log('onClose');
        setCodeVerityLoading(false);
      });
  };

  // 拉起图形验证
  const handleClickSendPhoneCode = () => {
    setCodeVerityLoading(true);
    gtRegister()
      .then((config) => {
        if (config?.enabled === false) {
          setChallenge(config?.challenge);
          handleSendPhoneMessageCode(config?.challenge);
        } else if (config.staticServers === null) {
          return Toast.fail('图形验证初始化失败');
        } else {
          window.initGeetest(
            {
              ...camelToUnderlineForObject(config),
              product: 'bind',
            },
            gtHandle,
          );
        }
      })
      .finally(() => {
        setCodeVerityLoading(false);
      });
  };

  // 发送手机验证码
  const handleSendPhoneMessageCode = usePersistFn((challenge: string) => {
    sendPhoneMessage({
      challenge,
      phone,
      type: SendPhoneMessageTypeEnum.DRIVER,
    })
      .then(() => {
        setCodeVerityLoading(false);
        validatorInputRef.current?.focus();
        Toast.success('验证码已发送');
        startCountDown();
      })
      .catch((err) => {
        if (err.code === '400199') {
          Toast.fail(err.msg);
          gtRef.current.reset();
          gtRegister().then((config) => {
            window.initGeetest(
              {
                ...camelToUnderlineForObject(config),
                product: 'bind',
              },
              gtHandle,
            );
          });
        } else {
          Toast.fail(err.msg);
        }
      })
      .finally(() => {
        setCodeVerityLoading(false);
      });
  });

  // 提交
  const handleSubmit = usePersistFn((phone, code) => {
    if (isCheckReadProtocol) {
      createAuthToken({
        challenge,
        figureCode: code,
        phone,
        type: SendPhoneMessageTypeEnum.DRIVER,
      }).then((res) => {
        Toast.success('登录成功');
        setCookieForBsmsDriverToken(res.bsms_driver_token);
        setCookie(COOKIE_KEY_OBJ.siamToken, (res.siamToken || '').slice(0, 6), 30);
        navigator('/bus-driver/home');
      });
    } else {
      Dialog.confirm({
        title: '提示',
        message: '请阅读并同意SHEIN《个人信息保护说明》',
        cancelButtonText: '拒绝',
        confirmButtonText: '同意',
        onOk: () => {
          setIsCheckReadProtocol(true);
          setTimeout(() => handleSubmit(phone, code), 0);
        },
      });
    }
  });

  return (
    <div className={styles.container}>
      <img className={styles.headerImg} src={HeaderBannerImg} alt={'头部底图'} loading="lazy" />
      <div className={styles.title}>{'SHEIN班车司机任务平台'}</div>
      <div className={styles.formContainer}>
        <div className={styles.formBox}>
          <Field
            label={'手机号'}
            placeholder={'请输入手机号码'}
            value={phone}
            extra={<></>}
            type="number"
            maxLength={11}
            onChange={(e) => {
              setPhone(e.target.value);
            }}
            leftIcon={
              <span className={styles.icon}>
                <Icon color="#ccc" name="m-phone" fontSize={17} />
              </span>
            }
          />
          <Field
            forwardRef={(ref) => {
              validatorInputRef.current = ref;
            }}
            value={code}
            type="number"
            digits={0}
            maxLength={6}
            onChange={(e) => {
              setCode(e.target.value);
            }}
            leftIcon={
              <span className={styles.icon}>
                <Icon name="pinkong" color="#ccc" fontSize={18} />
              </span>
            }
            extra={
              isCounting ? (
                <Button className={styles.timeButton} type="primary" size="small" plain>
                  {time}
                </Button>
              ) : (
                <Button
                  className={styles.codeButton}
                  onClick={handleClickSendPhoneCode}
                  type="primary"
                  size="small"
                  loading={codeVerityLoading}
                  disabled={!isCanSendCode}
                >
                  {'发送验证码'}
                </Button>
              )
            }
            label={'验证码'}
            placeholder={'请输入验证码'}
          />
        </div>
        <div className={styles.checkBox}>
          <Checkbox
            shape="square"
            label={
              <div style={{ fontSize: 12 }}>
                {'我已阅读并同意'}
                <Button
                  text
                  type="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    personalAgreementRef.current?.open();
                  }}
                >
                  {'《个人信息保护说明》'}
                </Button>
              </div>
            }
            value={isCheckReadProtocol}
            onChange={(val) => setIsCheckReadProtocol(val)}
          />
        </div>
        <div className={styles.submitBox}>
          <Button
            className={styles.submitButton}
            type="primary"
            size="large"
            disabled={!isCanNextStep}
            onClick={() => handleSubmit(phone, code)}
          >
            {'登录'}
          </Button>
        </div>
      </div>
      <PersonalAgreementDrawer ref={personalAgreementRef} />
    </div>
  );
};

export default BusManagementLoginPage;
