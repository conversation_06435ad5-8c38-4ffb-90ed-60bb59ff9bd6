import { useState } from 'react';

/**
 * @description useTimeCountDown
 * @param {unknown} duration desc
 * @returns {unknown} desc
 */
const useTimeCountDown = (duration = 60) => {
  const [time, setTime] = useState(duration);
  const [isCounting, setIsCounting] = useState(false);

  /**
   * @description startCountDown
   * @returns {unknown} desc
   */
  const startCountDown = () => {
    setIsCounting(true);
    setTime(duration);
    const interval = setInterval(() => {
      setTime((prev) => {
        if (prev === 0) {
          clearInterval(interval);
          setIsCounting(false);
          return duration;
        }
        return prev - 1;
      });
    }, 1000);
  };

  return {
    time,
    isCounting,
    startCountDown,
  };
};

export default useTimeCountDown;
