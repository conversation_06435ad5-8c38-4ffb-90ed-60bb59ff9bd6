import React, { ForwardRefRenderFunction, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { t } from '@shein-bbl/react';
import { But<PERSON>, Drawer } from 'shineout-mobile';
import { setStaffAgreeInCookie } from '../../utils';
import styles from './style.less';

export type IAgreementRef = {
  open: () => void;
  close: () => void;
};
type IAgreementProps = {
  agree?: () => void;
  url?: string;
  onClose?: () => void;
};

/**
 * @description AgreeAgreementDrawer
 * @param {unknown} props desc
 * @param {unknown} ref desc
 * @returns {unknown} desc
 */
const AgreeAgreementDrawer: ForwardRefRenderFunction<IAgreementRef, IAgreementProps> = (
  props,
  ref,
) => {
  const [visible, setVisible] = useState(false);
  const navigator = useNavigate();
  const { agree, onClose } = props;

  /**
   * @description handleOpen
   * @returns {unknown} desc
   */
  const handleOpen = () => {
    setVisible(true);
  };

  /**
   * @description handleClose
   * @returns {unknown} desc
   */
  const handleClose = () => {
    setVisible(false);
    onClose?.();
  };

  /**
   * @description handleAgree
   * @returns {unknown} desc
   */
  const handleAgree = () => {
    setStaffAgreeInCookie();
    if (agree !== undefined) {
      agree();
    } else {
      navigator('/access-control/personal/face-recording-staff');
    }
  };

  React.useImperativeHandle(ref, () => ({
    open: handleOpen,
    close: () => {
      setVisible(false);
    },
  }));

  return (
    <Drawer
      visible={visible}
      maskCloseAble={false}
      closable
      onClose={() => {
        handleClose();
      }}
      className={styles.container}
    >
      <div className={styles.title}>{t('人脸识别同意声明')}</div>
      <div className={styles.content}>
        {t('欢迎你使用人脸录入（识别）功能（或称“本功能”、“本服务”）！')}
        <br />
        {t(
          '本声明是用户（”您”）与本司及关联方（“我们”）为实施门禁管理而签订的处理您个人信息的有效协议。',
        )}
        <b>{t('在接受本协议之前，请仔细阅读本协议的全部内容。')}</b>
        <p>{t('一、功能说明')}</p>
        {t(
          '为完善来访人员出入办公区域、园区（“办公场所”）的管理，保障办公场所安全，验证出入办公场所的人员是否为本人，',
        )}
        <b>
          {t(
            '我们将收集您提交的含有人脸信息的照片，并将其存储于我们的服务器中，用于您来访时将该照片与您进行现场对比并判断您是否为访客本人，以便于您可以顺利进入我们的办公场所。',
          )}
        </b>
        <p>{t('二、授权与许可')}</p>
        <b>
          {t(
            '如您点击“我已理解并同意”或继续操作或以其他方式选择接受本声明规则，则视为您在使用本服务时，同意并授权我们使用你所提供的个人信息及人脸信息，以核验您的身份。',
          )}
        </b>
        <br />
        <b>{t('如您不同意或无法准确理解本声明的任何内容请不要进行后续操作。')}</b>
        <p>{t('三、信息安全声明')}</p>
        {t(
          '我们承诺对您的个人信息严格保密，并基于国家监管部门认可的加密算法进行数据加密传输，数据加密存储。如因业务或服务原因我们需要与第三方机构进行合作，我们将与合作签订保密合同，并要求合作伙伴做好用户信息安全保障，承诺尽到信息安全保护义务。',
        )}
        <b>
          {t(
            '但您知悉并理解，任何安全措施都无法做到无懈可击，如因不可抗力、计算机黑客袭击、系统故障、通讯故障、电脑病毒、恶意程序攻击及其他不可归因于信息控制人的情况而导致用户损失的，我们不承担任何责任。',
          )}
        </b>
        {t(
          '如你对我们收集、提供、存储、使用你的个人信息有任何疑问，或决定行使法律法规规定的权利，您可通过《隐私政策》的第九章所载的方式联系我们处理。',
        )}
        <div className={styles.inscribe}>{t('佛山众睿仓储服务有限责任公司')}</div>
      </div>
      <div className={styles.footer}>
        <Button type="primary" plain size="small" onClick={() => handleClose()}>
          {t('不同意')}
        </Button>
        <Button
          size="small"
          type="primary"
          onClick={() => handleAgree()}
          className={styles.agreeBtn}
        >
          {t('同意')}
        </Button>
      </div>
    </Drawer>
  );
};

export default React.forwardRef(AgreeAgreementDrawer);
