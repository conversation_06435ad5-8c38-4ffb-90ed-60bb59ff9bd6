.container {
  position: absolute;
  width: 100%;
  height: 100%;
  text-align: center;
  background: url('@/_/assets/images/access-control/personal-background.png') no-repeat center
    center;
  background-size: cover;
}

.title {
  margin-top: 6rem;
  font-size: 3rem;
  font-weight: 500;
  color: #fff;
}

.card {
  display: inline-block;
  width: 90%;
  height: 65%;
  margin-top: 7rem;
  text-align: center;
  background-color: #fff;
  border-radius: 50px;

  .hole {
    display: inline-block;
    width: 30%;
    height: 3%;
    margin-top: 2rem;
    background-color: #1070e9;
    border-radius: 50px;
  }

  .innerBox {
    display: inline-block;
    width: 85%;
    height: 83%;
    margin-top: 2rem;
    background-color: #e9f2ff;
    border-radius: 50px;

    .bodyBox {
      display: inline-block;
      width: 100%;
      height: 85%;

      .avatar img {
        display: inline-block;
        width: 16rem;
        height: 16rem;
        margin-top: 13%;
        border-radius: 50%;
        object-fit: cover;
      }

      .status p.change {
        margin-top: 3%;
        font-size: 2rem;
        color: #197afa;
        cursor: pointer;
      }

      .status p.approval {
        margin-top: 3%;
        font-size: 2rem;
        color: #ffa940;
      }

      .name {
        margin-top: 10%;
        font-size: 3rem;
        font-weight: 500;
        color: #35383d;
      }

      .workNum {
        margin-top: 3%;
        font-size: 2rem;
        font-weight: 500;
        color: #35383d;
      }
    }

    .footerBox {
      display: block;
      width: 100%;
      height: 15%;
      padding: 5%;
      overflow: hidden;
      font-size: 2rem;
      font-weight: 500;
      color: #666c7c;
      text-overflow: ellipsis;
      word-break: keep-all;
      background-color: #d7e5f9;
      border-radius: 0 0 50px 50px;
    }
  }
}
