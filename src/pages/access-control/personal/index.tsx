import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import NoFaceImg from '@/_/assets/images/access-control/no-face.png';
import Icon from '@/_/components/Icon';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { useMount } from '@shein-lego/use';
import { Dialog, Drawer } from 'shineout-mobile';
import { IGetStaffInfoResponse } from './interfaces';
import { getStaffInfo } from './services';
import styles from './style.less';

/**
 * @description Personal
 * @returns {unknown} desc
 */
const Personal: React.FC = () => {
  usePageTitle(t('个人信息'));
  const [data, setData] = useState<IGetStaffInfoResponse>();
  const navigator = useNavigate();
  const [visible, setVisible] = useState(false);

  useMount(() => {
    getStaffInfo().then((res) => {
      console.log('getStaffInfo', res);
      if (res === null) {
        navigator('/access-control/personal/agreement');
      } else {
        setData(res);
        return;
      }
    });
  });

  /**
   * @description changeAvatar
   * @returns {unknown} desc
   */
  const changeAvatar = () => {
    Dialog.confirm({
      zIndex: 99,
      title: t('系统提示'),
      message: t('修改后的人脸信息将经由上级审核。'),
      onOk: () => {
        navigator('/access-control/personal/face-recording-staff');
      },
    });
  };

  return (
    <div className={styles.container}>
      <div className={styles.card}>
        <div className={styles.hole}></div>
        <div className={styles.innerBox}>
          <div className={styles.bodyBox}>
            <div className={styles.avatar}>
              <img src={data?.faceUrl ? data?.faceUrl : NoFaceImg} alt={t('头像')} />
            </div>
            <div className={styles.status}>
              {data?.flowStatus === 2 && (
                <p className={styles.change} onClick={() => changeAvatar()}>
                  {t('修改头像')}
                  <Icon
                    style={{ display: 'inline-block', position: 'relative', top: 3, left: 3 }}
                    name="m-camera"
                    fontSize={'2.6rem'}
                    color="#197afa"
                  />
                </p>
              )}
              {data?.flowStatus === 1 && <p className={styles.approval}>{t('审批中')}</p>}
            </div>
            <div className={styles.name}>{data?.staffName ?? '--'}</div>
            <div className={styles.workNum}>{data?.workNum ?? '--'}</div>
          </div>
          <div className={styles.footerBox} onClick={() => setVisible(true)}>
            {data?.authStr}
          </div>
        </div>
      </div>
      <Drawer overlay visible={visible} onClose={() => setVisible(false)}>
        <div
          style={{
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <div
            style={{
              textAlign: 'center',
              lineHeight: '2rem',
              backgroundColor: '#fff',
              width: '50%',
              padding: 30,
              wordBreak: 'break-all',
            }}
          >
            {data?.authStr}
          </div>
        </div>
      </Drawer>
    </div>
  );
};

export default Personal;
