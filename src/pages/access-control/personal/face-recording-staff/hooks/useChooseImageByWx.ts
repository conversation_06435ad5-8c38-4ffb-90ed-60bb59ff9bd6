import { useEffect, useState } from 'react';
import { getHost, post } from '@/utils';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { Toast } from 'shineout-mobile';

type IApiResponse = {
  agentId: string;
  signature: string;
  noncestr: string;
  timestamp: string;
  traceId: string;
};

type IApiParams = {
  url: string;
};

/**
 * @description getAppIdByEnv
 * @returns {unknown} desc
 */
const getAppIdByEnv = () => {
  const host = getHost();
  const appIdMap = {
    dev: 'ww6e132f02586f7900',
    test: 'ww6e132f02586f7900',
    sit: 'ww5f025a4c596cbdd8',
    prod: 'wweb4abba124fabaad',
  };
  if (host.indexOf('lpmpm-dev') > -1) {
    return appIdMap.dev;
  } else if (host.indexOf('lpmpm-test') > -1) {
    return appIdMap.test;
  } else if (host.indexOf('lpmpm-sit') > -1) {
    return appIdMap.sit;
  } else if (host.indexOf('lpmpm') > -1) {
    return appIdMap.prod;
  }
  return appIdMap.dev;
};

const useChooseImageByWx = () => {
  const [isWechatReady, setIsWechatReady] = useState(false);

  useEffect(() => {
    const params: IApiParams = {
      url: location.href.split('#')[0],
    };
    post<IApiResponse>('wechat/msg/jssdk', params).then((data) => {
      wx.config({
        /** 必须这么写，否则wx.invoke调用形式的jsapi会有问题 */
        beta: true,
        /** 开启调试模式,调用的所有api的返回值会在客户端alert出来 */
        debug: false,
        /** 必填，企业微信的corpID，必须是本企业的corpID，不允许跨企业使用 */
        appId: getAppIdByEnv(),
        /** 必填，生成签名的时间戳 */
        timestamp: data.timestamp,
        /** 必填，生成签名的随机串 */
        nonceStr: data.noncestr,
        /** 必填，签名，见 附录-JS-SDK使用权限签名算法 */
        signature: data.signature,
        /** 必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来 */
        jsApiList: ['chooseImage', 'getLocalImgData'],
      });
      wx.ready(() => {
        setIsWechatReady(true);
        /** 需要检测的JS接口列表，这一步非必要 */
        wx.checkJsApi({
          jsApiList: ['chooseImage', 'getLocalImgData'],
          success: function (res: Record<string, any>): void {
            // Toast.info(`wx.checkJsApi.chooseImage & getLocalImgData ${JSON.stringify(res)}`);
            console.log('wx.checkJsApi.chooseImage & getLocalImgData', res);
          },
        });
      });
      wx.error((err) => {
        // Toast.info(`wx.error ${JSON.stringify(err)}`);
        console.log('wx.error', err);
      });
    });
  }, []);

  const chooseImage = usePersistFn((setFaceImage) => {
    wx.invoke?.(
      'chooseImage',
      {
        // 默认9
        count: 1,
        // 可以指定是原图还是压缩图，默认二者都有
        sizeType: ['original', 'compressed'],
        // 可以指定来源是相册还是相机，默认二者都有
        sourceType: ['camera'],
        //表示进入拍照界面的默认模式，目前有normal与batch两种选择，normal表示普通单拍模式，batch表示连拍模式，不传该参数则为normal模式。从3.0.26版本开始支持front和batch_front两种值，其中front表示默认为前置摄像头单拍模式，batch_front表示默认为前置摄像头连拍模式。（注：用户进入拍照界面仍然可自由切换两种模式）
        defaultCameraMode: 'normal',
        //整型值，0表示拍照时不保存到系统相册，1表示自动保存，默认值是1
        isSaveToAlbum: 1,
      },
      // 返回选定照片的本地ID列表，
      // Android中localId可以作为img标签的src属性显示图片；
      // iOS应当使用 getLocalImgData 获取图片base64数据，从而用于img标签的显示（在img标签内使用 wx.chooseImage 的 localid 显示可能会不成功）
      (res) => {
        // Toast.info('chooseImage', JSON.stringify(res));
        if (res.errMsg === 'chooseImage:ok' || res.err_Info === 'success') {
          console.log('chooseImage:ok', res);
          let localId;
          // 解决IOS无法上传的坑
          if (res.localIds[0].indexOf('wxlocalresource') !== -1) {
            localId = localId.replace('wxlocalresource', 'wxLocalResource');
          } else {
            localId = res.localIds[0];
          }
          wx.invoke(
            'getLocalImgData',
            {
              localId,
            },
            (res) => {
              console.log('getLocalImgData', res, res.err_Info);
              if (res.err_Info === 'success') {
                // Toast.success(t('上传成功'));
                setFaceImage(res.localData);
              } else {
                // Toast.info(t('上传失败'));
              }
            },
          );
        } else if (res.err_msg === 'chooseImage:ok') {
          console.log(t('当前是安卓'));
          // setFaceImage(JSON.parse(res.localIds)[0]);
          wx.invoke(
            'getLocalImgData',
            {
              localId: JSON.parse(res.localIds)[0],
            },
            (res) => {
              if (res.err_msg === 'getLocalImgData:ok') {
                Toast.success(t('上传成功'));
                // localData是图片的base64数据，可以用img标签显示
                setFaceImage(res.localData);
              } else {
                Toast.info(t('图片获取失败'));
              }
            },
          );
        } else {
          console.log('chooseImage:fail', res);
          if (res.errMsg === 'chooseImage:cancel') {
            Toast.info(t('已取消'));
            return;
          }
          if (res?.errMsg === 'chooseImage:camera sourceType is not supported') {
            Toast.fail(t('拍照失败，设备不支持，请在手机上进行操作'));
          }
          // Toast.info(t('上传失败'));
        }
      },
    );
  });

  const uploadImage = usePersistFn((setFaceImage) => {
    wx.invoke?.(
      'chooseImage',
      {
        // 默认9
        count: 1,
        // 可以指定是原图还是压缩图，默认二者都有
        sizeType: ['original', 'compressed'],
        // 可以指定来源是相册还是相机，默认二者都有
        sourceType: ['album'],
        //表示进入拍照界面的默认模式，目前有normal与batch两种选择，normal表示普通单拍模式，batch表示连拍模式，不传该参数则为normal模式。从3.0.26版本开始支持front和batch_front两种值，其中front表示默认为前置摄像头单拍模式，batch_front表示默认为前置摄像头连拍模式。（注：用户进入拍照界面仍然可自由切换两种模式）
        defaultCameraMode: 'normal',
        //整型值，0表示拍照时不保存到系统相册，1表示自动保存，默认值是1
        isSaveToAlbum: 1,
      },
      // 返回选定照片的本地ID列表，
      // Android中localId可以作为img标签的src属性显示图片；
      // iOS应当使用 getLocalImgData 获取图片base64数据，从而用于img标签的显示（在img标签内使用 wx.chooseImage 的 localid 显示可能会不成功）
      (res) => {
        if (res.errMsg === 'chooseImage:ok' || res.err_Info === 'success') {
          const localIds = res.localIds;
          wx.invoke(
            'getLocalImgData',
            {
              localId: localIds[0],
            },
            (res) => {
              if (res.err_Info === 'success') {
                Toast.success(t('上传成功'));
                // localData是图片的base64数据，可以用img标签显示
                setFaceImage(res.localData);
              }
            },
          );
        } else if (res.err_msg === 'chooseImage:ok') {
          console.log(t('当前是安卓'));
          // setFaceImage(JSON.parse(res.localIds)[0]);
          wx.invoke(
            'getLocalImgData',
            {
              localId: JSON.parse(res.localIds)[0],
            },
            (res) => {
              if (res.err_msg === 'getLocalImgData:ok') {
                Toast.success(t('上传成功'));
                // localData是图片的base64数据，可以用img标签显示
                setFaceImage(res.localData);
              } else {
                Toast.info(t('图片获取失败'));
              }
            },
          );
        } else {
          console.log('chooseImage:fail', res);
          if (res.errMsg === 'chooseImage:cancel') {
            Toast.info(t('已取消'));
            return;
          }
          if (res?.errMsg === 'chooseImage:camera sourceType is not supported') {
            Toast.fail(t('拍照失败，设备不支持，请在手机上进行操作'));
          }
          // Toast.info(t('上传失败'));
        }
      },
    );
  });
  return { chooseImage, uploadImage, isWechatReady } as const;
};
export default useChooseImageByWx;
