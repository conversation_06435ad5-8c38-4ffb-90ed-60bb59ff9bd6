import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import DemoAvatar from '@/_/assets/images/access-control/avatar1.png';
import CircleDash from '@/_/assets/images/access-control/circle-dash.png';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { Button, Dialog, Toast } from 'shineout-mobile';
import { base64ToFile } from '../../utils';
import { uploadFaceImage } from '../services';
import useChooseImageByWx from './hooks/useChooseImageByWx';
import styles from './style.less';

/**
 * 员工端人脸录入
 * 运行环境：企业微信
 * @returns
 */
const FaceRecordingStaff = () => {
  usePageTitle(t('人脸录入'));
  const { chooseImage, isWechatReady } = useChooseImageByWx();
  const [faceImage, setFaceImage] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const navigator = useNavigate();

  const handleTakePhotoClick = usePersistFn(() => {
    chooseImage(setFaceImage);
  });

  /**
   * @description handleSubmit
   * @returns {unknown} desc
   */
  const handleSubmit = () => {
    const imageFileObj = base64ToFile(faceImage, t('人脸图片'), 'faceFile');
    setLoading(true);
    uploadFaceImage(imageFileObj)
      .then((res) => {
        Toast.success(res.msg);
        navigator('/access-control/personal');
      })
      .catch((err) => {
        if (err?.code === '*********') {
          Dialog.confirm({
            zIndex: 99,
            title: t('录入人脸失败'),
            message: t('检测不到人脸，请调整姿势重拍'),
            onOk: () => handleTakePhotoClick(),
          });
        } else {
          Toast.info(err?.msg || t('录入人脸失败，请退出重试'));
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className={styles.container}>
      <div className={styles.circleMark}>
        <div className={styles.hole}></div>
        <img className={styles.circleDash} src={CircleDash} />
      </div>
      <div className={styles.content}>
        <div className="flex items-center justify-center min-h-0 flex-1">
          <img className={styles.avatarBg} src={faceImage} />
        </div>

        <div className={styles.infoBox}>
          <div className={styles.standard}>
            <img className={styles.avatar} src={DemoAvatar} />
            <div className={styles.text}>
              <p>{t('注意事项')}</p>
              <li>{t('左边为标准照片')}</li>
              <li>{t('人脸端正')}</li>
              <li>{t('避免角度倾斜，尽量露出双耳')}</li>
              <li>{t('如照片不规范，将无法进出')}</li>
            </div>
          </div>
          {faceImage === '' ? (
            <div className={styles.btnGroup}>
              <Button
                disabled={!isWechatReady}
                className={styles.btn}
                type="primary"
                onClick={() => handleTakePhotoClick()}
              >
                {t('拍照')}
              </Button>
            </div>
          ) : (
            <div className={styles.btnGroup}>
              <Button
                className={styles.btn}
                type="primary"
                plain
                onClick={() => handleTakePhotoClick()}
              >
                {t('重拍')}
              </Button>
              <Button
                loading={loading}
                className={styles.btn}
                type="primary"
                onClick={() => handleSubmit()}
              >
                {t('提交')}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FaceRecordingStaff;
