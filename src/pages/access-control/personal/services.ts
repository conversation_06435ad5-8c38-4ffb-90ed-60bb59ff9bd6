import { get, post } from '@/utils';
import {
  IfaceChangeInfoResponse,
  IFaceChangeReviewResponse,
  IGetStaffInfoResponse,
  IUploadFaceImageParams,
  IUploadFaceImageResponse,
} from './interfaces';

/**
 * 人脸检测接口
 * https://soapi.sheincorp.cn/application/3580/routes/169337/doc
 */
export const uploadFaceImage = (params: IUploadFaceImageParams | any) => {
  return post<{ data: IUploadFaceImageResponse; msg: string }>('/ams/staff/detectFace', params, {
    showErrorMsg: false,
  });
};

/**
 * 员工门禁信息
 * https://soapi.sheincorp.cn/application/3580/routes/169338/doc
 */
export const getStaffInfo = () => {
  return get<IGetStaffInfoResponse>('/ams/staff/staffInfo', {});
};

/**
 * 员工更换人脸审核
 * https://soapi.sheincorp.cn/application/3580/routes/169339/doc
 */
export const faceChangeReview = (params: IFaceChangeReviewResponse) => {
  return post<boolean>('/ams/staff/faceChangeReview', params);
};

/**
 * 员工更换人脸审核信息
 * https://soapi.sheincorp.cn/application/3580/routes/169340/docarams
 */
export const faceChangeInfo = (params: { id: number }) => {
  return get<IfaceChangeInfoResponse>('/ams/staff/faceChangeInfo', params);
};
