import { MutableRefObject, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMount } from '@shein-lego/use';
import { checkStaffAgreeInCookie } from '../../utils';
import AgreeAgreementDrawer, { IAgreementRef } from '../components/AgreeAgreementDrawer';

/**
 * @description FaceAgreement
 * @returns {unknown} desc
 */
const FaceAgreement = () => {
  const agreementRef: MutableRefObject<IAgreementRef | null> = useRef(null);
  const navigator = useNavigate();

  useMount(() => {
    if (checkStaffAgreeInCookie()) {
      navigator('/access-control/personal/face-recording-staff');
    }

    agreementRef.current?.open();
  });

  return (
    <div>
      <AgreeAgreementDrawer
        ref={agreementRef}
        onClose={() => {
          navigator('/');
        }}
      />
    </div>
  );
};

export default FaceAgreement;
