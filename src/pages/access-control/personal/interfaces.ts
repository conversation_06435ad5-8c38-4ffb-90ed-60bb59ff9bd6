export interface IUploadFaceImageParams {
  faceFile: any;
}

export interface IUploadFaceImageResponse {
  /** 工号 */
  workNum: string;
  /** 姓名 */
  staffName: string;
  /** 门禁权限 园区1、园区2 */
  authStr: string;
  /** 人脸照片 */
  faceUrl: string;
  /** 流程状态 1 审核中 2通过 */
  flowStatus: number;
}

export type IGetStaffInfoResponse = {
  /** 工号 */
  workNum: string;
  /** 姓名 */
  staffName: string;
  /** 门禁权限 园区1、园区2 */
  authStr: string;
  /** 人脸照片 */
  faceUrl: string;
  /** 流程状态  1 审核中 2通过 */
  flowStatus: number;
} | null;

export interface IFaceChangeReviewResponse {
  id: number;
  /** 审核状态1通过 2拒绝 */
  status: 1 | 2;
}

export interface IfaceChangeInfoResponse {
  /** 申请日期 */
  createTime: string;
  /** 申请人工号 */
  workNum: string;
  /** 申请人姓名 */
  staffName: string;
  /** 人脸照片 */
  lastFaceUrl: string;
  /** 人脸照片 */
  newFaceUrl: string;
  /** 部门 */
  oaDepartmentAllName: string;
}
