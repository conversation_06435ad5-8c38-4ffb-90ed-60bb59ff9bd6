import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMount } from '@shein-lego/use';
import { checkEnableRegisterFace } from '../my-wallet/api';
import Personal from './personal';

/**
 * @description AccessControl
 * @returns {unknown} desc
 */
const AccessControl: React.FC = () => {
  const [checkedResult, setCheckedResult] = useState<boolean>(false);
  const navigate = useNavigate();
  useMount(() => {
    checkEnableRegisterFace('/access-control')
      .then(() => {
        setCheckedResult(true);
        console.log('checkEnableRegisterFace.success');
      })
      .catch(() => {
        setCheckedResult(false);
        navigate('/');
      });
  });

  return <>{checkedResult && <Personal />}</>;
};

export default AccessControl;
