import React, { CSSProperties, useEffect, useMemo, useRef, useState } from 'react';
import { Drawer } from 'shineout-mobile';

type IProps = {
  text?: string;
  style?: CSSProperties;
  rows?: number;
};

/**
 * 超出行文本显示缩略
 * @param props
 * @returns
 */
const EllipsisText: React.FC<IProps> = (props) => {
  const { text, style = {}, rows = 1 } = props;
  const [visible, setVisible] = useState<boolean>(false);

  const elRef = useRef<HTMLSpanElement>(null);

  const [ellipsis, setEllipsis] = useState<boolean>(false);

  console.log('ellipsis', ellipsis);

  useEffect(() => {
    if (!elRef.current) {
      return;
    }

    const el = elRef.current;

    if (el.scrollWidth > el.clientWidth) {
      setEllipsis(true);
    } else {
      setEllipsis(false);
    }

    if (el.scrollHeight > el.clientHeight) {
      setEllipsis(true);
    } else {
      setEllipsis(false);
    }
  }, [rows, text]);

  const mergedStyle: CSSProperties = useMemo(() => {
    return {
      ...style,
      whiteSpace: 'pre-line',
      textOverflow: 'ellipsis',
      overflow: 'hidden',
      display: '-webkit-box',
      width: '100%',
      height: 'auto',
      WebkitBoxOrient: 'vertical',
      WebkitLineClamp: rows,
    };
  }, [rows, style]);

  if (ellipsis) {
    return (
      <>
        <span ref={elRef} style={mergedStyle} onClick={() => setVisible(true)}>
          {text}
        </span>

        <Drawer overlay visible={visible} onClose={() => setVisible(false)}>
          <div
            style={{
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                textAlign: 'center',
                lineHeight: '2rem',

                backgroundColor: '#fff',
                width: '50%',
                padding: 30,
                wordBreak: 'break-all',
              }}
            >
              {text}
            </div>
          </div>
        </Drawer>
      </>
    );
  }

  return (
    <span ref={elRef} style={mergedStyle}>
      {text}
    </span>
  );
};

export default EllipsisText;
