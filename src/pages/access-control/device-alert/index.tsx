import React from 'react';
import { Page, PageContentLoading } from '@/_/components';
import { formatDate } from '@/utils';
import { t } from '@shein-bbl/react';
import { useRequest } from 'ahooks';
import moment from 'moment';
import { Table } from 'shineout-mobile';
import { getOfflineDevices } from './api';
import styles from './index.less';

/**
 * @description Main
 * @returns {unknown} desc
 */
const Main: React.FC = () => {
  const { data, loading } = useRequest(getOfflineDevices);

  const columns = [
    { title: t('所属片区'), render: 'regionName', fixed: true },
    { title: t('所属园区'), render: 'parkName' },
    { title: t('所属子仓'), render: 'subWarehouseName' },
    { title: t('设备名称'), render: 'deviceName' },
    { title: t('IP地址'), render: 'deviceIp' },
  ];

  if (loading) {
    return (
      <Page title={t('门禁设备告警')} className="bg-white" contentClassName="bg-white">
        <PageContentLoading />
      </Page>
    );
  }

  return (
    <Page title={t('门禁设备告警')} contentClassName="bg-white">
      <h1 className={styles.title}>
        {t('【门禁设备告警】')}
        {formatDate(moment())}
      </h1>
      <div className={styles.tips}>
        <div>{t('1、请检查设备是否供电正常；')}</div>
        <div>{t('2、如果设备供电正常，请尝试断电重启设备；')}</div>
        <div>{t('3、如以上方式无法解决，请联系设备厂商处理；')}</div>
      </div>
      <Table
        className={styles.table}
        style={{ minWidth: 500 }}
        data={data || []}
        size="small"
        striped
        split
        columns={columns}
        keygen="id"
        fixHead
        empty={t('无数据')}
      />
    </Page>
  );
};

export default Main;
