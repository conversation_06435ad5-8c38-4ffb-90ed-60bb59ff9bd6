import { useEffect, useRef } from 'react';
import { useUnmount } from '@shein-lego/use';
import { Toast } from 'shineout-mobile';
import { IAppointmentAllInfo } from '../../interfaces';
import { saveFlowDataInfo } from '../services';
import { CurrentStepsEnum } from '../typings';

/**
 * 半分钟自动保存一次
 */
const AUTO_SAVE_TIME = 1000 * 60 * 0.5;
const useAutosave = (
  data: IAppointmentAllInfo,
  setData,
  currentSteps: CurrentStepsEnum,
  unLoginCallback: () => void,
  starting: boolean,
) => {
  const timer = useRef<any>();
  const dataRef = useRef<any>();
  const currentStepsRef = useRef<any>();
  const startingRef = useRef<any>();

  useEffect(() => {
    dataRef.current = data;
    currentStepsRef.current = currentSteps;
    startingRef.current = starting;
  }, [currentSteps, data, starting]);

  useEffect(() => {
    timer.current = setInterval(() => {
      // console.log('auto save.data', currentStepsRef.current, startingRef.current);
      if (
        currentStepsRef.current !== CurrentStepsEnum.ONE_STEP &&
        currentStepsRef.current !== CurrentStepsEnum.FOUR_STEP &&
        startingRef.current
      ) {
        saveFlowDataInfo(
          {
            ...dataRef.current,
            needValidate: false,
          },
          {
            showErrorMsg: false,
          },
        )
          .then((res) => {
            console.log('auto save then');
            if (!res) {
              unLoginCallback();
              return;
            }
            setData(res);
          })
          .catch((err) => {
            console.log('auto save catch');
            Toast.info(err?.msg);
            unLoginCallback();
          });
      }
    }, AUTO_SAVE_TIME);
    return () => {
      clearInterval(timer.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useUnmount(() => {
    clearInterval(timer.current);
  });
};

export default useAutosave;
