import { useEffect, useState } from 'react';
import { get } from '@/utils';
import { t } from '@shein-bbl/core';
import { usePersistFn } from '@shein-lego/use';
import { Toast } from 'shineout-mobile';
// import wx from 'weixin-js-sdk';
import { isWeiXin } from '../../utils';

/**
 * @description sdkService
 * @param {unknown} url desc
 * @returns {unknown} desc
 */
const sdkService = (url) => {
  return get<{
    appId: string;
    nonceStr: string;
    timestamp: number;
    url: string;
    signature: string;
  }>('/weixin/createJsapiSignature', { url });
};

const useWxSdk = () => {
  const [isNoSupportWxSdk, setIsNoSupportWxSdk] = useState(false);
  useEffect(() => {
    if (!isWeiXin()) {
      setIsNoSupportWxSdk(true);
      return;
    }
    sdkService(location.href.split('#')[0]).then((res) => {
      const { appId, nonceStr, timestamp, signature } = res;
      wx.config({
        debug: false,
        appId,
        timestamp,
        nonceStr,
        signature,
        jsApiList: ['chooseImage', 'uploadImage'],
      });

      wx.ready(() => {
        /** 需要检测的JS接口列表，这一步非必要 */
        wx.checkJsApi({
          jsApiList: ['chooseImage', 'getLocalImgData'],
          success: function (res: Record<string, any>): void {
            // Toast.info('wx.checkJsApi.chooseImage & getLocalImgData', JSON.stringify(res));
            console.log('wx.checkJsApi.chooseImage & getLocalImgData', res);
          },
          error: () => {
            setIsNoSupportWxSdk(true);
          },
        });
      });
      wx.error(() => {
        setIsNoSupportWxSdk(true);
      });
    });
  }, []);

  const chooseImage = usePersistFn((setFaceImage) => {
    try {
      wx.invoke(
        'chooseImage',
        {
          // 默认9
          count: 1,
          // 可以指定是原图还是压缩图，默认二者都有
          sizeType: ['original', 'compressed'],
          // 可以指定来源是相册还是相机，默认二者都有
          sourceType: ['album', 'camera'],
          //表示进入拍照界面的默认模式，目前有normal与batch两种选择，normal表示普通单拍模式，batch表示连拍模式，不传该参数则为normal模式。从3.0.26版本开始支持front和batch_front两种值，其中front表示默认为前置摄像头单拍模式，batch_front表示默认为前置摄像头连拍模式。（注：用户进入拍照界面仍然可自由切换两种模式）
          defaultCameraMode: 'normal',
          //整型值，0表示拍照时不保存到系统相册，1表示自动保存，默认值是1
          isSaveToAlbum: 1,
        },
        // 返回选定照片的本地ID列表，
        // Android中localId可以作为img标签的src属性显示图片；
        // iOS应当使用 getLocalImgData 获取图片base64数据，从而用于img标签的显示（在img标签内使用 wx.chooseImage 的 localid 显示可能会不成功）
        (res) => {
          // Toast.info('chooseImage', JSON.stringify(res));
          if (res.errMsg === 'chooseImage:ok') {
            let localId;
            //解决IOS无法上传的坑
            if (res.localIds[0].indexOf('wxlocalresource') !== -1) {
              localId = localId.replace('wxlocalresource', 'wxLocalResource');
            } else {
              localId = res.localIds[0];
            }
            wx.invoke(
              'getLocalImgData',
              {
                localId,
              },
              (res) => {
                if (res.errMsg === 'getLocalImgData:ok') {
                  Toast.success(t('上传成功'));
                  // localData是图片的base64数据，可以用img标签显示
                  setFaceImage(res.localData);
                } else {
                  if (res.errMsg === 'chooseImage:cancel') {
                    return Toast.info(t('取消上传'));
                  }
                  Toast.info(res.errMsg);
                }
              },
            );
          } else if (res.err_msg === 'chooseImage:ok') {
            console.log(t('当前是安卓'));
            wx.invoke(
              'getLocalImgData',
              {
                localId: JSON.parse(res.localIds)[0],
              },
              (res) => {
                if (res.err_msg === 'getLocalImgData:ok') {
                  Toast.success(t('上传成功'));
                  // localData是图片的base64数据，可以用img标签显示
                  setFaceImage(res.localData);
                } else {
                  Toast.info(t('图片获取失败'));
                }
              },
            );
            // setFaceImage(JSON.parse(res.localIds)[0]);
          } else {
            if (res?.errMsg === 'chooseImage:cancel') {
              return Toast.info(t('取消上传'));
            }
            Toast.info(res?.errMsg);
          }
        },
      );
    } catch (_error) {
      setIsNoSupportWxSdk(true);
    }
  });

  const uploadImage = usePersistFn((setFaceImage, cameraCanvasRef) => {
    wx.invoke(
      'chooseImage',
      {
        // 默认9
        count: 1,
        // 可以指定是原图还是压缩图，默认二者都有
        sizeType: ['original', 'compressed'],
        // 可以指定来源是相册还是相机，默认二者都有
        sourceType: ['album'],
        //表示进入拍照界面的默认模式，目前有normal与batch两种选择，normal表示普通单拍模式，batch表示连拍模式，不传该参数则为normal模式。从3.0.26版本开始支持front和batch_front两种值，其中front表示默认为前置摄像头单拍模式，batch_front表示默认为前置摄像头连拍模式。（注：用户进入拍照界面仍然可自由切换两种模式）
        defaultCameraMode: 'normal',
        //整型值，0表示拍照时不保存到系统相册，1表示自动保存，默认值是1
        isSaveToAlbum: 1,
      },
      // 返回选定照片的本地ID列表，
      // Android中localId可以作为img标签的src属性显示图片；
      // iOS应当使用 getLocalImgData 获取图片base64数据，从而用于img标签的显示（在img标签内使用 wx.chooseImage 的 localid 显示可能会不成功）
      (res) => {
        if (res.errMsg === 'chooseImage:ok' || res.err_Info === 'success') {
          const localIds = res.localIds;
          wx.invoke(
            'getLocalImgData',
            {
              localId: localIds[0],
            },
            (res) => {
              if (res.errMsg === 'getLocalImgData:ok') {
                Toast.success(t('上传成功'));
                setFaceImage(res.localData); // localData是图片的base64数据，可以用img标签显示
                cameraCanvasRef.current.getContext('2d').drawImage(res.localData, 0, 0);
              }
            },
          );
        } else if (res.err_msg === 'chooseImage:ok') {
          console.log(t('当前是安卓'));
          // setFaceImage(JSON.parse(res.localIds)[0]);
          wx.invoke(
            'getLocalImgData',
            {
              localId: JSON.parse(res.localIds)[0],
            },
            (res) => {
              if (res.err_msg === 'getLocalImgData:ok') {
                Toast.success(t('上传成功'));
                // localData是图片的base64数据，可以用img标签显示
                setFaceImage(res.localData);
              } else {
                Toast.info(t('图片获取失败'));
              }
            },
          );
        } else {
          console.log('chooseImage:fail', res);
          if (res.errMsg === 'chooseImage:cancel') {
            Toast.info(t('已取消'));
            return;
          }
          // Toast.info(t('上传失败'));
        }
      },
    );
  });

  return { chooseImage, uploadImage, isNoSupportWxSdk };
};

export default useWxSdk;
