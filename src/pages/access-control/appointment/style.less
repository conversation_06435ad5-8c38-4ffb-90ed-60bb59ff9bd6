.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow: auto;
  background-color: white;
  -webkit-overflow-scrolling: touch;
  flex: 1;
  overscroll-behavior: none;

  .headerImg {
    width: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    overflow: auto;
  }

  .stepBox {
    position: absolute;
    top: 0;
    right: 8%;
    left: 8%;
    width: 84%;
    // height: 100%;

    :global(.sm-steps) {
      margin-top: 2rem;
      background: transparent !important;
      background-color: transparent !important;

      :global(.sm-steps-title) {
        color: white !important;
      }

      :global(.sm-steps-finish) {
        /* stylelint-disable-next-line max-nesting-depth */
        :global(.sm-steps-line) {
          background: rgb(86 217 193) !important;
        }
      }
    }

    :global(.sm-steps-horizontal .sm-steps-line.sm-steps-finish) {
      background: rgb(86 217 193) !important;
    }
  }

  // .step1 {
  //   position: relative;
  //   padding: 0 15px;
  //   background-color: white;

  //   .formBox {
  //     position: relative;
  //     margin-top: 2rem;

  //     /* stylelint-disable-next-line selector-id-pattern */
  //     #captchaBox {
  //       position: relative;
  //       margin: 0 auto;
  //       margin-top: 1rem;
  //       margin-bottom: 1rem;
  //       text-align: center;
  //     }
  //   }

  //   .checkBox {
  //     position: relative;
  //     padding-left: 4rem;
  //     margin-top: 1rem;
  //     text-align: center;
  //   }

  //   .submitBox {
  //     position: relative;
  //     margin-top: 8rem;
  //     margin-top: 3rem;
  //     margin-bottom: 2rem;
  //     text-align: center;
  //   }
  // }

  .step2 {
    position: relative;
    padding: 0 15px;
    background-color: white;

    .submitBox {
      position: relative;
      display: flex;
      margin-top: 10rem;
      margin-bottom: 2rem;
      text-align: center;
      flex-direction: row;
      align-items: center;
    }
  }

  .step3 {
    position: relative;
    padding: 0 0 5px;
    margin-bottom: 60px;
    background-color: white;

    :global(.sm-radio-group .sm-radio) {
      height: 24px !important;
    }

    :global(.sm-upload-handler) {
      grid-column: 4;
    }

    .tongxingrenContainer:first-child {
      margin-top: 0;
    }

    .tongxingrenContainer {
      position: relative;
      margin: 15px;
      overflow: hidden;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 8px 0 rgb(20 23 55 / 5%);

      .itemHeader {
        display: flex;
        padding: 15px 12px 10px;
        background-color: white;
        flex-direction: row;

        /* stylelint-disable-next-line max-nesting-depth */
        .title {
          flex-grow: 1;
          font-size: 14px;
          font-weight: 500;
          text-align: left;
        }
        /* stylelint-disable-next-line max-nesting-depth */
        .icon {
          width: 30px;
          text-align: center;
        }
      }
    }

    .singleAddButton {
      width: 100%;
      padding: 15px 0;
      margin-top: -20px;
      text-align: center;
      background-color: #f4f5f8;
    }
  }

  .footerSubmitBox {
    position: fixed;
    bottom: 0;
    display: flex;
    width: 100%;
    height: 60px;
    max-width: 750px;
    text-align: center;
    background-color: white;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }

  .step4 {
    position: relative;
    padding: 0 10px 5px;
    margin-bottom: 60px;
    background-color: white;

    :global(.sm-upload-handler) {
      grid-column: 4;
    }

    .tongxingrenBox {
      position: relative;
      padding: 0 7px;
      margin-top: 10px;
    }

    .submitBox {
      position: relative;
      display: block;
      display: flex;
      margin-top: 3rem;
      margin-bottom: 1rem;
      text-align: center;
      flex-direction: row;
      align-items: center;
    }
  }
}
