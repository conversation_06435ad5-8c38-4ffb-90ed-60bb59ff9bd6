import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import HeaderBannerImg from '@/_/assets/images/access-control/header-banner.png';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { useMount, usePersistFn } from '@shein-lego/use';
import { omit } from 'lodash';
import { Dialog } from 'shineout-mobile';
import { IAppointmentAllInfo } from '../interfaces';
import { getTokenInCookie } from '../utils';
import StepBar from './components/StepBar';
import StepFour from './components/StepFour';
import StepOne from './components/StepOne';
import StepThree from './components/StepThree';
import StepTwo from './components/StepTwo';
import useAutosave from './hooks/useAutosave';
import { deleteAppointmentRecord, selectFlowData, selectVisitor } from './services';
import styles from './style.less';
import { CurrentStepsEnum } from './typings';

const MAX_TONGXINGREN_NUM = 20;

const Appointment: React.FC = () => {
  usePageTitle(t('物流园区访客预约平台'));
  const [currentSteps, setCurrentSteps] = useState<CurrentStepsEnum>();
  const [data, setData] = useState<IAppointmentAllInfo>();
  const [starting, setStarting] = useState<boolean>(false);

  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const isFromPassCardRebook = searchParams.get('isFromPassCardRebook') !== null;
  console.log(t('是否从预约记录的重新新建进来的'), isFromPassCardRebook);

  /**
   * @description unLoginCallback
   * @returns {unknown} desc
   */
  const unLoginCallback = () => {
    setCurrentSteps(CurrentStepsEnum.ONE_STEP);
    setStarting(false);
  };
  useAutosave(data, setData, currentSteps, unLoginCallback, starting);

  const handleCheckExistAppointment = usePersistFn(() => {
    selectFlowData().then((res) => {
      console.log(t('检查是否存在未填写完成的单据'), res !== null);
      if (res !== null) {
        if (isFromPassCardRebook) {
          selectFlowData().then((res) => {
            console.log('isFromPassCardRebook.selectFlowData', res);
            if (res === null) return;
            setData(omit(res, 'formAppointmentInfoDto.appointmentStep'));
            setCurrentSteps(res?.formAppointmentInfoDto.appointmentStep + 1);
            setStarting(true);
          });
          return;
        }
        Dialog.confirm({
          coveringClose: false,
          zIndex: 99,
          title: t('系统提示'),
          message: t('系统检测到您有尚未完成的表单，是否继续填写？'),
          onOk: () => {
            console.log(t('使用未完成的单据继续填写'));
            selectFlowData().then((res) => {
              if (res === null) return;
              setData(omit(res, 'formAppointmentInfoDto.appointmentStep'));
              setCurrentSteps(res?.formAppointmentInfoDto?.appointmentStep + 1);
              setStarting(true);
            });
          },
          onCancel: () => {
            console.log(t('不使用未完成的单据继续填写'));
            setCurrentSteps(CurrentStepsEnum.TWO_STEP);
            deleteAppointmentRecord();
            setStarting(true);
          },
        });
      } else {
        if (currentSteps === CurrentStepsEnum.ONE_STEP) return;
        // 获取访客信息
        selectVisitor().then((res) => {
          setCurrentSteps(CurrentStepsEnum.TWO_STEP);
          setData({
            ...data,
            visitorDto: res,
          });
        });
        setStarting(true);
      }
    });
  });

  // 初始化
  useMount(() => {
    setTimeout(() => {
      if (getTokenInCookie() !== '') {
        setCurrentSteps(CurrentStepsEnum.TWO_STEP);
        handleCheckExistAppointment();
      } else {
        setCurrentSteps(CurrentStepsEnum.ONE_STEP);
      }
    }, 300);
  });

  return (
    <div className={styles.container}>
      <img className={styles.headerImg} src={HeaderBannerImg} alt={t('头部底图')} />

      <StepBar currentSteps={currentSteps} />

      {currentSteps === CurrentStepsEnum.ONE_STEP && (
        <StepOne
          currentSteps={currentSteps}
          setCurrentSteps={setCurrentSteps}
          setStarting={setStarting}
          handleCheckExistAppointment={handleCheckExistAppointment}
        />
      )}

      {currentSteps === CurrentStepsEnum.TWO_STEP && (
        <StepTwo
          data={data}
          setData={setData}
          currentSteps={currentSteps}
          setStarting={setStarting}
          setCurrentSteps={setCurrentSteps}
        />
      )}

      {currentSteps === CurrentStepsEnum.THREE_STEP && (
        <StepThree
          data={data}
          setData={setData}
          MAX_TONGXINGREN_NUM={MAX_TONGXINGREN_NUM}
          currentSteps={currentSteps}
          setCurrentSteps={setCurrentSteps}
        />
      )}

      {currentSteps === CurrentStepsEnum.FOUR_STEP && (
        <StepFour
          data={data}
          setData={setData}
          currentSteps={currentSteps}
          setCurrentSteps={setCurrentSteps}
        />
      )}
    </div>
  );
};

export default Appointment;
