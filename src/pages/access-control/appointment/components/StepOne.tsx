import { useState } from 'react';
import { phoneLogin } from '../../home/<USER>';
import { setTokenInCookie } from '../../utils';
import RegisterForm from './RegisterForm';

/**
 * @description StepOne
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const StepOne = ({ currentSteps, setCurrentSteps, setStarting, handleCheckExistAppointment }) => {
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);

  /**
   * @description handleSubmit
   * @param {unknown} phone desc
   * @param {unknown} code desc
   * @param {unknown} requestId desc
   * @returns {unknown} desc
   */
  const handleSubmit = (phone, code, requestId) => {
    setSubmitLoading(true);
    phoneLogin({
      phone,
      figureCode: code,
      requestId,
    })
      .then((res) => {
        setTokenInCookie(res);
        setSubmitLoading(false);
        setCurrentSteps(currentSteps + 1);
        handleCheckExistAppointment();
        setStarting();
      })
      .finally(() => {
        setSubmitLoading(false);
      });
  };
  return (
    <>
      <RegisterForm handleSubmit={handleSubmit} submitLoading={submitLoading} />
    </>
  );
};

export default StepOne;
