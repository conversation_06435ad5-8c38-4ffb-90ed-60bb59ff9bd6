.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow: auto;
  background-color: white;
  -webkit-overflow-scrolling: touch;
  flex: 1;
  overscroll-behavior: none;

  .headerImg {
    width: 100%;
    background-size: contain;
    background-repeat: no-repeat;
  }

  .title {
    position: absolute;
    top: 5rem;
    left: 1.5rem;
    font-size: 2.5rem;
    font-weight: 500;
    color: white;
  }

  .stepBox {
    position: absolute;
    top: 0;
    right: 8%;
    left: 8%;
    width: 84%;
    height: 100%;

    :global(.sm-steps) {
      margin-top: 2rem;
      background: transparent !important;
      background-color: transparent !important;

      :global(.sm-steps-title) {
        color: white !important;
      }

      :global(.sm-steps-finish) {
        /* stylelint-disable-next-line max-nesting-depth */
        :global(.sm-steps-line) {
          background: rgb(86 217 193) !important;
        }
      }
    }
  }

  .step1 {
    position: relative;
    padding: 0 15px;
    background-color: white;

    .formBox {
      position: relative;
      margin-top: 2rem;

      /* stylelint-disable-next-line selector-id-pattern */
      #captchaBox {
        position: relative;
        margin: 0 auto;
        margin-top: 1rem;
        margin-bottom: 1rem;
        text-align: center;
      }
    }

    .checkBox {
      position: relative;
      margin-top: 1rem;
    }

    .submitBox {
      position: relative;
      margin-top: 8rem;
      margin-top: 3rem;
      margin-bottom: 2rem;
      text-align: center;
    }
  }
}
