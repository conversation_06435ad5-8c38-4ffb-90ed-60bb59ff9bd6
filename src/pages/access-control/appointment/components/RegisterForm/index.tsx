import { MutableRefObject, useEffect, useRef, useState } from 'react';
import Icon from '@/_/components/Icon';
import { gtRegister, sendMessages } from '@/pages/access-control/home/<USER>';
import { objectHumpToLine } from '@/pages/access-control/utils';
import '@/_/assets/js/gt';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { Button, Checkbox, Field, Toast } from 'shineout-mobile';
import { checkSms } from '../../services';
import PrivateAgreementDrawer, { IAgreementRef } from '../PrivateAgreementDrawer';
import SafeAgreementDrawer from '../SafeAgreementDrawer';
import styles from './styles.less';

type IProps = {
  submitLoading: boolean;
  handleSubmit: (phone: string, code: string, requestId: string) => void;
};
const RegisterForm: React.FC<IProps> = ({ submitLoading, handleSubmit }) => {
  const [time, setTime] = useState(0);
  const gtRef = useRef<any>(null);
  const [requestId, setRequestId] = useState<string>('');
  const validatorInputRef = useRef<any>(null);
  const [phone, setPhone] = useState<string>('');
  const [code, setCode] = useState<string>('');
  const [codeVerityLoading, setCodeVerityLoading] = useState<boolean>(false);
  const [isCheckReadProtocol, setIsCheckReadProtocol] = useState(false);
  const isCanNextStep = phone.length === 11 && code.length === 6 && isCheckReadProtocol;
  const privateAgreementRef: MutableRefObject<IAgreementRef | null> = useRef(null);
  const safeAgreementRef: MutableRefObject<IAgreementRef | null> = useRef(null);

  useEffect(() => {
    if (time > 0) {
      setTimeout(() => {
        setTime(time - 1);
      }, 1000);
    }
  }, [time]);

  /**
   * @description gtHandle
   * @param {unknown} gt desc
   * @returns {unknown} desc
   */
  const gtHandle = (gt) => {
    gt.onReady(() => {
      gtRef.current = gt;
      gtRef.current.verify();
    })
      .onSuccess(() => {
        const result = gtRef.current?.getValidate();
        if (!result) {
          Toast.info(t('请完成验证'));
        }
        startSendCode(result?.geetest_challenge);
      })
      .onError(() => {
        setCodeVerityLoading(false);
      })
      .onClose(() => {
        setCodeVerityLoading(false);
      });
  };

  const handleClickSendCode = () => {
    if (phone.length === 0) {
      Toast.info(t('请输入手机号码'));
      return;
    }
    if (phone.length !== 11) {
      Toast.info(t('请输入正确的手机号码'));
      return;
    }
    if (!isCheckReadProtocol) {
      Toast.info(t('请阅读并勾选《隐私政策》与《安全管理须知》'));
      return;
    }
    checkSms({ phone }).then((res) => {
      if (res) {
        setCodeVerityLoading(true);
        gtRegister()
          .then((config) => {
            setRequestId(config?.requestId);
            // 降级方案： 不实用图形验证
            if (config?.enabled === false) {
              startSendCode(config?.challenge);
            } else {
              window.initGeetest(
                {
                  ...objectHumpToLine(config),
                  product: 'bind',
                },
                gtHandle,
              );
            }
          })
          .catch(() => {
            setCodeVerityLoading(false);
          });
      }
    });
  };

  const startSendCode = usePersistFn((challenge: string) => {
    sendMessages({
      challenge,
      requestId,
      phone,
    })
      .then(() => {
        setCodeVerityLoading(false);
        validatorInputRef.current?.focus();
        setTime(60);
        Toast.success(t('验证码已发送'));
      })
      .catch((err) => {
        if (err.code === '400199') {
          Toast.info(err.msg);
          gtRef.current.reset();
          gtRegister().then((config) => {
            setRequestId(config?.requestId);
            window.initGeetest(
              {
                ...objectHumpToLine(config),
                product: 'bind',
              },
              gtHandle,
            );
          });
        } else {
          Toast.info(err.msg);
        }
      })
      .finally(() => {
        setCodeVerityLoading(false);
      });
  });

  return (
    <>
      <div className={styles.step1} style={{ padding: '0 15px' }}>
        <div className={styles.formBox}>
          <Field
            label={t('手机号')}
            placeholder={t('请输入手机号码')}
            value={phone}
            extra={<></>}
            type="number"
            maxLength={11}
            onChange={(e) => {
              if ((e.target.value as any) < 0) {
                Toast.info(t('请输入正确的手机号码'));
                return;
              }
              setPhone(e.target.value);
            }}
            leftIcon={
              <span style={{ position: 'relative', top: 1, marginRight: 5 }}>
                <Icon color="#197afa" name="m-phone" fontSize={17} />
              </span>
            }
          />
          <Field
            forwardRef={(ref) => {
              validatorInputRef.current = ref;
            }}
            value={code}
            type="number"
            digits={0}
            maxLength={6}
            onChange={(e) => {
              if ((e.target.value as any) < 0) {
                Toast.info(t('请输入正确验证码'));
                return;
              }
              setCode(e.target.value);
            }}
            leftIcon={
              <span style={{ position: 'relative', top: 1, marginRight: 5 }}>
                <Icon name="pinkong" color="#197afa" fontSize={18} />
              </span>
            }
            extra={
              time ? (
                <Button
                  type="primary"
                  size="small"
                  plain
                  style={{
                    backgroundColor: '#E3EDFA',
                    color: '#197AFA',
                    width: 82,
                    borderColor: '#E3EDFA',
                    position: 'absolute',
                    right: 10,
                    top: 6,
                  }}
                >
                  {time}
                </Button>
              ) : (
                <Button
                  onClick={handleClickSendCode}
                  type="primary"
                  size="small"
                  loading={codeVerityLoading}
                  style={{
                    width: 82,
                    position: 'absolute',
                    right: 10,
                    top: 6,
                  }}
                >
                  {t('发送验证码')}
                </Button>
              )
            }
            label={t('验证码')}
            placeholder={t('请输入验证码')}
          />
        </div>
        <div className={styles.checkBox} style={{ marginTop: 10, paddingLeft: 15 }}>
          <Checkbox
            shape="square"
            label={
              <div style={{ fontSize: 12 }}>
                {t('我已阅读并同意')}
                <Button
                  text
                  type="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    privateAgreementRef.current?.open();
                  }}
                >
                  {t('《隐私政策》')}
                </Button>
                {t('和')}
                <Button
                  text
                  type="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    safeAgreementRef.current?.open();
                  }}
                >
                  {t('《安全管理须知》')}
                </Button>
              </div>
            }
            onChange={(val) => setIsCheckReadProtocol(val)}
          />
        </div>
        <div className={styles.submitBox}>
          <Button
            loading={submitLoading}
            style={{ width: '80%', marginTop: '5rem', marginLeft: '10%' }}
            type="primary"
            size="large"
            round
            disabled={!isCanNextStep}
            onClick={() => handleSubmit(phone, code, requestId)}
          >
            {t('下一步')}
          </Button>
        </div>
      </div>
      <PrivateAgreementDrawer ref={privateAgreementRef} />
      <SafeAgreementDrawer ref={safeAgreementRef} />
    </>
  );
};

export default RegisterForm;
