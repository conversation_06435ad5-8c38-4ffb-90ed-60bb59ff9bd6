import { t } from '@shein-bbl/react';
import { Loading } from 'shineout-mobile';

/**
 * @description PageLoading
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const PageLoading: React.FC<{ loading: boolean }> = ({ loading }) => {
  if (loading === false) return null;
  return (
    <div
      style={{
        zIndex: 999,
        position: 'absolute',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        width: '100%',
        flexDirection: 'column',
      }}
    >
      <Loading size={40} type="spinner"></Loading>
      <div style={{ marginTop: 6, color: 'gary' }}>{t('加载中...')}</div>
    </div>
  );
};

export default PageLoading;
