import { t } from '@shein-bbl/react';
import moment from 'moment';
import { Cell, NPicker } from 'shineout-mobile';

const getData = (date) => {
  const data = [
    {
      name: '00:00',
      disable: false,
      children: [
        {
          name: '06:00',
        },
        {
          name: '09:00',
        },
        {
          name: '12:00',
        },
        {
          name: '15:00',
        },
        {
          name: '18:00',
        },
        {
          name: '21:00',
        },
        {
          name: '23:59',
        },
      ],
    },
    {
      name: '06:00',
      disable: false,
      children: [
        {
          name: '09:00',
        },
        {
          name: '12:00',
        },
        {
          name: '15:00',
        },
        {
          name: '18:00',
        },
        {
          name: '21:00',
        },
        {
          name: '23:59',
        },
      ],
    },
    {
      name: '09:00',
      disable: false,
      children: [
        {
          name: '12:00',
        },
        {
          name: '15:00',
        },
        {
          name: '18:00',
        },
        {
          name: '21:00',
        },
        {
          name: '23:59',
        },
      ],
    },
    {
      name: '12:00',
      disable: false,
      children: [
        {
          name: '15:00',
        },
        {
          name: '18:00',
        },
        {
          name: '21:00',
        },
        {
          name: '23:59',
        },
      ],
    },
    {
      name: '15:00',
      disable: false,
      children: [
        {
          name: '18:00',
        },
        {
          name: '21:00',
        },
        {
          name: '23:59',
        },
      ],
    },
    {
      name: '18:00',
      disable: false,
      children: [
        {
          name: '21:00',
        },
        {
          name: '23:59',
        },
      ],
    },
    {
      name: '21:00',
      disable: false,
      children: [
        {
          name: '23:59',
        },
      ],
    },
  ];

  if (date === undefined) return data;

  // 今天之前
  if (moment(date).isBefore(moment().format('YYYY-MM-DD'))) {
    return data.map((item) => ({
      ...item,
      disable: true,
    }));
  }
  // 今天之后
  else if (moment(date).isAfter(moment().format('YYYY-MM-DD'))) {
    return data;
  }
  // 今天
  else {
    const reData = data.map((item) => {
      if (moment(date + ' ' + item.name + ':00').diff(moment(), 'seconds') < 0) {
        return {
          ...item,
          disable: true,
        };
      } else {
        return item;
      }
    });

    reData.forEach((item, index) => {
      if (reData.length === index + 1) {
        reData[index].disable = false;
      } else {
        if (
          moment(date + ' ' + item.name + ':00').diff(moment(), 'seconds') < 0 &&
          moment(date + ' ' + reData[index + 1].name + ':00').diff(moment(), 'seconds') > 0
        ) {
          reData[index].disable = false;
        }
      }
    });

    return reData;
  }
};

type IProps = {
  date: string;
  value: [string, string];
  onChange: (v: [string, string]) => void;
};

/**
 * @description SelectAppointmentTimeByPicker
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const SelectAppointmentTimeByPicker: React.FC<IProps> = ({ date, value, onChange }) => {
  return (
    <NPicker
      title={t('预约时间')}
      format={'name'}
      renderItem={(d) => d.name}
      closeAtFilterSelect
      keygen={'name'}
      data={getData(date)}
      disabled={(d) => (d?.disable === true ? true : false)}
      onChange={(data, _c, _i) => {
        onChange(data);
        console.log(data, _c, _i);
      }}
      value={value[0] === undefined ? undefined : value}
      onOk={(data) => {
        console.log('onOk', data);
        onChange(data);
      }}
    >
      <Cell
        label={t('预约时间')}
        placeholder={t('请选择预约时间')}
        isLink
        value={
          value?.[0] !== undefined && value?.[1] !== undefined ? (
            <span style={{ color: '#454950' }}>{value.join(' - ')} </span>
          ) : (
            ''
          )
        }
      />
    </NPicker>
  );
};

export default SelectAppointmentTimeByPicker;
