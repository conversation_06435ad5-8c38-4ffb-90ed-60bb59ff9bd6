import { useState } from 'react';
// import './style.less';
import { t } from '@shein-bbl/react';
import moment from 'moment';
import { Cell, DatetimePicker, Drawer } from 'shineout-mobile';
type IProps = {
  value: string;
  onChange: (value: number) => void;
};
/**
 * @description SelectAppointmentDate
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const SelectAppointmentDate: React.FC<IProps> = ({ value, onChange }) => {
  const [visible, setVisible] = useState(false);

  return (
    <>
      <Cell
        label={t('预约日期')}
        align="right"
        isLink
        onClick={() => {
          setVisible(true);
          console.log('visible', visible);
          if (value === '' || value === undefined || value === null) {
            console.log('onClick.value', value);
            onChange?.(moment().valueOf());
          }
        }}
        placeholder={t('请选择预约日期')}
        value={value ? <span style={{ color: '#454950' }}>{value}</span> : ''}
      />
      <Drawer position="bottom" visible={visible} onCancel={() => setVisible(false)}>
        <DatetimePicker
          minDate={new Date()}
          maxDate={moment().add(3, 'months').toDate()}
          type="date"
          visible={visible}
          value={value ? new Date(value.replace(/-/g, '/')) : new Date()}
          renderItem={(type, num) => {
            return `${num}${{ year: t('年'), month: t('月'), date: t('日') }[type]}`;
          }}
          onChange={(val) => {
            onChange?.(val as any);
          }}
          onOk={(val) => {
            onChange?.(val as any);
            setVisible(false);
          }}
          onCancel={() => setVisible(false)}
        />
      </Drawer>
    </>
  );
};

export default SelectAppointmentDate;
