import React, { ForwardRefRenderFunction, useState } from 'react';
import { t } from '@shein-bbl/react';
import { Drawer } from 'shineout-mobile';
import styles from './style.less';
export type IAgreementRef = {
  open: () => void;
};

/**
 * @description SafeAgreementDrawer
 * @param {unknown} _props desc
 * @param {unknown} ref desc
 * @returns {unknown} desc
 */
const SafeAgreementDrawer: ForwardRefRenderFunction<IAgreementRef> = (_props, ref) => {
  const [visible, setVisible] = useState(false);
  /**
   * @description handleOpen
   * @returns {unknown} desc
   */
  const handleOpen = () => {
    setVisible(true);
  };

  React.useImperativeHandle(ref, () => ({
    open: handleOpen,
  }));

  return (
    <Drawer
      maskCloseAble={false}
      closeable
      visible={visible}
      onClose={() => setVisible(false)}
      className={styles.container}
    >
      <div className={styles.title}>{t('SHEIN访客安全管理须知')}</div>
      <div className={styles.content}>
        {t(
          '欢迎您的来访！为加强公司安全管理,规范来访人员(以下简称“您”或“访客”)的来访秩序,保障公司的信息、财产及人员安全,维护公司正常经营生产,特此告知。',
        )}
        <b>
          {t(
            '请您审慎阅读下述内容,特别是以斜体/粗体/下划线标识的条款,您应重点阅读,并在充分理解并同意后再开始使用访客系统服务与访问SHEIN办公场所。',
          )}
        </b>
        <p>{t('一、相关定义')}</p>
        <b>{t('1.来访人员：')}</b>
        {t(
          '指因商务合作、应聘、设施维修、环境绿化养护等需要前往公司场所的办公人员及物业人员以外的人员,包括送样人员、运货人员、驻场项目人员等。',
        )}
        <br />
        {t('2.特殊来访人员：指公司总经办来访人员和政府部门来访人员。')}
        <br />
        {t('3.工作人员：指公司办公人员及物业人员,包括被访人员、普通员工、安保人员等。')}
        <br />
        {t('4.被访人员(部门)：指接受来访及发出邀请的公司办公人员(部门)。')}
        <br />
        {t('5.电子访客证：指访客通过相关访客预约登记手续生成的访客码/访客短信等凭证内容。')}
        <br />
        {t('6.通行证：指来访人员在访问时间内用于出入物流园区的有效证件,包括访客证、临时工作证等。')}
        <br />
        <p>{t('二、车辆出入')}</p>
        {t('1.请按照现场工作人员指引出入物流园区,必要时请配合车辆的安全检查。')}
        <br />
        {t(
          '2.请在指定区域停放车辆,切勿占用人行通道、消防通道及其他可能影响生产与安全的道路或区域。',
        )}
        <br />
        {t('3.请按照物流园区的各类道路标志行使并关注其他车辆动态,非必要时不得鸣笛。')}
        <p>{t('三、来访管理')}</p>
        {t('1.请提供真实有效的信息')}
        <br />
        {t(
          '2.请您凭借电子访客证等证明至物流园区门岗防损处领取实物通行证,并等候被访人员一并开展访问工作。',
        )}
        <br />
        {t('3.请妥善保管通行证(除特殊来访人员外),并在访问时间内全程佩戴通行证。')}
        <br />
        {t('4.请于访问结束时,将通行证归还至被访人员或防损人员。')}
        <br />
        <p>{t('四、行为准则')}</p>
        {t(
          '1.请在访问前配合安全检查(如有),禁止携带任何管制刀具、化学品等危险易燃物品进入办公场所,一经发现我们有权视具体情况作出移交公安机关等处理。',
        )}
        <br />
        {t(
          '2.请谨慎保管您的随身物品,一旦发生丢失,您需自行承担责任,但您可联系工作人员积极配合您找回物品。',
        )}
        <br />
        {t('3.请跟随被访人员的引领,切勿在园区内部随意走动。')}
        <br />
        {t('4.请遵守基本的办公行为规范,禁止大声喧哗,保持办公环境整洁。')}
        <br />
        {t('5.请依照工作人员指引,根据各生成区域的要求,遵守安全规定,穿戴防护用品。')}
        <br />
        <b>{t('6.未经被访人员许可：')}</b>
        {t('(1)不得进入与本工作无关的场所和重点区域；')}
        <br />
        {t('(2)不得拍照、录音、录像等；')}
        <br />
        {t('(3)不得借用公司工作人员的工牌；')}
        <br />
        {t('(4)不得触摸或操作任何设备。')}
        <br />
        {t('7.请勿在办公场所内吸烟或饮食,必要时应移步至指定区域。')}
        <br />
        {t('8.如遇紧急情况,请听从工作人员的指挥。')}
        <br />
        {t('9.其他未尽事宜应以工作人员现场告知和各办公场所的告示牌、标准规范等为准。')}
        <br />
        <p>{t('五、安全')}</p>
        {t('1.请您确保自身安全并对自己的行为负责,如您需要帮助,请及时向工作人员或他人反馈；')}
        <br />
        {t('2.如遇火灾、公共卫生、安全等危机事件,请听从工作人员指挥有序离场或开展救助工作。')}
        <br />
        {t(
          '3.为履行维护公共财产与人身安全等法定义务,我们会在办公场所布设闭路电视摄像头(即CCTV),或在部分区域安置门禁等设备,请您注意您的仪容仪表与行为举止,避免造成不良影响。',
        )}{' '}
        <br />
        <p>{t('六、保密义务')}</p>
        {t(
          '您在访问过程中,有可能通过口头交流、书面记录或视觉观察的方式获得我们不为公众所知悉的信息(简称“保密信息”),包括但不限于:1.技术信息,例如技术诀窍、工艺、生产技巧、过程和策略、发明或研究项目等；2.经营信息,例如关于成本、利润、定价、市场、销售、供应商、客户、员工、未来发展计划、未来产品计划、营销计划或策略的信息等；3.与员工、参与者、承包商或代理有关的个人信息；4.其他任何具有类似性质的非公开信息。',
        )}
        <b>
          {t(
            '我们的物流园区内部不允许拍摄、录音或录像。如您因特殊情况需要拍摄、录音或录像的,请您提前获得被访人员的明确同意。          您清楚,相关保密信息仅在双方磋商、合作所必须的最小范围内使用,未经我们书面同意,请勿以任何方式对外披露。',
          )}
        </b>
        <p>{t('七、声明')}</p>
        {t(
          '本须知经您勾选同意后发生法律效力,一旦您开始使用访客系统服务或访问SHEIN办公场所,即代表您已认真仔细阅读并理解本须知中各项条款,请您务必严格遵守上述内容,避免因此给您个人或给我司及关联方造成损失,否则您应自行承当相应责任。        欢迎您对我们的工作进行监督并提出改进意见(公众号:园区知IN)。期待您的来访！',
        )}
      </div>
    </Drawer>
  );
};

export default React.forwardRef(SafeAgreementDrawer);
