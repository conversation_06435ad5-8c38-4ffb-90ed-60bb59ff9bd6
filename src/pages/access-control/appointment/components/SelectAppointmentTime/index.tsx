import { useRef, useState } from 'react';
import { t } from '@shein-bbl/react';
import { <PERSON>, Checkbox, Drawer, Slider, Toast } from 'shineout-mobile';
import style from './style.less';

type IProps = {
  value: [string, string];
  onChange: (v: [string, string]) => void;
};

export const TimeRangeMap = {
  0: '00:00',
  1: '06:00',
  2: '09:00',
  3: '12:00',
  4: '15:00',
  5: '18:00',
  6: '21:00',
  7: '23:59',
};

/**
 * @description getTimeRangeMapTagBtnData
 * @returns {unknown} desc
 */
const getTimeRangeMapTagBtnData = () => {
  const TimeRangeMapTagBtnData = [];

  for (let i = 0; Object.keys(TimeRangeMap).length - 1 > i; i++) {
    TimeRangeMapTagBtnData.push({
      id: `${i}-${i + 1}`,
      start: i,
      end: i + 1,
      name: `${TimeRangeMap[i]}-${TimeRangeMap[i + 1]}`,
    });
  }

  console.log(TimeRangeMapTagBtnData);
  return TimeRangeMapTagBtnData;
};

/**
 * @description getValueKey
 * @param {unknown} value desc
 * @returns {unknown} desc
 */
const getValueKey = (value: [string, string]) => {
  const returnValue = [];
  Object.entries(TimeRangeMap).forEach(([key, val]) => {
    if (val === value[0]) {
      returnValue[0] = key;
    }
    if (val === value[1]) {
      returnValue[1] = key;
    }
  });
  return returnValue;
};

const SelectAppointmentTime: React.FC<IProps> = ({ value, onChange }) => {
  const [visible, setVisible] = useState(false);
  const [tagValue, setTagValue] = useState<number[]>([]);
  const valueRef = useRef(value);

  /**
   * @description setValue
   * @param {unknown} val desc
   * @returns {unknown} desc
   */
  const setValue = (val) => {
    onChange?.(val);
    valueRef.current = val;
  };

  /**
   * @description transformToSetValue
   * @param {unknown} val desc
   * @returns {unknown} desc
   */
  const transformToSetValue = (val) => {
    const min = Math.min(...val);
    const max = Math.max(...val);
    if (val.length === 0) {
      setValue([undefined, undefined]);
    } else {
      setValue([TimeRangeMap?.[min], TimeRangeMap?.[max + 1]]);
    }
  };

  /**
   * @description transformToSetTagValue
   * @param {unknown} val desc
   * @returns {unknown} desc
   */
  const transformToSetTagValue = (val) => {
    const min = Math.min(...getValueKey(val));
    const max = Math.max(...getValueKey(val));
    const tValue = Array.from({ length: max - min }, (_, i) => i + min);
    setTagValue(tValue);
  };

  return (
    <>
      <Cell
        label={t('预约时间')}
        align="right"
        isLink
        onClick={() => {
          setVisible(true);
          if (
            Object.values(TimeRangeMap).includes(value[0]) &&
            Object.values(TimeRangeMap).includes(value[1])
          ) {
            transformToSetTagValue(value);
          } else {
            // Toast.info(t('时间范围选择异常，重置为默认时间'));
            setValue([TimeRangeMap[1], TimeRangeMap[2]]);
            transformToSetTagValue([TimeRangeMap[1], TimeRangeMap[2]]);
          }
        }}
        placeholder={t('请选择预约时间')}
        value={value[0] !== undefined && value[1] !== undefined ? value.join(' - ') : ''}
      />
      <Drawer
        visible={visible}
        position="bottom"
        closeable
        onClose={() => {
          setVisible(false);
        }}
        className={style.drawer}
      >
        <div style={{ marginLeft: 20, marginTop: 40, fontSize: 16, fontWeight: 500 }}>
          {t('当前选取的时间: ')}{' '}
          <span
            style={{
              display: 'inline-block',
              marginLeft: 10,
            }}
          >
            {value[0] !== undefined && value[1] !== undefined ? value.join(' - ') : ''}
          </span>
        </div>
        <div
          style={{
            padding: '0 15px',
          }}
        >
          <Slider
            autoHideResult
            range
            scale={[0, 1, 2, 3, 4, 5, 6, 7]}
            trackHeight={10}
            value={getValueKey(value)}
            formatValue={(v) => TimeRangeMap[v]}
            formatScale={(v) => TimeRangeMap[v]}
            onChange={(val) => {
              console.log('slider.onchange.val', val);
              if (val[0] === val[1]) {
                Toast.info(t('开始时间不能等于结束时间'));
                setValue(valueRef.current);
              } else {
                setValue([TimeRangeMap[Number(val[0])], TimeRangeMap[Number(val[1])]]);
                transformToSetTagValue([
                  TimeRangeMap[Number(val[0])],
                  TimeRangeMap[Number(val[1])],
                ]);
              }
            }}
          />
        </div>
        <div>
          <Checkbox.Group
            style={{ padding: '8px 15px', overflowY: 'scroll', maxHeight: 200 }}
            mode="tag"
            inline
            keygen={(item) => item.start}
            format={(item) => item.start}
            data={getTimeRangeMapTagBtnData()}
            renderItem={(item) => item.name}
            disabled={(item) => {
              if (tagValue.length !== 0) {
                const max = Math.max(...tagValue);
                const min = Math.min(...tagValue);
                if (max !== 6 && item.start === max + 1) {
                  return false;
                } else if (min !== 0 && item.start === min - 1) {
                  return false;
                } else if (item.start === min || item.start === max) {
                  return false;
                } else {
                  return true;
                }
              } else {
                return false;
              }
            }}
            value={tagValue}
            onChange={(val) => {
              console.log('onchange.val', val);
              if (val.length === 0) {
                Toast.info(t('至少选择一个时间段'));
              } else {
                setTagValue(val);
                transformToSetValue(val);
              }
            }}
          />
        </div>
      </Drawer>
    </>
  );
};

export default SelectAppointmentTime;
