import { useEffect, useState } from 'react';
import { t } from '@shein-bbl/react';
import { Button, Field } from 'shineout-mobile';
import { saveFlowDataInfo } from '../services';
import styles from '../style.less';
/**
 * @description StepTwo
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const StepTwo = ({ currentSteps, setCurrentSteps, data, setData, setStarting }) => {
  const [loading, setLoading] = useState(false);
  const [disabledBtn, setDisabledBtn] = useState(false);

  useEffect(() => {
    if (data?.visitorDto?.visitorName && data?.visitorDto?.visitorCompany) {
      setDisabledBtn(false);
    } else {
      setDisabledBtn(true);
    }
  }, [data?.visitorDto?.visitorName, data?.visitorDto?.visitorCompany]);

  return (
    <div className={styles.step2}>
      <div>
        <Field
          label={t('姓名')}
          placeholder={t('请输入真实姓名')}
          align="right"
          value={data?.visitorDto?.visitorName}
          maxLength={64}
          minLength={1}
          onChange={(e) => {
            setData({
              ...data,
              visitorDto: {
                ...data?.visitorDto,
                visitorName: e.target.value,
              },
            });
          }}
        />
        <Field
          label={t('车牌号')}
          placeholder={t('若开车进入,请务必输入车牌号')}
          maxLength={10}
          align="right"
          value={data?.visitorDto?.visitorLicensePlate}
          onChange={(e) => {
            setData({
              ...data,
              visitorDto: {
                ...data?.visitorDto,
                visitorLicensePlate: e.target.value,
              },
            });
          }}
        />
        <Field
          label={t('公司名称')}
          placeholder={t('请输入您的公司名称')}
          maxLength={30}
          minLength={1}
          align="right"
          value={data?.visitorDto?.visitorCompany}
          onChange={(e) => {
            setData({
              ...data,
              visitorDto: {
                ...data?.visitorDto,
                visitorCompany: e.target.value,
              },
            });
          }}
        />
      </div>
      <div className={styles.submitBox}>
        <Button
          round
          style={{ width: '80%', marginRight: '10%', marginLeft: '10%' }}
          type="primary"
          disabled={disabledBtn}
          onClick={() => {
            setLoading(true);
            saveFlowDataInfo({
              visitorDto: {},
              formAppointmentInfoDto: {},
              ...data,
              needValidate: true,
            })
              .then((res) => {
                setCurrentSteps(currentSteps + 1);
                setData(res);
                setStarting(true);
              })
              .finally(() => {
                setLoading(false);
              });
          }}
          loading={loading}
        >
          {t('下一步')}
        </Button>
      </div>
    </div>
  );
};

export default StepTwo;
