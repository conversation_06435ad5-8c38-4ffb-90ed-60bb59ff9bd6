import { MutableRefObject, useEffect, useRef, useState } from 'react';

interface IUseTakePhotoByH5Props {
  /**
   * 是否降级处理
   */
  isNoSupportH5: boolean;
  getImg: (setFaceImage, setX, setY) => null | { base64Img: string };
  openMedia: (setIsPreview) => void;
  closeMedia: () => void;
  mediaOpening: boolean;
  cameraVideoRef: MutableRefObject<HTMLVideoElement>;
  cameraCanvasRef: MutableRefObject<HTMLCanvasElement>;
}

const useTakePhotoByH5 = (width, height): IUseTakePhotoByH5Props => {
  const cameraVideoRef = useRef(null);
  const cameraCanvasRef = useRef(null);
  const [mediaOpening, setMediaOpening] = useState(false);
  const [isNoSupportH5, setIsNoSupportH5] = useState(false);

  useEffect(() => {
    if (navigator?.mediaDevices?.getUserMedia === undefined) {
      setIsNoSupportH5(true);
    }
  }, []);

  function successFunc(mediaStream: MediaStream): void {
    const video = cameraVideoRef.current;
    // 旧的浏览器可能没有srcObject
    if (video === null) {
      setIsNoSupportH5(true);
      return;
    }
    if ('srcObject' in video) {
      video.srcObject = mediaStream;
    }
    video.onloadedmetadata = () => {
      video.play();
    };
  }

  /**
   * @description errorFunc
   * @param {error} err 错误信息
   * @returns {void} 无
   */
  function errorFunc(err: Error): void {
    console.log(`getUserMedia.error:${err.name}: ${err.message}`);
    setIsNoSupportH5(true);
  }

  // 启动摄像头
  const openMedia = (setIsPreview) => {
    setMediaOpening(true);
    setIsPreview(false);
    // 打开摄像头
    const opt = {
      audio: false,
      video: {},
    };
    try {
      if (navigator?.mediaDevices?.getUserMedia === undefined) {
        setIsNoSupportH5(true);
        return;
      }
      navigator?.mediaDevices?.getUserMedia(opt).then(successFunc).catch(errorFunc);
      setMediaOpening(true);
    } catch (_error) {
      setIsNoSupportH5(true);
      setMediaOpening(false);
    }
  };

  /**
   * 关闭摄像头
   * @returns
   */
  const closeMedia = () => {
    const video = cameraVideoRef.current;
    const stream = video?.srcObject;
    if (!stream) return;
    if ('getTracks' in stream) {
      const tracks = stream.getTracks();
      tracks.forEach((track) => {
        track.stop();
      });
      setMediaOpening(false);
    }
  };

  /**
   * 截图当前视频帧
   * @param setFaceImage
   * @returns
   */
  const getImg = (setFaceImage, setX, setY): null | { base64Img: string } => {
    // 获取图片资源
    const video = cameraVideoRef.current;
    const canvas = cameraCanvasRef.current;

    if (canvas === null) {
      return null;
    }

    // 宽图
    if (video.videoWidth > video.videoHeight) {
      setX(0);
      setY((height - (video.videoHeight * width) / video.videoWidth) / 2);
    }
    // 长图
    else {
      setY(0);
      setX((width - (video.videoWidth * height) / video.videoHeight) / 2);
    }
    const ctx = canvas.getContext('2d');
    // 把视频中的一帧在canvas画布里面绘制出来
    ctx.drawImage(video, 0, 0, video.width, video.height);
    // 将图片资源转成字符串
    const imgStr = canvas.toDataURL();
    // 将图片资源转成base64格式
    const base64Img = imgStr.split(';base64,').pop();
    const imgData = {
      base64Img,
    };
    // 获取到图片之后可以自动关闭摄像头
    closeMedia();
    setMediaOpening(false);
    setFaceImage(imgStr);
    return imgData;
  };

  return {
    isNoSupportH5,
    getImg,
    openMedia,
    cameraCanvasRef,
    cameraVideoRef,
    mediaOpening,
    closeMedia,
  };
};

export default useTakePhotoByH5;
