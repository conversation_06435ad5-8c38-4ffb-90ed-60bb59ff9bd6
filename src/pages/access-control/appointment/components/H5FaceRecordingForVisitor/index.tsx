import { forwardRef, ForwardRefRenderFunction, useImperativeHandle, useState } from 'react';
import DemoAvatar from '@/_/assets/images/access-control/avatar1.png';
import CircleDash from '@/_/assets/images/access-control/circle-dash.png';
import { base64ToFile } from '@/pages/access-control/utils';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { Button, Dialog, Toast } from 'shineout-mobile';
import useWxSdk from '../../hooks/useWxSdk';
import { detectFace } from '../../services';
import useTakePhotoByH5 from './hooks/useTakePhotoByH5';
import styles from './style.less';

export type IRef = {
  close: () => void;
  open: (img?: string) => void;
};

export type IProps = {
  handleSureFace: (res: any) => void;
};

/**
 * 访客端人脸录入
 * 运行环境：微信H5
 * @returns
 */
const H5FaceRecordingForVisitor: ForwardRefRenderFunction<IRef, IProps> = (
  { handleSureFace },
  ref,
) => {
  const [faceImage, setFaceImage] = useState<string>('');
  const width = window.innerWidth;
  const height = window.innerHeight * 0.7;
  const [loading, setLoading] = useState<boolean>(false);
  const [isPreview, setIsPreview] = useState<boolean>(false);

  const {
    isNoSupportH5,
    getImg,
    openMedia,
    cameraCanvasRef,
    cameraVideoRef,
    mediaOpening,
    closeMedia,
  } = useTakePhotoByH5(width, height);

  const { chooseImage, isNoSupportWxSdk } = useWxSdk();

  const [x, setX] = useState<number>(0);
  const [y, setY] = useState<number>(0);

  useImperativeHandle(ref, () => ({
    close: () => {
      closeMedia();
      setFaceImage('');
    },
    open: (img) => {
      // if (img !== '' && img !== undefined) {
      //   console.log('img', img);
      //   const imgObj = new Image();
      //   imgObj.src = img;
      //   imgObj.onload = () => {
      //     console.log('iw', imgObj.width, imgObj.height);
      //     console.log('sw', width, height);
      //     const imageWidth = imgObj.width;
      //     const imageHeight = imgObj.height;
      //     const canvas = cameraCanvasRef.current;
      //     // 宽图
      //     if (imageWidth > imageHeight) {
      //       setX(0);
      //       setY((height - (imageHeight * width) / imageWidth) / 2);
      //     }
      //     // 长图
      //     else {
      //       setY(0);
      //       setX((width - (imageWidth * height) / imageHeight) / 2);
      //     }
      //     const ctx = canvas.getContext('2d');
      //     ctx.drawImage(imgObj, 0, 0, width, height);
      //     setFaceImage(img);
      //   };
      // }
      if (img !== '' && img !== undefined) {
        setIsPreview(true);
        setFaceImage(img);
      }
    },
  }));

  /**
   * @description handleTakePhotoClick
   * @returns {unknown} desc
   */
  const handleTakePhotoClick = () => {
    openMedia(setIsPreview);
  };

  const handleTakePhotoClickByWx = usePersistFn(() => {
    chooseImage(setFaceImage);
  });

  /**
   * @description handleReTakePhotoClick
   * @returns {unknown} desc
   */
  const handleReTakePhotoClick = () => {
    setFaceImage('');
    openMedia(setIsPreview);
  };

  const handleGetImg = usePersistFn(() => {
    getImg(setFaceImage, setX, setY);
  });

  const handleSubmit = usePersistFn(() => {
    setLoading(true);
    const imageFileObj = base64ToFile(faceImage, t('人脸图片'), 'faceFile');
    detectFace(imageFileObj)
      .then((res) => {
        console.log('res', res);
        handleSureFace(res);
      })
      .catch((err) => {
        if (err.code === '*********') {
          Dialog.confirm({
            zIndex: 99,
            title: t('校验失败'),
            message: t('录入失败, 请调整拍照姿势, 再试一次'),
            onOk: () => handleTakePhotoClick(),
          });
        } else {
          Toast.info(err.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  });

  if (isNoSupportH5 && isNoSupportWxSdk) {
    Toast.info(t('设备不支持，请更换设备录入'));
    return null;
  }

  return (
    <div className={styles.container}>
      <div className={styles.circleMark}>
        <div className={styles.hole}></div>
        <img className={styles.circleDash} src={CircleDash} />
      </div>
      <div className={styles.content}>
        {faceImage !== '' && isPreview === true ? (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              width,
              height,
            }}
          >
            <img
              style={{
                transform: 'rotateY(180deg)',
                // maxWidth: width,
                maxWidth: '100%',
                // maxHeight: height,
                maxHeight: '100%',
                objectFit: 'contain',
              }}
              src={faceImage}
            />
          </div>
        ) : (
          <>
            <div
              style={{
                height,
                width,
                overflow: 'hidden',
                ...(!mediaOpening && {
                  position: 'absolute',
                  top: -10000,
                }),
              }}
            >
              <video
                muted
                autoPlay
                playsInline
                className={mediaOpening ? styles.cameraVideoHide : undefined}
                height={height}
                width={width}
                style={{
                  height: '100%',
                  transform: 'rotateY(180deg)',
                }}
                ref={cameraVideoRef}
              />
            </div>
            <div
              style={{
                width,
                height,
                backgroundSize: 'cover',
                backgroundPosition: 'center center',
                ...(mediaOpening && {
                  position: 'absolute',
                  top: -10000,
                }),
              }}
            >
              <canvas
                ref={cameraCanvasRef}
                width={width}
                height={height}
                style={{
                  width,
                  height,
                  padding: `${y}px ${x}px`,
                  transform: 'rotateY(180deg)',
                  ...(mediaOpening && {
                    position: 'absolute',
                    top: -10000,
                  }),
                }}
              />
            </div>
          </>
        )}
        <div className={styles.infoBox}>
          <div className={styles.standard}>
            <img className={styles.avatar} src={DemoAvatar} />
            <div className={styles.text}>
              <p>{t('注意事项')}</p>
              <li>{t('左边为标准照片')}</li>
              <li>{t('人脸端正')}</li>
              <li>{t('避免角度倾斜，尽量露出双耳')}</li>
              <li>{t('如照片不规范，将无法进出')}</li>
            </div>
          </div>
          {isNoSupportH5 === true && isNoSupportWxSdk === false ? (
            <>
              {faceImage === '' ? (
                <div className={styles.btnGroup}>
                  <Button
                    className={styles.btn}
                    type="primary"
                    onClick={() => handleTakePhotoClickByWx()}
                  >
                    {t('拍照')}
                  </Button>
                </div>
              ) : (
                <div className={styles.btnGroup}>
                  <Button
                    className={styles.btn}
                    type="primary"
                    plain
                    onClick={() => handleTakePhotoClickByWx()}
                  >
                    {t('重拍')}
                  </Button>
                  <Button
                    loading={loading}
                    className={styles.btn}
                    type="primary"
                    onClick={() => handleSubmit()}
                  >
                    {t('提交')}
                  </Button>
                </div>
              )}
            </>
          ) : (
            <>
              {faceImage === '' ? (
                <div className={styles.btnGroup}>
                  {!mediaOpening ? (
                    <Button
                      className={styles.btn}
                      type="primary"
                      onClick={() => handleTakePhotoClick()}
                    >
                      {t('开启相机')}
                    </Button>
                  ) : (
                    <Button className={styles.btn} type="primary" onClick={() => handleGetImg()}>
                      {t('拍照')}
                    </Button>
                  )}
                </div>
              ) : (
                <div className={styles.btnGroup}>
                  <Button
                    className={styles.btn}
                    type="primary"
                    plain
                    onClick={() => handleReTakePhotoClick()}
                  >
                    {t('重拍')}
                  </Button>
                  <Button
                    loading={loading}
                    disabled={isPreview === true}
                    className={styles.btn}
                    type="primary"
                    onClick={() => handleSubmit()}
                  >
                    {t('确认')}
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default forwardRef(H5FaceRecordingForVisitor);
