.container {
  :global(.sm-drawer) {
    top: 30%;
    left: 30%;
    max-height: 87vh;
    padding: 10px 15px;
    overflow: hidden;
    transform: translate(-20%, -25%);
  }

  overflow: auto;

  .title {
    width: 100%;
    height: 30px;
    margin-top: 5px;
    margin-bottom: 5px;
    font-size: 18px;
    font-weight: 500;
    line-height: 30px;
    text-align: center;
  }

  .content {
    max-height: 72vh;
    overflow: auto;
    font-size: 14px;

    .inscribe {
      font-size: 14px;
      text-align: right;
    }
  }

  .footer {
    text-align: right;

    .agreeBtn {
      margin-left: 15px;
    }
  }

  p {
    margin-top: 5px;
    margin-bottom: 5px;
    font-size: 16px;
    font-weight: 500;
  }

  h4 {
    margin-top: 8px;
    margin-bottom: 5px;
  }
}
