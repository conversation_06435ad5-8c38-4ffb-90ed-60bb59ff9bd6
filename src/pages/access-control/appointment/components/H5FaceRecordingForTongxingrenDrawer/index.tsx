import {
  forwardRef,
  ForwardRefRenderFunction,
  MutableRefObject,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import DemoAvatar from '@/_/assets/images/access-control/avatar1.png';
import CircleDash from '@/_/assets/images/access-control/circle-dash.png';
import Icon from '@/_/components/Icon';
import { IAppointmentAllInfo } from '@/pages/access-control/interfaces';
import {
  base64ToFile,
  checkTongxingrenAgreeInCookie,
  setTongxingrenAgreeInCookie,
} from '@/pages/access-control/utils';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { But<PERSON>, Dialog, Drawer, Toast, Upload } from 'shineout-mobile';
import useWxSdk from '../../hooks/useWxSdk';
import { detectFace } from '../../services';
import AgreeAgreementForAppointmentDrawer, {
  IAgreementRef,
} from '../AgreeAgreementForAppointmentDrawer';
import PageLoading from '../Loading';
import useTakePhotoByH5 from './hooks/useTakePhotoByH5';
import styles from './style.less';

export type IRef = {
  close: () => void;
  open: (img: string) => void;
  record: (index: number) => void;
};

export type IProps = {
  handleSureFace: (res: any) => void;
  currentTongxingrenIndex: number;
  data: IAppointmentAllInfo;

  setTongxingrenValue: (index: number, key: string, value: any) => void;
};

/**
 * 访客端[同行人]人脸录入
 * 运行环境：微信H5
 * @returns
 */
const H5FaceRecordingForTongxingrenDrawer: ForwardRefRenderFunction<IRef, IProps> = (
  props,
  ref,
) => {
  const [visible, setVisible] = useState(false);
  const agreeRef: MutableRefObject<IAgreementRef | null> = useRef(null);
  const { currentTongxingrenIndex, data, setTongxingrenValue } = props;

  const [faceImage, setFaceImage] = useState<string>('');
  const [uploadImage, setUploadImage] = useState<{
    faceId: number;
    url: string;
  }>();
  const { handleSureFace } = props;
  const width = window.innerWidth;
  const height = window.innerHeight * 0.7;
  const [loading, setLoading] = useState<boolean>(false);
  const {
    isNoSupportH5,
    getImg,
    openMedia,
    cameraCanvasRef,
    cameraVideoRef,
    mediaOpening,
    closeMedia,
  } = useTakePhotoByH5(width, height);
  const [uploading, setUploading] = useState<boolean>(false);

  // 正在使用H5上传图片的方式
  const [uploadingFileWay, setUploadingFileWay] = useState<boolean>();
  // 正在使用H5拍照的方式
  const [takingPhotoFileWay, setTalkingPhotoFileWay] = useState<boolean>();

  const [isPreview, setIsPreview] = useState<boolean>(false);

  const { chooseImage, isNoSupportWxSdk } = useWxSdk();

  const [x, setX] = useState<number>(0);
  const [y, setY] = useState<number>(0);

  const checkSupportForH5 = !isNoSupportH5;
  const checkSupportForWx = !isNoSupportWxSdk;

  console.log('faceImage', faceImage);

  /**
   * @description open
   * @param {unknown} currentImage desc
   * @returns {unknown} desc
   */
  const open = (currentImage) => {
    if (currentImage !== '' && currentImage !== undefined && currentImage !== null) {
      setIsPreview(true);
      setFaceImage(currentImage);
    }
  };

  /**
   * 同行人人脸录入
   */
  const record = (index) => {
    if (checkTongxingrenAgreeInCookie()) {
      setVisible(true);
      open(data?.formAppointmentInfoDto?.companionDtos?.[index]?.fileUrl);
    } else {
      agreeRef.current?.open();
    }
  };

  /**
   * 同行人人脸录入
   * 打开同协议
   */
  const agree = () => {
    setTongxingrenAgreeInCookie();
    agreeRef.current.close();
    setVisible(true);
    open(data?.formAppointmentInfoDto?.companionDtos?.[currentTongxingrenIndex]?.fileUrl);
  };

  /**
   * @description close
   * @returns {unknown} desc
   */
  const close = () => {
    setFaceImage('');
    setUploadImage(undefined);
    setUploadingFileWay(undefined);
    setUploading(false);
    setLoading(false);
    setUploadImage(undefined);
    setIsPreview(false);
    setTalkingPhotoFileWay(undefined);
    closeMedia();
    setVisible(false);
    const canvas = cameraCanvasRef.current;
    const ctx = canvas.getContext('2d');

    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  };

  useImperativeHandle(ref, () => ({
    close,
    open,
    record,
  }));

  const startChooseOrTakePhotoByWx = usePersistFn(() => {
    setIsPreview(false);
    setFaceImage('');
    chooseImage(setFaceImage);
  });

  /**
   * H5方式
   * 开始拍照
   */
  const startTakePhotoClickByCamH5 = () => {
    setIsPreview(false);
    setUploadingFileWay(false);
    setTalkingPhotoFileWay(true);
    setFaceImage('');
    openMedia();
  };

  /**
   *
   * H5方式
   * 重新开始拍照
   */
  const reTakePhotoClickByCamH5 = () => {
    startTakePhotoClickByCamH5();
  };

  /**
   * H5方式
   * 获取图片-截图
   */
  const handleGetImg = usePersistFn(() => {
    getImg(setFaceImage, setX, setY);
  });

  /**
   *  H5方式
   * 提交人脸
   */
  const submitByH5Cam = usePersistFn(() => {
    setLoading(true);
    const imageFileObj = base64ToFile(faceImage, t('人脸图片'), 'faceFile');
    detectFace(imageFileObj)
      .then((res) => {
        handleSureFace(res);
      })
      .catch((err) => {
        if (err.code === '*********') {
          Dialog.confirm({
            zIndex: 99,
            title: t('校验失败'),
            message: t('录入失败, 请调整拍照姿势, 再试一次'),
            onOk: () => startTakePhotoClickByCamH5(),
          });
        } else {
          Toast.info(err.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  });

  /**
   * 微信方式
   * 获取选中图片
   */
  const submitByWx = usePersistFn(() => {
    setIsPreview(false);
    setLoading(true);
    const imageFileObj = base64ToFile(faceImage, t('人脸图片'), 'faceFile');
    detectFace(imageFileObj)
      .then((res) => {
        handleSureFace(res);
      })
      .catch((err) => {
        if (err.code === '*********') {
          Dialog.confirm({
            zIndex: 99,
            title: t('校验失败'),
            message: t('录入失败, 请调整拍照姿势, 再试一次'),
            onOk: () => startTakePhotoClickByCamH5(),
          });
        } else {
          Toast.info(err.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  });

  /**
   * 微信方式
   * 提交人脸
   */
  const submitByH5Upload = usePersistFn(() => {
    handleSureFace(uploadImage);
  });

  const uploadByH5Button = () => {
    if (takingPhotoFileWay) {
      return null;
    }
    return (
      <Upload
        className={styles.btn}
        action="/ams/h5/appointment/detectFace"
        name="faceFile"
        renderItem="url"
        disabled={isNoSupportH5 && isNoSupportWxSdk ? false : uploading || mediaOpening}
        onCancel={(data) => {
          console.log('H5.Upload.onCancel', data);
          setUploading(false);
          setUploadingFileWay(false);
        }}
        onError={(data) => {
          console.log('H5.Upload.onError', data);
          setUploading(false);
          setUploadingFileWay(false);
        }}
        onSuccess={(data, file, dataURL) => {
          console.log('H5.Upload.onSuccess', data);
          const res = JSON.parse(data);
          if (res.code !== '0') {
            setUploading(false);
            setUploadingFileWay(false);
            Toast.info(res.msg);
            return {};
          }
          const canvas = cameraCanvasRef.current;
          const ctx = canvas.getContext('2d');
          const img = new Image();
          img.src = dataURL;
          img.onload = () => {
            const imgWidth = img.width;
            const imgHeight = img.height;
            console.log('imgWidth', imgWidth);
            console.log('imgHeight', imgHeight);
            // 宽图
            if (imgWidth > imgHeight) {
              const newHeight = (width * imgHeight) / imgWidth;
              const y = (height - newHeight) / 2;
              setY(y);
              setX(0);
            }
            // 长图
            else {
              const newWidth = (imgWidth * height) / imgHeight;
              const x = (width - newWidth) / 2;
              setX(x);
              setY(0);
            }
            console.log('img', img);
            ctx.drawImage(img, 0, 0, width, height);
            setUploadImage(res.data);
          };
          setUploading(false);
          setUploadingFileWay(true);
          setTalkingPhotoFileWay(false);
          setIsPreview(false);

          return { name: file.name, url: dataURL };
        }}
        renderFile={() => <></>}
      >
        <Button
          icon="upload"
          className={styles.btn}
          type="primary"
          plain
          disabled={isNoSupportH5 && isNoSupportWxSdk ? false : uploading || mediaOpening}
          // onClick={(data) => {
          //   console.log('H5.Upload.Click', data);
          //   setIsPreview(false);
          // }}
        >
          {uploadingFileWay ? t('重新上传') : t('上传')}
        </Button>
      </Upload>
    );
  };

  /**
   * @description takePhotoByH5Button
   * @returns {unknown} desc
   */
  const takePhotoByH5Button = () => {
    if (faceImage === '') {
      return !mediaOpening ? (
        <Button
          className={styles.btn}
          type="primary"
          onClick={() => {
            if (uploadingFileWay) {
              submitByH5Upload();
            } else {
              startTakePhotoClickByCamH5();
            }
          }}
        >
          {uploadingFileWay ? t('确认') : t('开启相机')}
        </Button>
      ) : (
        <Button className={styles.btn} type="primary" onClick={() => handleGetImg()}>
          {t('拍照')}
        </Button>
      );
    } else {
      return (
        <>
          {!uploadingFileWay && (
            <Button
              className={styles.btn}
              type="primary"
              plain
              onClick={() => reTakePhotoClickByCamH5()}
            >
              {t('重拍')}
            </Button>
          )}
          {!isPreview &&
            (uploadingFileWay ? (
              <Button
                disabled={isPreview === true}
                loading={loading}
                className={styles.btn}
                type="primary"
                onClick={() => submitByH5Upload()}
              >
                {t('确认')}
              </Button>
            ) : (
              <Button
                disabled={isPreview === true}
                loading={loading}
                className={styles.btn}
                type="primary"
                onClick={() => submitByH5Cam()}
              >
                {t('确认')}
              </Button>
            ))}
        </>
      );
    }
  };

  const renderButtonGroup = () => {
    // 支持H5拍照 ｜ 支持微信上传
    if (checkSupportForH5 && checkSupportForWx) {
      return (
        <div className={styles.btnGroup}>
          {uploadByH5Button()}
          {takePhotoByH5Button()}
        </div>
      );
    }
    // 支持H5拍照 ｜ 不支持微信上传
    else if (checkSupportForH5 && !checkSupportForWx) {
      return (
        <div className={styles.btnGroup}>
          {uploadByH5Button()}
          {takePhotoByH5Button()}
        </div>
      );
    }
    // 不支持H5拍照 ｜ 支持微信上传
    else if (!checkSupportForH5 && checkSupportForWx) {
      return faceImage === '' ? (
        <div className={styles.btnGroup}>
          {uploadByH5Button()}
          <Button
            className={styles.btn}
            type="primary"
            onClick={() => startChooseOrTakePhotoByWx()}
          >
            {t('拍照')}
          </Button>
        </div>
      ) : (
        <div className={styles.btnGroup}>
          <Button
            className={styles.btn}
            type="primary"
            plain
            onClick={() => startChooseOrTakePhotoByWx()}
          >
            {t('重拍')}
          </Button>
          <Button
            loading={loading}
            className={styles.btn}
            type="primary"
            onClick={() => submitByWx()}
          >
            {t('提交')}
          </Button>
        </div>
      );
    }
    // 不支持H5拍照 ｜ 不支持微信上传
    else {
      return <div className={styles.btnGroup}>{uploadByH5Button()}</div>;
    }
  };

  return (
    <>
      <Drawer visible={visible} overlay>
        <div style={{ width: '100vw', height: '100vh' }}>
          <div style={{ position: 'absolute', right: '2rem', top: '2rem', zIndex: 11 }}>
            <Icon
              name="close"
              color="white"
              fontSize="2rem"
              onClick={() => {
                setVisible(false);
                close();
              }}
            ></Icon>
          </div>

          <div className={styles.container}>
            <PageLoading loading={loading} />
            <div className={styles.circleMark}>
              <div className={styles.hole}></div>
              <img className={styles.circleDash} src={CircleDash} />
            </div>
            <div className={styles.content}>
              {isPreview === true && (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width,
                    height,
                  }}
                >
                  <img
                    style={{
                      transform: 'rotateY(180deg)',
                      // maxWidth: width,
                      maxWidth: '100%',
                      // maxHeight: height,
                      maxHeight: '100%',
                      objectFit: 'contain',
                    }}
                    src={faceImage}
                  />
                </div>
              )}
              <div
                style={{
                  height,
                  width,
                  display: isPreview ? 'none' : 'block',
                  overflow: 'hidden',
                  ...(!mediaOpening && {
                    position: 'absolute',
                    top: -10000,
                  }),
                }}
              >
                <video
                  muted
                  autoPlay
                  playsInline
                  className={mediaOpening ? styles.cameraVideoHide : undefined}
                  height={height}
                  width={width}
                  style={{
                    height: '100%',
                    transform: 'rotateY(180deg)',
                  }}
                  ref={cameraVideoRef}
                />
              </div>
              <div
                style={{
                  width,
                  height,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center center',
                  display: isPreview ? 'none' : 'block',
                  ...(mediaOpening && {
                    position: 'absolute',
                    top: -10000,
                  }),
                }}
              >
                <canvas
                  ref={cameraCanvasRef}
                  width={width}
                  height={height}
                  style={{
                    height,
                    width,
                    padding: `${y}px ${x}px`,
                    transform: 'rotateY(180deg)',
                    ...(mediaOpening
                      ? {
                          position: 'absolute',
                          top: -10000,
                        }
                      : {}),
                  }}
                />
              </div>
              <div className={styles.infoBox}>
                <div className={styles.standard}>
                  <img className={styles.avatar} src={DemoAvatar} />
                  <div className={styles.text}>
                    <p>{t('注意事项')}</p>
                    <li>{t('左边为标准照片')}</li>
                    <li>{t('人脸端正')}</li>
                    <li>{t('避免角度倾斜，尽量露出双耳')}</li>
                    <li>{t('如照片不规范，将无法进出')}</li>
                  </div>
                </div>
                {renderButtonGroup()}
              </div>
            </div>
          </div>
        </div>
      </Drawer>

      {/* 同行人-人脸协议 */}
      <AgreeAgreementForAppointmentDrawer
        agree={() => agree()}
        onClose={() => {
          setTongxingrenValue(currentTongxingrenIndex, 'needEnterStash', false);
        }}
        ref={agreeRef}
      />
    </>
  );
};

export default forwardRef(H5FaceRecordingForTongxingrenDrawer);
