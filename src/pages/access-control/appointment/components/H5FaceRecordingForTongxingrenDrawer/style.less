/* stylelint-disable max-nesting-depth */
.container {
  width: 100%;
  height: 100%;
  // overflow-x: hidden;
  background-color: white;

  :global(.sm-upload) {
    display: block;
  }

  .circleMark {
    position: absolute;
    top: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
    text-align: centers;
    background: linear-gradient(transparent, transparent);

    .circleDash {
      position: absolute;
      top: 6rem;
      left: calc(50% - 18rem);
      z-index: 5;
      width: 36rem;
      height: 36rem;
    }

    .hole {
      position: absolute;
      top: 7rem;
      left: calc(50% - 17rem);
      z-index: -1;
      width: 34rem;
      height: 34rem;
      border-radius: 50%;
      box-shadow: 0 0 0 9999px rgb(0 0 0 / 75%);
    }
  }

  .content {
    position: absolute;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    // overflow-x: hidden;
    // overflow-y: scroll;

    .cameraVideo {
      width: 100%;
      height: 60%;
    }

    .cameraVideoHidden {
      width: 100%;
      height: 70%;
    }

    .cameraCanvas {
      width: 100%;
      height: 70%;
    }

    .cameraCanvasHidden {
      width: 100%;
      height: 70%;
    }

    .avatarBg {
      z-index: 1;
      display: block;
      width: 100%;
      height: 100%;
      flex-grow: 1;
      // max-height: 70%;
    }

    .infoBox {
      position: relative;
      bottom: 0;
      z-index: 3;
      width: 100%;
      height: 30%;
      padding: 3rem 4rem 5%;
      background-color: black;

      .standard {
        display: flex;

        .avatar {
          max-width: 10rem;
          max-height: 10rem;
        }

        .text {
          padding-left: 2rem;
          font-size: 1rem;
          color: white;
          text-decoration: dotted underline;
          text-decoration-color: white;

          p {
            padding-bottom: 5px;
            font-size: 1.5rem;
            font-weight: 500;
          }

          li {
            overflow: hidden;
            line-height: 2rem;
          }
        }
      }

      .btnGroup {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-top: 2rem;

        .btn {
          width: 100%;
          height: 4rem;
        }
      }
    }
  }
}
