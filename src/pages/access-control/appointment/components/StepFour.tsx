import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { t } from '@shein-bbl/react';
import { Button, Cell, Dialog, Field, Tag, Toast } from 'shineout-mobile';
import VisitorInfoCard from '../../appointment-record/detail/components/VisitorInfoCard';
import { IAppointmentAllInfo } from '../../interfaces';
import { getLastStepInfo, submitAppointment } from '../services';
import styles from '../style.less';
import { CurrentStepsEnum } from '../typings';

// const isInOptions = [
//   { value: true, name: t('是') },
//   { value: false, name: t('否') },
// ];

const StepFour: React.FC<{
  data: IAppointmentAllInfo;
  setData: any;
  currentSteps: CurrentStepsEnum;
  setCurrentSteps: any;
}> = ({ data, setData, currentSteps, setCurrentSteps }) => {
  const [loading, setLoading] = useState(false);
  const navigator = useNavigate();
  /**
   * @description handleSubmit
   * @returns {unknown} desc
   */
  const handleSubmit = () => {
    Dialog.confirm({
      zIndex: 99,
      title: t('系统提示'),
      message: t('提交申请后信息将不可修改，是否确认提交。'),
      coveringClose: false,
      onOk: () => {
        setLoading(true);
        submitAppointment()
          .then(() => {
            navigator('/access-control/home');
            Toast.success(t('提交成功'));
          })
          .catch()
          .finally(() => {
            setLoading(false);
          });
      },
    });
  };

  /**
   * @description handleLastStep
   * @returns {unknown} desc
   */
  const handleLastStep = () => {
    getLastStepInfo().then((res) => {
      setData(res);
      setCurrentSteps(currentSteps - 1);
    });
  };

  return (
    <>
      <div className={styles.step4}>
        <div className={styles.infoShow}>
          {/* <Field
            label={t('被访问人姓名')}
            value={data?.formAppointmentInfoDto?.appointmentEnName}
            placeholder={t('请输入被访人英文名(如有)')}
            align="right"
            readonly
          /> */}
          <Field
            label={t('被访人工号')}
            value={data?.formAppointmentInfoDto?.appointmentWorkNum}
            placeholder={t('请输入被访问人工号')}
            align="right"
            readonly
          />
          <Cell
            label={t('开始时间')}
            value={
              <span style={{ color: '#454950' }}>
                {data?.formAppointmentInfoDto?.appointmentBeginTime}
              </span>
            }
            align="right"
            placeholder={t('请选择开始时间')}
            readonly
          />
          <Cell
            label={t('结束时间')}
            value={
              <span style={{ color: '#454950' }}>
                {data?.formAppointmentInfoDto?.appointmentEndTime}
              </span>
            }
            align="right"
            placeholder={t('请选择结束时间')}
            readonly
          />
          <Cell
            label={t('访问园区')}
            value={(data?.formAppointmentInfoDto?.appointmentParks || []).map((item) => {
              return (
                <Tag key={item.id} color="#F4F5F8" textColor="#35383D" style={{ margin: 2 }}>
                  {item.parkName}
                </Tag>
              );
            })}
            align="right"
            placeholder={t('可多选')}
            readonly
          />
          <Field
            value={data?.formAppointmentInfoDto?.appointmentReason}
            label={t('来访事由')}
            placeholder={t('请输入您的来访事由')}
            align="right"
            type="textarea"
            row={3}
            readonly
          />
        </div>
        <div className={styles.tongxingrenBox}>
          <div
            style={{
              fontSize: '1.6rem',
              marginLeft: '0px 5px',
              marginBottom: '1rem',
              fontWeight: 'bold',
              color: '#35383d',
              borderTop: 'rgb(244, 245, 248) solid 1px',
              paddingTop: '12px',
            }}
          >
            {t('访客信息')}
          </div>
          <VisitorInfoCard
            avatar={data?.formAppointmentInfoDto?.fileUrl}
            intoWarehouse={data?.formAppointmentInfoDto?.needEnterStash}
            username={data?.visitorDto?.visitorName}
            phone={data?.formAppointmentInfoDto?.visitorPhone}
            company={data?.visitorDto?.visitorCompany}
            carNumber={data?.visitorDto?.visitorLicensePlate}
            isSelf={true}
            tagName={t('本人')}
            style={{
              backgroundColor: '#F4F5F8',
            }}
          />
          {(data?.formAppointmentInfoDto?.companionDtos || []).map((item, index) => {
            return (
              <VisitorInfoCard
                key={item?.faceId || item._hash}
                avatar={item?.fileUrl}
                intoWarehouse={item.needEnterStash}
                username={item?.companionName}
                phone={item?.companionPhone}
                carNumber={item?.visitorLicensePlate}
                company={data?.visitorDto?.visitorCompany}
                isSelf={false}
                tagName={t('同行人') + (index + 1)}
                style={{
                  backgroundColor: '#F4F5F8',
                }}
              />
            );
          })}
        </div>
      </div>

      <div className={styles.footerSubmitBox}>
        <Button
          round
          plain
          type="primary"
          style={{ width: '40%', marginRight: '5%', marginLeft: '5%' }}
          onClick={() => {
            handleLastStep();
          }}
        >
          {t('上一步')}
        </Button>
        <Button
          round
          style={{ width: '40%', marginRight: '5%', marginLeft: '5%' }}
          type="primary"
          onClick={() => {
            handleSubmit();
          }}
          loading={loading}
        >
          {t('提交申请')}
        </Button>
      </div>
    </>
  );
};

export default StepFour;
