import React from 'react';
import Icon from '@/_/components/Icon';
import { t } from '@shein-bbl/react';

/**
 * @description NoFacePlaceholder
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const NoFacePlaceholder: React.FC<{
  style?: React.CSSProperties;
  iconStyle?: React.CSSProperties;
  onClick?: () => void;
}> = ({ style, iconStyle, onClick }) => {
  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: '#F4F5F8',
        textAlign: 'center',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: '4px',
        ...style,
      }}
      onClick={() => {
        onClick?.();
      }}
    >
      <Icon
        name="camera"
        fontSize={30}
        color="#CCCFD7"
        style={{ marginBottom: '5px', ...iconStyle }}
      />
      <span style={{ color: '#666C7C', fontSize: '11px' }}>{t('人脸录入')}</span>
    </div>
  );
};

export default NoFacePlaceholder;
