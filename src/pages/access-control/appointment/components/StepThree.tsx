import React, { MutableRefObject, useEffect, useRef } from 'react';
import Icon from '@/_/components/Icon';
// import NoFaceImg from '@/_/assets/images/access-control/no-face.png';
import { formatDate } from '@/utils';
import { t } from '@shein-bbl/react';
import { useMount } from '@shein-lego/use';
import { uniqueId } from 'lodash';
import moment from 'moment';
import {
  Button,
  Cell,
  Checkbox,
  DatetimePicker,
  Drawer,
  Field,
  Radio,
  Tag,
  Toast,
} from 'shineout-mobile';
import { IAppointmentParks } from '../../home/<USER>';
import {
  EAllowCarry,
  IAppointmentAllInfo,
  ICompanionDtosItem,
  IFormAppointmentInfoDto,
} from '../../interfaces';
import { checkVisitorAgreeInCookie, setVisitorAgreeInCookie } from '../../utils';
import { getLastStepInfo, saveFlowDataInfo, selectParks, selectVisitorFaceInfo } from '../services';
import styles from '../style.less';
import { CurrentStepsEnum } from '../typings';
import AgreeAgreementForAppointmentDrawer, {
  IAgreementRef,
} from './AgreeAgreementForAppointmentDrawer';
import H5FaceRecordingForTongxingrenDrawer, { IRef } from './H5FaceRecordingForTongxingrenDrawer';
import H5FaceRecordingForVisitor from './H5FaceRecordingForVisitor';
import NoFacePlaceholder from './NoFacePlaceholder';
import SelectAppointmentDate from './SelectAppointmentDate';
import SelectAppointmentTimeByPicker from './SelectAppointmentTimeByPicker';

const isInOptions = [
  { value: true, name: t('是') },
  { value: false, name: t('否') },
];

const StepThree: React.FC<{
  data: IAppointmentAllInfo;
  setData: any;
  MAX_TONGXINGREN_NUM: number;
  currentSteps: CurrentStepsEnum;
  setCurrentSteps: any;
}> = ({ data, setData, MAX_TONGXINGREN_NUM, currentSteps, setCurrentSteps }) => {
  const [startTimeVisible, setStartTimeVisible] = React.useState(false);
  const [endTimeVisible, setEndTimeVisible] = React.useState(false);
  const [parkSelectMultiVisible, setParkSelectMultiVisible] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const tongxingrenDrawerRef: MutableRefObject<IRef | null> = useRef(null);
  const visitorCamRef: MutableRefObject<IRef | null> = useRef(null);
  const agreementForVisitorRef: MutableRefObject<IAgreementRef | null> = useRef(null);
  const [visitorFaceRecordVisible, setVisitorFaceRecordVisible] = React.useState(false);

  const [currentTongxingrenIndex, setCurrentTongxingrenIndex] = React.useState<number>();
  const [parkOptions, setParkOptions] = React.useState<IAppointmentParks>([]);

  const [tempAppointmentDate, setTempAppointmentDate] = React.useState<string>(undefined);
  const [tempAppointmentTime, setTempAppointmentTime] = React.useState<[string, string]>([
    undefined,
    undefined,
  ]);
  useEffect(() => {
    if (
      data?.formAppointmentInfoDto?.appointmentBeginTime !== undefined &&
      data?.formAppointmentInfoDto?.appointmentBeginTime !== '' &&
      data?.formAppointmentInfoDto?.appointmentBeginTime !== null
    ) {
      setTempAppointmentDate(formatDate(data?.formAppointmentInfoDto?.appointmentBeginTime));
      setTempAppointmentTime([
        moment(data?.formAppointmentInfoDto?.appointmentBeginTime).format('HH:mm'),
        moment(data?.formAppointmentInfoDto?.appointmentEndTime).format('HH:mm'),
      ]);
    }
    // if (data?.formAppointmentInfoDto?.appointmentBeginTime === undefined) {
    //   setTempAppointmentDate(undefined);
    // }
  }, [
    data?.formAppointmentInfoDto?.appointmentBeginTime,
    data?.formAppointmentInfoDto?.appointmentEndTime,
  ]);

  useMount(() => {
    selectParks().then((res) => {
      setParkOptions(res);
    });
  });

  /**
   * @description setFormAppointmentInfoDto
   * @param {unknown} key desc
   * @param {unknown} value desc
   * @returns {unknown} desc
   */
  const setFormAppointmentInfoDto = <K extends keyof IFormAppointmentInfoDto>(key: K, value) => {
    setData({
      ...data,
      formAppointmentInfoDto: {
        ...data?.formAppointmentInfoDto,
        [key]: value,
      },
    });
  };

  /**
   * @description setFormAppointmentInfoDtoTime
   * @param {unknown} start desc
   * @param {unknown} end desc
   * @returns {unknown} desc
   */
  const setFormAppointmentInfoDtoTime = (start, end) => {
    setData({
      ...data,
      formAppointmentInfoDto: {
        ...data?.formAppointmentInfoDto,
        appointmentBeginTime: start,
        appointmentEndTime: end,
      },
    });
  };

  /**
   * @description setFormAppointmentInfoDtoObj
   * @param {unknown} obj desc
   * @returns {unknown} desc
   */
  const setFormAppointmentInfoDtoObj = <K extends keyof IFormAppointmentInfoDto>(
    obj: Record<K, any>,
  ) => {
    setData({
      ...data,
      formAppointmentInfoDto: {
        ...data?.formAppointmentInfoDto,
        ...obj,
      },
    });
  };

  /**
   * @description setNeedEnterStash
   * @param {unknown} val desc
   * @returns {unknown} desc
   */
  const setNeedEnterStash = (val: boolean) => {
    setData({
      ...data,
      formAppointmentInfoDto: {
        ...data?.formAppointmentInfoDto,
        needEnterStash: val === data?.formAppointmentInfoDto?.needEnterStash ? undefined : val,
        carryPhone: val ? data?.formAppointmentInfoDto?.carryPhone : undefined,
        carryComputer: val ? data?.formAppointmentInfoDto?.carryComputer : undefined,
      },
    });
  };

  /**
   * @description addTongxingren
   * @returns {unknown} desc
   */
  const addTongxingren = () => {
    setFormAppointmentInfoDto('companionDtos', [
      ...(data?.formAppointmentInfoDto?.companionDtos || []),
      {
        _hash: uniqueId(),
        companionName: '',
        companionPhone: '',
        visitorLicensePlate: '',
        needEnterStash: false,
      },
    ]);
  };

  /**
   * @description removeTongxingren
   * @param {unknown} index desc
   * @returns {unknown} desc
   */
  const removeTongxingren = (index) => {
    setFormAppointmentInfoDto(
      'companionDtos',
      (data?.formAppointmentInfoDto?.companionDtos || []).filter((_, i) => i !== index),
    );
  };

  /**
   * @description setTongxingrenValue
   * @param {unknown} i desc
   * @param {unknown} k desc
   * @param {unknown} v desc
   * @returns {unknown} desc
   */
  const setTongxingrenValue = <K extends keyof ICompanionDtosItem>(i: number, k: K, v: any) => {
    setFormAppointmentInfoDto(
      'companionDtos',
      (data?.formAppointmentInfoDto?.companionDtos || []).map((item, index) => {
        if (i === index) {
          return {
            ...item,
            [k]: v,
          };
        } else {
          return item;
        }
      }),
    );
  };

  /**
   * @description handleSubmit
   * @returns {unknown} desc
   */
  const handleSubmit = () => {
    setLoading(true);
    saveFlowDataInfo({
      ...data,
      needValidate: true,
    })
      .then((res) => {
        setCurrentSteps(currentSteps + 1);
        setData(res);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  /**
   * @description handleLastStep
   * @returns {unknown} desc
   */
  const handleLastStep = () => {
    getLastStepInfo().then((res) => {
      setData(res);
      setCurrentSteps(currentSteps - 1);
    });
  };

  /**
   * @description handleDeleteVisitorFace
   * @returns {unknown} desc
   */
  const handleDeleteVisitorFace = () => {
    setFormAppointmentInfoDtoObj({
      faceId: undefined,
      fileUrl: undefined,
    });
  };

  /**
   * @description handleDeleteTongxingrenFace
   * @returns {unknown} desc
   */
  const handleDeleteTongxingrenFace = () => {
    setFormAppointmentInfoDto(
      'companionDtos',
      (data?.formAppointmentInfoDto?.companionDtos || []).map((item, i) => {
        if (i === currentTongxingrenIndex) {
          return {
            ...item,
            faceId: undefined,
            fileUrl: undefined,
          };
        } else {
          return item;
        }
      }),
    );
  };

  /**
   * 访客人脸录入
   */
  const handleRecordFaceForVisitor = () => {
    if (checkVisitorAgreeInCookie()) {
      setVisitorFaceRecordVisible(true);
      visitorCamRef.current.open(data?.formAppointmentInfoDto?.fileUrl);
    } else {
      agreementForVisitorRef.current.open();
    }
  };

  /**
   * 访客人脸录入
   */
  const handleAgreeAgreement = () => {
    agreementForVisitorRef.current.close();
    setVisitorFaceRecordVisible(true);
    visitorCamRef.current.open(data?.formAppointmentInfoDto?.fileUrl);
    setVisitorAgreeInCookie();
  };

  /**
   * 访客人脸录入
   * 确认后回调函数
   */
  const handleVisitorFaceSure = (faceObj: { faceId: number; url: string }) => {
    setFormAppointmentInfoDtoObj({
      faceId: faceObj.faceId,
      fileUrl: faceObj.url,
    });
    setVisitorFaceRecordVisible(false);
    visitorCamRef.current.close();
  };

  /**
   * 同行人人脸录入
   * 确认回调函数
   * @param faceObj
   */
  const handleTongxingrenFaceSure = (faceObj: { faceId: number; url: string }) => {
    setFormAppointmentInfoDto(
      'companionDtos',
      (data?.formAppointmentInfoDto?.companionDtos || []).map((item, i) => {
        if (i === currentTongxingrenIndex) {
          return {
            ...item,
            faceId: faceObj.faceId,
            fileUrl: faceObj.url,
          };
        } else {
          return item;
        }
      }),
    );
    setCurrentTongxingrenIndex(null);
    tongxingrenDrawerRef.current.close();
  };

  useEffect(() => {
    if (
      data?.formAppointmentInfoDto?.needEnterStash &&
      (data?.formAppointmentInfoDto?.faceId === null ||
        data?.formAppointmentInfoDto?.faceId === undefined)
    ) {
      selectVisitorFaceInfo().then((res) => {
        setFormAppointmentInfoDtoObj({
          faceId: res?.faceId,
          fileUrl: res?.fileUrl,
        });
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.formAppointmentInfoDto?.needEnterStash]);

  return (
    <>
      <div className={styles.step3}>
        {/* 访客信息 */}
        <div style={{ padding: '0 15px' }}>
          {/* <Field
            noCompatibleScroll={true}
            label={t('被访问人姓名')}
            placeholder={t('请输入被访人英文名(如有)')}
            align="right"
            maxLength={64}
            value={data?.formAppointmentInfoDto?.appointmentEnName}
            onChange={(e) => {
              setFormAppointmentInfoDto('appointmentEnName', e.target.value);
            }}
          /> */}
          <Field
            noCompatibleScroll={true}
            type="number"
            label={t('被访人工号')}
            placeholder={t('请输入被访问人工号')}
            align="right"
            value={data?.formAppointmentInfoDto?.appointmentWorkNum}
            maxLength={8}
            onChange={(e) => {
              setFormAppointmentInfoDto('appointmentWorkNum', e.target.value);
            }}
          />
          <SelectAppointmentDate
            value={tempAppointmentDate}
            onChange={(value) => {
              setTempAppointmentDate(formatDate(value));
              if (
                tempAppointmentTime[0] !== undefined &&
                tempAppointmentTime[0] !== null &&
                tempAppointmentTime[1] !== undefined &&
                tempAppointmentTime[1] !== null
              ) {
                setFormAppointmentInfoDtoTime(
                  formatDate(value) + ' ' + tempAppointmentTime[0] + ':00',
                  formatDate(value) + ' ' + tempAppointmentTime[1] + ':00',
                );
              } else {
                setFormAppointmentInfoDtoTime(undefined, undefined);
              }
            }}
          />
          <SelectAppointmentTimeByPicker
            value={tempAppointmentTime}
            date={tempAppointmentDate}
            onChange={(value) => {
              setTempAppointmentTime(value);
              if (tempAppointmentDate !== undefined && tempAppointmentDate !== null) {
                setFormAppointmentInfoDtoTime(
                  formatDate(tempAppointmentDate) + ' ' + value[0] + ':00',
                  formatDate(tempAppointmentDate) + ' ' + value[1] + ':00',
                );
              } else {
                setFormAppointmentInfoDtoTime(undefined, undefined);
              }
            }}
          />

          <Cell
            label={t('访问园区')}
            align="right"
            isLink
            onClick={() => setParkSelectMultiVisible(true)}
            placeholder={t('可多选')}
            value={
              (data?.formAppointmentInfoDto?.appointmentParks || []).length === 0 ? (
                ''
              ) : (
                <>
                  {(data?.formAppointmentInfoDto?.appointmentParks || []).map((item) => {
                    return (
                      <Tag key={item.id} color="#F4F5F8" textColor="#35383D" style={{ margin: 2 }}>
                        {item?.parkName}
                      </Tag>
                    );
                  })}
                </>
              )
            }
          />
          <Field
            noCompatibleScroll={true}
            label={t('来访事由')}
            placeholder={t('请输入您的来访事由')}
            align="right"
            maxLength={30}
            minLines={1}
            value={data?.formAppointmentInfoDto?.appointmentReason}
            onChange={(e) => {
              setFormAppointmentInfoDto('appointmentReason', e.target.value);
            }}
          />
          <Cell
            label={<div>{t('是否进入库区')}</div>}
            align="right"
            value={
              <Radio.Group
                inline
                data={isInOptions}
                value={data?.formAppointmentInfoDto?.needEnterStash}
                format={(item) => item.value}
                renderItem={(item) => item.name}
                keygen="name"
                onChange={(item: any) => {
                  setNeedEnterStash(item.value);
                }}
              />
            }
          />
          {data?.formAppointmentInfoDto?.needEnterStash && (
            <Cell
              label=""
              value={
                <div className="flex items-center">
                  <Checkbox
                    label={t('携带“手机”进入库区')}
                    value={data?.formAppointmentInfoDto?.carryPhone === EAllowCarry.YES}
                    onChange={(bool) =>
                      setFormAppointmentInfoDto(
                        'carryPhone',
                        bool ? EAllowCarry.YES : EAllowCarry.NO,
                      )
                    }
                    shape="square"
                    style={{ marginRight: 18 }}
                  />
                  <Checkbox
                    label={t('携带“电脑”进入库区')}
                    value={data?.formAppointmentInfoDto?.carryComputer === EAllowCarry.YES}
                    onChange={(bool) =>
                      setFormAppointmentInfoDto(
                        'carryComputer',
                        bool ? EAllowCarry.YES : EAllowCarry.NO,
                      )
                    }
                    shape="square"
                  />
                </div>
              }
            />
          )}
          {data?.formAppointmentInfoDto?.needEnterStash && (
            <Cell
              label={t('人脸录入')}
              align="right"
              value={
                data?.formAppointmentInfoDto?.fileUrl ? (
                  <div style={{ position: 'relative', height: 110 }}>
                    <img
                      style={{
                        width: 100,
                        height: 100,
                        // objectFit: 'cover',
                        position: 'absolute',
                        transform: 'rotateY(180deg)',
                        top: 5,
                        right: 0,
                        display: 'block',
                        borderRadius: '4px',
                      }}
                      onClick={() => {
                        handleRecordFaceForVisitor();
                      }}
                      src={data?.formAppointmentInfoDto?.fileUrl}
                    />
                    <Icon
                      name="pc-close-opaque-multic1"
                      style={{ fontSize: 26, position: 'absolute', top: -5, right: -10 }}
                      onClick={() => {
                        handleDeleteVisitorFace();
                      }}
                    />
                  </div>
                ) : (
                  <NoFacePlaceholder
                    style={{ width: 100, height: 100, float: 'right', fontSize: 12 }}
                    iconStyle={{ fontSize: 24 }}
                    onClick={() => {
                      if (checkVisitorAgreeInCookie()) {
                        setVisitorFaceRecordVisible(true);
                      } else {
                        agreementForVisitorRef.current.open();
                      }
                    }}
                  />
                )
              }
            />
          )}

          {(data?.formAppointmentInfoDto?.companionDtos || [])?.length === 0 && (
            <Cell
              label={t('同行人(若有)')}
              align="right"
              value={
                <Button text type="primary" onClick={() => addTongxingren()}>
                  {t('添加')}
                </Button>
              }
            />
          )}
        </div>

        {/* 同行人信息 */}
        <div
          style={{
            backgroundColor: '#F4F5F8',
            paddingTop: (data?.formAppointmentInfoDto?.companionDtos || []).length > 0 ? 10 : 0,
            paddingBottom: (data?.formAppointmentInfoDto?.companionDtos || []).length > 0 ? 10 : 0,
          }}
        >
          {(data?.formAppointmentInfoDto?.companionDtos || []).map((_item, index) => {
            return (
              // eslint-disable-next-line react/no-array-index-key
              <div className={styles.tongxingrenContainer} key={index}>
                <div className={styles.itemHeader}>
                  <div className={styles.title}>
                    {t('同行人')}
                    {index + 1}
                  </div>
                  <div className={styles.icon} onClick={() => removeTongxingren(index)}>
                    <Icon name="pc-close" fontSize={20} color={'#999DA8'} />
                  </div>
                </div>
                <Field
                  noCompatibleScroll={true}
                  label={t('姓名')}
                  placeholder={t('请输入姓名')}
                  align="right"
                  maxLength={64}
                  value={(data?.formAppointmentInfoDto?.companionDtos || [])?.[index].companionName}
                  onChange={(e) => {
                    setTongxingrenValue(index, 'companionName', e.target.value);
                  }}
                />
                <Field
                  noCompatibleScroll={true}
                  label={t('手机号')}
                  placeholder={t('请输入手机号')}
                  maxLength={11}
                  align="right"
                  value={
                    (data?.formAppointmentInfoDto?.companionDtos || [])?.[index].companionPhone
                  }
                  onChange={(e) => {
                    setTongxingrenValue(index, 'companionPhone', e.target.value);
                  }}
                />
                <Field
                  noCompatibleScroll={true}
                  label={t('车牌号')}
                  placeholder={t('请输入车牌号')}
                  maxLength={10}
                  align="right"
                  value={
                    (data?.formAppointmentInfoDto?.companionDtos || [])?.[index].visitorLicensePlate
                  }
                  onChange={(e) => {
                    setTongxingrenValue(index, 'visitorLicensePlate', e.target.value);
                  }}
                />
                <Cell
                  label={<div>{t('是否进入库区')}</div>}
                  align="right"
                  value={
                    <Radio.Group
                      inline
                      data={isInOptions}
                      value={data?.formAppointmentInfoDto?.companionDtos?.[index].needEnterStash}
                      format={(item) => item.value}
                      renderItem={(item) => item.name}
                      keygen
                      onChange={(e: any) => {
                        setTongxingrenValue(
                          index,
                          'needEnterStash',
                          e.value ===
                            (data?.formAppointmentInfoDto?.companionDtos || [])?.[index]
                              .needEnterStash
                            ? undefined
                            : e.value,
                        );
                      }}
                    />
                  }
                />
                {data?.formAppointmentInfoDto?.companionDtos?.[index].needEnterStash && (
                  <Cell
                    label={t('人脸录入')}
                    align="right"
                    value={
                      data?.formAppointmentInfoDto?.companionDtos?.[index]?.fileUrl ? (
                        <div style={{ position: 'relative', height: 90 }}>
                          <img
                            style={{
                              width: 80,
                              height: 80,
                              // objectFit: 'cover',
                              position: 'absolute',
                              top: 5,
                              transform: 'rotateY(180deg)',
                              right: 0,
                              display: 'block',
                              borderRadius: '4px',
                              backgroundSize: 'cover',
                              backgroundRepeat: 'no-repeat',
                            }}
                            onClick={() => {
                              setCurrentTongxingrenIndex(index);
                              tongxingrenDrawerRef.current.record(index);
                            }}
                            src={data?.formAppointmentInfoDto?.companionDtos?.[index]?.fileUrl}
                          />
                          <Icon
                            name="pc-close-opaque-multic1"
                            style={{ fontSize: 26, position: 'absolute', top: -5, right: -10 }}
                            onClick={() => {
                              setCurrentTongxingrenIndex(index);
                              handleDeleteTongxingrenFace();
                            }}
                          />
                        </div>
                      ) : (
                        <NoFacePlaceholder
                          style={{ width: 80, height: 80, float: 'right' }}
                          onClick={() => {
                            setCurrentTongxingrenIndex(index);
                            tongxingrenDrawerRef.current.record(index);
                          }}
                        />
                      )
                    }
                  />
                )}
              </div>
            );
          })}
        </div>

        {/* 新增同行人按钮 */}
        {(data?.formAppointmentInfoDto?.companionDtos || [])?.length > 0 &&
          (data?.formAppointmentInfoDto?.companionDtos || [])?.length < MAX_TONGXINGREN_NUM && (
            <div className={styles.singleAddButton}>
              <Button type="primary" text onClick={() => addTongxingren()}>
                {t('新增同行人')}
              </Button>
            </div>
          )}
        {/* 选择访问的园区 */}
        <Drawer
          visible={parkSelectMultiVisible}
          position="bottom"
          closeable
          onClose={() => {
            setParkSelectMultiVisible(false);
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              fontWeight: 500,
              marginBottom: 20,
              marginTop: 20,
              fontSize: 16,
            }}
          >
            {t('选择访问的园区')}
          </div>
          <Checkbox.Group
            style={{ padding: '8px 25px', overflowY: 'scroll', maxHeight: 200 }}
            mode="tag"
            inline
            keygen={(item) => item.id}
            format={(item) => item.id}
            data={(parkOptions || []).map((item) => ({ id: item.id, parkName: item.parkName }))}
            renderItem={(item) => item.parkName}
            onCancel={() => setParkSelectMultiVisible(false)}
            value={(data?.formAppointmentInfoDto?.appointmentParks || []).map((item) => item.id)}
            onChange={(val) => {
              if (val.length > 30) {
                Toast.info(t('园区最多选择30个'));
              } else {
                setFormAppointmentInfoDto(
                  'appointmentParks',
                  val.map((id) => (parkOptions || []).find((item) => item.id === id)),
                );
              }
            }}
          />
        </Drawer>

        <Drawer
          position="bottom"
          visible={startTimeVisible || endTimeVisible}
          onCancel={() => {
            setStartTimeVisible(false);
            setEndTimeVisible(false);
          }}
        >
          {/* 开始时间 */}
          {startTimeVisible && (
            <DatetimePicker
              type="datetime"
              visible={startTimeVisible}
              minDate={new Date()}
              maxDate={
                data?.formAppointmentInfoDto?.appointmentEndTime !== '' &&
                data?.formAppointmentInfoDto?.appointmentEndTime !== null &&
                data?.formAppointmentInfoDto?.appointmentEndTime !== undefined
                  ? new Date(data?.formAppointmentInfoDto?.appointmentEndTime)
                  : undefined
              }
              renderItem={(type, num) => {
                return `${num}${
                  { year: t('年'), month: t('月'), date: t('日'), hour: t('时'), minute: t('分') }[
                    type
                  ]
                }`;
              }}
              onOk={(val) => {
                setStartTimeVisible(false);
                setFormAppointmentInfoDto('appointmentBeginTime', formatDate(val as any, true));
              }}
              onCancel={() => setStartTimeVisible(false)}
            />
          )}
          {/* 结束时间 */}
          {endTimeVisible && (
            <DatetimePicker
              type="datetime"
              visible={endTimeVisible}
              minDate={
                data?.formAppointmentInfoDto?.appointmentBeginTime !== '' &&
                data?.formAppointmentInfoDto?.appointmentBeginTime !== null &&
                data?.formAppointmentInfoDto?.appointmentBeginTime !== undefined
                  ? new Date(data?.formAppointmentInfoDto?.appointmentBeginTime)
                  : new Date()
              }
              maxDate={
                data?.formAppointmentInfoDto?.appointmentBeginTime !== '' &&
                data?.formAppointmentInfoDto?.appointmentBeginTime !== null &&
                data?.formAppointmentInfoDto?.appointmentBeginTime !== undefined
                  ? new Date(
                      moment(data?.formAppointmentInfoDto?.appointmentBeginTime).format(
                        'YYYY-MM-DD',
                      ) + ' 23:59:59',
                    )
                  : undefined
              }
              renderItem={(type, num) => {
                return `${num}${
                  { year: t('年'), month: t('月'), date: t('日'), hour: t('时'), minute: t('分') }[
                    type
                  ]
                }`;
              }}
              onOk={(val) => {
                setEndTimeVisible(false);
                setFormAppointmentInfoDto('appointmentEndTime', formatDate(val as any, true));
              }}
              onCancel={() => setEndTimeVisible(false)}
            />
          )}
        </Drawer>

        {/* 访客-人脸协议 */}
        <AgreeAgreementForAppointmentDrawer
          onClose={() => {
            setFormAppointmentInfoDto('needEnterStash', false);
          }}
          agree={handleAgreeAgreement}
          ref={agreementForVisitorRef}
        />

        {/* 访问人脸信息录入 */}
        <Drawer visible={visitorFaceRecordVisible} overlay>
          <div style={{ width: '100vw', height: '100vh' }}>
            <div style={{ position: 'absolute', right: '2rem', top: '2rem', zIndex: 11 }}>
              <Icon
                name="close"
                color="white"
                fontSize="2rem"
                onClick={() => {
                  setVisitorFaceRecordVisible(false);
                  visitorCamRef.current.close();
                }}
              ></Icon>
            </div>
            <H5FaceRecordingForVisitor ref={visitorCamRef} handleSureFace={handleVisitorFaceSure} />
          </div>
        </Drawer>

        <H5FaceRecordingForTongxingrenDrawer
          ref={tongxingrenDrawerRef}
          handleSureFace={handleTongxingrenFaceSure}
          currentTongxingrenIndex={currentTongxingrenIndex}
          data={data}
          setTongxingrenValue={setTongxingrenValue}
        />
      </div>

      {/* 提交按钮 */}
      <div className={styles.footerSubmitBox}>
        <Button
          round
          plain
          type="primary"
          style={{ width: '40%', marginRight: '5%', marginLeft: '5%' }}
          onClick={() => {
            handleLastStep();
          }}
        >
          {t('上一步')}
        </Button>
        <Button
          round
          style={{ width: '40%', marginRight: '5%', marginLeft: '5%' }}
          type="primary"
          onClick={() => {
            handleSubmit();
          }}
          loading={loading}
        >
          {t('下一步')}
        </Button>
      </div>
    </>
  );
};

export default StepThree;
