import { t } from '@shein-bbl/react';
import { Icons, Steps } from 'shineout-mobile';
const stepNames = [t('预约手机'), t('个人信息'), t('预约信息'), t('申请确认')];
import styles from '../style.less';

export const commonStyle = {
  width: 22,
  height: 22,
  borderRadius: '50%',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  fontSize: 12,
  color: 'white',
};
export const activeStyle = {
  backgroundColor: '#56D9C1',
  color: '#fff',
};
export const inActiveStyle = {
  backgroundColor: '#A9C5E9',
  color: '#fff',
};
export const finishedStyle = {
  backgroundColor: 'rgb(86 217 193)',
  color: '#fff',
};

/**
 * @description StepBar
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const StepBar = ({ currentSteps }) => {
  return (
    <div className={styles.stepBox}>
      <Steps
        data={stepNames}
        keygen={(d) => d}
        value={stepNames[currentSteps]}
        active={({ index }) => <span style={{ ...commonStyle, ...activeStyle }}>{index + 1}</span>}
        inactive={({ index }) => (
          <span
            style={{
              ...commonStyle,
              ...inActiveStyle,
              ...(index < currentSteps ? finishedStyle : {}),
            }}
          >
            {index < currentSteps ? (
              <Icons fontSize={14} style={{ fontWeight: 'bold' }} name="success" />
            ) : (
              index + 1
            )}
          </span>
        )}
      />
    </div>
  );
};

export default StepBar;
