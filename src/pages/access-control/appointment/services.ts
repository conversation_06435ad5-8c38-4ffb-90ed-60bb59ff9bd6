import { get, post } from '@/utils';
import { omit } from 'lodash';
import { IAppointmentRecordResponse } from '../appointment-record/interfaces';
import { IAppointmentParks } from '../home/<USER>';
import { IAppointmentAllInfo, IFaceItem, ISelectVisitorResponse } from '../interfaces';
import { getFetchHeaderConfig } from '../utils';

/**
 * 查询访客信息
 * https://soapi.sheincorp.cn/application/3580/routes/169186/doc
 */
export const selectVisitor = () => {
  return get<ISelectVisitorResponse>(
    '/ams/h5/amsVisitor/selectVisitor',
    {},
    getFetchHeaderConfig(),
  );
};

/**
 * 查询预约表单信息
 * https://soapi.sheincorp.cn/application/3580/routes/169206/doc
 */
export const selectFlowData = () => {
  return get<IAppointmentAllInfo | null>('/ams/h5/appointment/selectFormData');
};

/**
 * 删除预约表单记录
 * https://soapi.sheincorp.cn/application/3580/routes/169190/doc
 * @returns
 */
export const deleteAppointmentRecord = () => {
  return post<boolean>('/ams/h5/appointment/deleteAppointmentRecord', {}, getFetchHeaderConfig());
};

/**
 * 访客人脸检测
 * https://soapi.sheincorp.cn/application/3580/routes/169212/doc
 * @returns
 */
export const detectFace = (params: { faceFile: string } | any) => {
  return post<IFaceItem>('/ams/h5/appointment/detectFace', params, {
    showErrorMsg: false,
    ...getFetchHeaderConfig(),
  });
};

/**
 * 查询上一步信息
 * https://soapi.sheincorp.cn/application/3580/routes/169215/doc
 * @param params
 * @returns
 */
export const getLastStepInfo = () => {
  return get<IAppointmentRecordResponse>(
    '/ams/h5/appointment/selectLastStepInfo',
    {},
    getFetchHeaderConfig(),
  );
};

/**
 * 保存访客预约表单信息
 * https://soapi.sheincorp.cn/application/3580/routes/169573/doc
 */
export const saveFlowDataInfo = (
  params: IAppointmentAllInfo,
  config?: {
    showErrorMsg?: boolean;
  },
) => {
  const data = {
    ...params,
    formAppointmentInfoDto: {
      ...params.formAppointmentInfoDto,
      companionDtos: (params?.formAppointmentInfoDto?.companionDtos || []).map((item) =>
        omit(item, '_hash'),
      ),
    },
  };
  return post<IAppointmentAllInfo>('/ams/h5/appointment/saveFormDataInfo', data, {
    ...getFetchHeaderConfig(),
    ...config,
  });
};

/**
 * 查询访客信息
 * https://soapi.sheincorp.cn/application/3580/routes/169186/doc
 */
export const selectFormData = () => {
  return get<IAppointmentAllInfo>('/ams/h5/appointment/selectFormData', {}, getFetchHeaderConfig());
};

/**
 * 查询门禁相关园区
 * https://soapi.sheincorp.cn/application/3694/routes/169655/doc
 */
export const selectParks = () => {
  return get<IAppointmentParks>('/ams/h5/park/selectParks', {}, getFetchHeaderConfig());
};

/**
 * 提交访客预约流程
 * https://soapi.sheincorp.cn/application/3580/routes/169213/doc
 */
export const submitAppointment = () => {
  return post<boolean>('/ams/h5/appointment/submitAppointment', {}, getFetchHeaderConfig());
};

/**
 * 查询访客人脸
 * https://soapi.sheincorp.cn/application/3580/routes/170385/doc
 */
export const selectVisitorFaceInfo = () => {
  return get<IFaceItem>('/ams/h5/amsVisitor/selectVisitorFaceInfo', {}, getFetchHeaderConfig());
};

/**
 * 检查手机号是否能发起验证码,不能会立即抛出错误
 * https://soapi.sheincorp.cn/application/3694/routes/177091/doc
 * @param params
 * @returns
 */
export const checkSms = (params: { phone: string }) => {
  return post<boolean>('/ams/h5/login/checkSms', params);
};

/**
 * 重新发起访客预约
 * https://soapi.sheincorp.cn/application/3580/routes/178691/doc
 * @param id
 * @returns
 */
export const rebook = (id: number) => {
  return get<boolean>('/ams/h5/appointment/rebook', { id });
};
