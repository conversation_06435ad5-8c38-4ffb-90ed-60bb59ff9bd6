import React from 'react';
import HeaderBannerImg from '@/_/assets/images/access-control/header-banner.png';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import LoginForm from './components/LoginForm';
import styles from './style.less';

/**
 * @description Login
 * @returns {unknown} desc
 */
const Login: React.FC = () => {
  usePageTitle(t('物流园区访客预约平台'));

  return (
    <div className={styles.container}>
      <img className={styles.headerImg} src={HeaderBannerImg} alt={t('头部底图')} />
      <div className={styles.title}>{t('物流园区访客预约平台')}</div>
      <LoginForm />
    </div>
  );
};

export default Login;
