import { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useMount } from '@shein-lego/use';
import RegisterForm from '../../appointment/components/RegisterForm';
import { phoneLogin } from '../../home/<USER>';
import { getTokenInCookie, setTokenInCookie } from '../../utils';

/**
 * @description LoginForm
 * @returns {unknown} desc
 */
const LoginForm = () => {
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const location = useLocation();
  const routeQuery = new URLSearchParams(location.search);
  const redirectUrl = routeQuery.get('redirect')
    ? `/#/access-control/${routeQuery.get('redirect')}`
    : '/#/access-control/appointment';

  useMount(() => {
    if (getTokenInCookie() !== '') {
      window.location.replace(redirectUrl);
    }
  });

  /**
   * @description handleSubmit
   * @param {unknown} phone desc
   * @param {unknown} code desc
   * @param {unknown} requestId desc
   * @returns {unknown} desc
   */
  const handleSubmit = (phone, code, requestId) => {
    setSubmitLoading(true);
    phoneLogin({
      phone,
      figureCode: code,
      requestId,
    })
      .then((res) => {
        setTokenInCookie(res);
        setSubmitLoading(false);
        window.location.replace(redirectUrl);
      })
      .finally(() => {
        setSubmitLoading(false);
      });
  };

  return (
    <>
      <RegisterForm handleSubmit={handleSubmit} submitLoading={submitLoading} />
    </>
  );
};

export default LoginForm;
