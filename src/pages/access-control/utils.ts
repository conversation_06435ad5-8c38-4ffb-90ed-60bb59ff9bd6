/**
 * 将对象属性名从驼峰式转为下划线式
 * @description 转换对象属性名格式
 * @param {Record<string, any>} obj - 需要转换的对象
 * @returns {Record<string, any>} 转换后的新对象
 */
export const objectHumpToLine = (obj: Record<string, any>): Record<string, any> => {
  const newObj = new Object();
  for (const key in obj) {
    newObj[key.replace(/([A-Z])/g, '_$1').toLowerCase()] = obj[key];
  }
  return newObj;
};

const ExpiresTime = 60 * 60 * 1000 * 24 * 7;

/**
 * 将token存入cookie
 * @description 设置accessToken到cookie中
 * @param {string} token - 需要存储的token值
 * @returns {void}
 */
export const setTokenInCookie = (token: string): void => {
  const date = new Date();
  date.setTime(date.getTime() + ExpiresTime);
  document.cookie = `accessToken=${token};expires=${date.toUTCString()};path=/`;
};

/**
 * 清除cookie中的token
 * @description 删除accessToken cookie
 * @returns {void}
 */
export const clearTokenInCookie = (): void => {
  document.cookie = `accessToken=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
};

/**
 * 从cookie中获取token
 * @description 获取accessToken cookie值
 * @returns {string} token值或空字符串
 */
export const getTokenInCookie = (): string => {
  const token = document.cookie.match(/accessToken=([^;]*)/);
  return token ? token[1] : '';
};

/**
 * 设置访客同意标记到cookie
 * @description 设置visitorAgreeFlag cookie
 * @returns {void}
 */
export const setVisitorAgreeInCookie = (): void => {
  const date = new Date();
  date.setTime(date.getTime() + 60 * 60 * 1000 * 24 * 365);
  document.cookie = `visitorAgreeFlag=${1};expires=${date.toUTCString()};path=/`;
};

/**
 * 检查访客是否已同意
 * @description 检查visitorAgreeFlag cookie值
 * @returns {boolean} 是否已同意
 */
export const checkVisitorAgreeInCookie = (): boolean => {
  const flag = document.cookie.match(/visitorAgreeFlag=([^;]*)/);
  return Number(flag?.[1]) === 1 ? true : false;
};

/**
 * 设置同行人同意标记到cookie
 * @description 设置tongxingrenAgreeFlag cookie
 * @returns {void}
 */
export const setTongxingrenAgreeInCookie = (): void => {
  const date = new Date();
  date.setTime(date.getTime() + 60 * 60 * 1000 * 24 * 365);
  document.cookie = `tongxingrenAgreeFlag=${1};expires=${date.toUTCString()};path=/`;
};

/**
 * 检查同行人是否已同意
 * @description 检查tongxingrenAgreeFlag cookie值
 * @returns {boolean} 是否已同意
 */
export const checkTongxingrenAgreeInCookie = (): boolean => {
  const flag = document.cookie.match(/tongxingrenAgreeFlag=([^;]*)/);
  return Number(flag?.[1]) === 1 ? true : false;
};

/**
 * 设置员工同意标记到cookie
 * @description 设置staffAgreeFlag cookie
 * @returns {void}
 */
export const setStaffAgreeInCookie = (): void => {
  const date = new Date();
  date.setTime(date.getTime() + 60 * 60 * 1000 * 24 * 365);
  document.cookie = `staffAgreeFlag=${1};expires=${date.toUTCString()};path=/`;
};

/**
 * 检查员工是否已同意
 * @description 检查staffAgreeFlag cookie值
 * @returns {boolean} 是否已同意
 */
export const checkStaffAgreeInCookie = (): boolean => {
  const flag = document.cookie.match(/staffAgreeFlag=([^;]*)/);
  console.log('flag', flag);
  return Number(flag?.[1]) === 1 ? true : false;
};

/**
 * 获取请求头配置
 * @description 获取包含token的请求头配置
 * @returns {{headers: {token: string}}} 请求头配置对象
 */
export const getFetchHeaderConfig = (): { headers: { token: string } } => {
  return {
    headers: {
      token: getTokenInCookie(),
    },
  };
};

/**
 * 将base64数据转换为Blob对象
 * @description 转换base64数据为Blob
 * @param {string} dataURI - base64格式的数据URI
 * @returns {Blob} 转换后的Blob对象
 */
export function dataURLtoFile(dataURI: string): Blob {
  const binary = atob(dataURI.split(',')[1]);
  const array = [];
  for (let i = 0; i < binary.length; i++) {
    array.push(binary.charCodeAt(i));
  }
  return new Blob([new Uint8Array(array)], { type: 'image/png' });
}

/**
 * 将Blob转换为FormData
 * @description 转换Blob为FormData用于上传
 * @param {Blob} fileData - 需要转换的Blob数据
 * @returns {FormData} 包含文件的FormData对象
 */
export function uploadImg(fileData: Blob): FormData {
  const formData = new FormData();
  const fileOfBlob = new File([fileData], new Date() + '.png'); // 命名图片名
  formData.append('file', fileOfBlob);
  return formData;
}

/**
 * 将base64图片转换为FormData文件
 * @description 转换base64图片为FormData文件格式
 * @param {string} data - base64格式的图片数据
 * @param {string} fileName - 生成的文件名
 * @param {string} fileKey - FormData中的字段名
 * @returns {FormData} 包含文件的FormData对象
 */
export function base64ToFile(data: string, fileName: string, fileKey: string): FormData {
  const dataArr = data.split(',');
  const byteString = atob(dataArr[1]);
  const options: any = {
    type: 'image/jpeg',
    endings: 'native',
  };
  const u8Arr = new Uint8Array(byteString.length);
  for (let i = 0; i < byteString.length; i++) {
    u8Arr[i] = byteString.charCodeAt(i);
  }
  const formData = new FormData();
  const fileOfBlob = new File([u8Arr], fileName + '.jpg', options); //返回文件流
  formData.append(fileKey, fileOfBlob);
  return formData;
}

/**
 * 将日期字符串转换为时间戳
 * @description 转换日期字符串为Unix时间戳
 * @param {string} dateStr - 日期字符串
 * @returns {number} Unix时间戳
 */
export function getUnixTime(dateStr: string): number {
  const newStr = dateStr.replace(/-/g, '/');
  const date = new Date(newStr);
  const time_str = date.getTime().toString();
  return Number(time_str.substr(0, 10));
}

/**
 * 判断是否在微信浏览器环境中
 * @description 检测当前环境是否为微信浏览器
 * @returns {boolean} 是否在微信浏览器中
 */
export function isWeiXin(): boolean {
  // 获取 User Agent
  const userAgent = navigator.userAgent.toLowerCase();

  // 判断是否在微信中打开
  if (userAgent.indexOf('micromessenger') !== -1) {
    return true;
  } else {
    return false;
  }
}

/**
 * 判断是否在企业微信环境中
 * @description 检测当前环境是否为企业微信
 * @returns {boolean} 是否在企业微信中
 */
export function isEnterpriseWechat(): boolean {
  const ua = window.navigator.userAgent.toLowerCase();
  if (ua.match(/wxwork/i)) {
    return true;
  } else {
    return false;
  }
}

/**
 * 检查当前浏览器环境
 * @description 检测当前运行环境类型
 * @returns {'EWX'|'WX'|'H5'} 环境类型(企业微信/微信/H5)
 */
export function checkCurrentBrowserEnv(): 'EWX' | 'WX' | 'H5' {
  if (isWeiXin() && isEnterpriseWechat()) {
    return 'EWX';
  } else if (isWeiXin() && !isEnterpriseWechat()) {
    return 'WX';
  } else {
    return 'H5';
  }
}
