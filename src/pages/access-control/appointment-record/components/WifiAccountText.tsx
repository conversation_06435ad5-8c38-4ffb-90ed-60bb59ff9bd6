import React, { useState } from 'react';
import { EMPTY_STR } from '@/share';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import { useRequest, useSetState } from 'ahooks';
import { Button, Loading } from 'shineout-mobile';
import { IWifiInfoResponse } from '../interfaces';
import { getWifiInfo } from '../services';
import Agreement from './Agreement';

interface IWifiAccountTextProps {
  id: number;
}

/**
 * @description WifiAccountText
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const WifiAccountText: React.FC<IWifiAccountTextProps> = (props) => {
  const { id } = props;
  const [agreementVisible, setAgreementVisible] = useState(false);
  const [state, setState] = useSetState<{
    isMasked: boolean;
    wifiInfo?: IWifiInfoResponse;
  }>({
    wifiInfo: null,
    isMasked: false,
  });

  const { loading, runAsync } = useRequest(getWifiInfo, {
    manual: true,
  });

  /**
   * @description handleGetWifiInfo
   * @returns {unknown} desc
   */
  const handleGetWifiInfo = () => {
    setAgreementVisible(false);
    runAsync({ id }).then((res) => {
      setState({ wifiInfo: res, isMasked: true });
    });
  };

  if (state.isMasked) {
    return (
      <div className="break-all mt-1">
        <span className="mr-3">
          <span className="mr-2">{t('账号')}:</span>
          <span>{state.wifiInfo?.flowNum || EMPTY_STR}</span>
        </span>
        <span className="mr-2">{t('密码')}:</span>
        <span>{state.wifiInfo?.wifiPassword || EMPTY_STR}</span>
      </div>
    );
  }

  if (loading) {
    return <Loading size={18} />;
  }

  return (
    <>
      <Button className="align-bottom" text onClick={() => setAgreementVisible(true)}>
        <Icon name="display-fill" color="#197AFA" fontSize={18} />
      </Button>
      <Agreement
        visible={agreementVisible}
        onClose={() => setAgreementVisible(false)}
        onAgree={handleGetWifiInfo}
      />
    </>
  );
};

export default WifiAccountText;
