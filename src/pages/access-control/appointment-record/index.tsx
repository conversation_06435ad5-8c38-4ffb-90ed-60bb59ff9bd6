import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ApproveFailImg from '@/_/assets/images/access-control/approve-fail.png';
import ApproveIngImg from '@/_/assets/images/access-control/approve-ing.png';
import ApproveSuccessImg from '@/_/assets/images/access-control/approve-success.png';
import Icon from '@/_/components/Icon';
import { usePageTitle } from '@/_/hooks';
import ShowRichEditorContent from '@/pages/procedure/my/detail/components/approval/ShowRichEditorContent';
import { EMPTY_STR } from '@/share';
import { t } from '@shein-bbl/react';
import { useMount } from '@shein-lego/use';
import classNames from 'classnames';
import { Button, Dialog, Tag, Toast } from 'shineout-mobile';
import { rebook, selectFlowData } from '../appointment/services';
import { EAllowCarry } from '../interfaces';
import { getVisitorPassList } from '../pass-card-list/services';
import WifiAccountText from './components/WifiAccountText';
import VisitorInfoCard from './detail/components/VisitorInfoCard';
import { IAppointmentRecordDetailResponse, IAppointmentRecordResponse } from './interfaces';
import { getAppointmentRecord, getAppointmentRecordDetail } from './services';
import styles from './style.less';
import { AppointmentFlowStatusEnum } from './typings';

/**
 * @description AppointmentRecord
 * @returns {unknown} desc
 */
const AppointmentRecord: React.FC = () => {
  usePageTitle(t('预约记录'));
  const navigator = useNavigate();
  const [data, setData] = useState<IAppointmentRecordResponse>();
  const [detail, setDetail] = useState<IAppointmentRecordDetailResponse>();
  const [currentDetailId, setCurrentDetailId] = useState<number>();
  const [loading, setLoading] = useState(false);

  useMount(() => {
    getAppointmentRecord().then((res) => {
      setData(res);
    });
  });

  /**
   * @description handleClickPass
   * @param {unknown} id desc
   * @returns {unknown} desc
   */
  const handleClickPass = (id: number) => {
    getVisitorPassList().then((res) => {
      if (
        res?.length === 0 ||
        (res || []).find((item) => item.amsAppointmentRecordId === id) === undefined
      ) {
        Toast.info(t('当前没有可用的通行证'));
      } else {
        navigator('/access-control/pass-card-list/' + id);
      }
    });
  };

  /**
   * @description handleReEdit
   * @param {unknown} id desc
   * @returns {unknown} desc
   */
  const handleReEdit = (id: number) => {
    console.log(t('重新编辑'), id);
    selectFlowData().then((res) => {
      if (res !== null) {
        Dialog.confirm({
          coveringClose: false,
          zIndex: 99,
          title: t('系统提示'),
          message: t('系统检测您有尚未完成的表单，【重新编辑】操作将会覆盖该表单，是否继续操作？'),
          onOk: () => {
            rebook(id).then((res) => {
              if (res) {
                Toast.success(t('重新创建此单据'));
                navigator('/access-control/appointment?isFromPassCardRebook=1');
              }
            });
          },
        });
      } else {
        rebook(id).then((res) => {
          if (res) {
            Toast.success(t('重新创建此单据'));
            navigator('/access-control/appointment?isFromPassCardRebook=1');
          }
        });
      }
    });
  };

  /**
   * @description handleShowDetail
   * @param {unknown} id desc
   * @returns {unknown} desc
   */
  const handleShowDetail = (id: number) => {
    setLoading(true);
    getAppointmentRecordDetail({ id })
      .then((res) => {
        setCurrentDetailId(id);
        setDetail(res);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  /**
   * @description handleCloseDetail
   * @returns {unknown} desc
   */
  const handleCloseDetail = () => {
    setCurrentDetailId(undefined);
    setDetail(undefined);
  };

  return (
    <div
      className={styles.container}
      style={{
        paddingBottom: '10px',
      }}
    >
      {(data || []).map((item) => {
        const appointmentTimeRange = `${item.appointmentBeginTime} - ${
          item.appointmentEndTime.split(' ')?.[1]
        }`;
        return (
          <div
            className={
              item.flowStatus === AppointmentFlowStatusEnum.AUDIT_FAIL
                ? classNames(styles.itemBox, styles.fail)
                : styles.itemBox
            }
            key={item.id}
          >
            <div className={styles.title}>
              <span className={styles.label}>{t('申请时间')}:</span> {item.submitAppointmentTime}
            </div>
            <div className={styles.content}>
              <div className={styles.row}>
                <span className={styles.label}>{t('预约时间')}: </span>
                {appointmentTimeRange}
              </div>
              <div className={styles.row}>
                <span className={styles.label}>{t('被访问人')}: </span>
                {item.appointmentEnName}
              </div>
              <div className={styles.row}>
                <span className={styles.label}>{t('被访人工号')}:</span>
                <span>{item.appointmentWorkNum}</span>
              </div>
              <div className={styles.parkRow}>
                <span
                  className={styles.label}
                  style={{
                    minWidth: 80,
                    marginLeft: 3,
                  }}
                >
                  {t('访问园区：')}
                </span>
                <div className={styles.parkList}>
                  {(item.appointmentParks || []).map((park) => {
                    return (
                      <Tag
                        style={{
                          margin: '0px 5px 2px 0px',
                        }}
                        color="#F4F5F8"
                        textColor="#35383D"
                        key={park.id}
                      >
                        {park.parkName}
                      </Tag>
                    );
                  })}
                </div>
              </div>
              <div className={styles.row}>
                <span className={styles.label}>{t('来访事由')}:</span> {item.appointmentReason}
              </div>
              <div className={styles.row}>
                <span className={`${styles.label} w-auto`}>{t('SHEIN-GUEST上网信息')}:</span>
                {item.flowStatus === AppointmentFlowStatusEnum.AUDIT_SUCCESS &&
                item.canConnectWifi ? (
                  <WifiAccountText id={item.id} />
                ) : (
                  <span>{EMPTY_STR}</span>
                )}
              </div>
              <div className={styles.row}>
                <span className={`${styles.label} w-auto`}>{t('是否携带手机进入库区')}:</span>
                <span>{item.carryPhone === EAllowCarry.YES ? t('是') : t('否')}</span>
              </div>
              <div className={styles.row}>
                <span className={`${styles.label} w-auto`}>{t('是否携带电脑进入库区')}:</span>
                <span>{item.carryComputer === EAllowCarry.YES ? t('是') : t('否')}</span>
              </div>
              {item.flowStatus === AppointmentFlowStatusEnum.AUDIT_FAIL && (
                <div className={styles.row}>
                  <span className={styles.label}>{t('失败原因')}:</span>
                  <div>
                    <ShowRichEditorContent richEditorString={item.rejectedReason} />
                  </div>
                </div>
              )}
            </div>
            <div className={styles.seal}>
              {item.flowStatus === AppointmentFlowStatusEnum.AUDIT_SUCCESS && (
                <img src={ApproveSuccessImg} alt={t('审核通过')} />
              )}
              {item.flowStatus === AppointmentFlowStatusEnum.AUDITING && (
                <img src={ApproveIngImg} alt={t('审核中')} />
              )}
              {item.flowStatus === AppointmentFlowStatusEnum.AUDIT_FAIL && (
                <img src={ApproveFailImg} alt={t('审核失败')} />
              )}
            </div>
            <div className={styles.detail}>
              {detail && currentDetailId === Number(item.id) && (
                <>
                  <div
                    style={{
                      color: '#35383D',
                      fontSize: '1.6rem',
                      fontWeight: 500,
                      marginBottom: 10,
                      marginLeft: 5,
                      marginTop: 16,
                    }}
                  >
                    {t('访客信息')}
                  </div>
                  <VisitorInfoCard
                    intoWarehouse={detail.needEnterStash}
                    username={detail.visitorName}
                    phone={detail.visitorPhone}
                    company={detail.visitorCompany}
                    carNumber={detail.visitorLicensePlate}
                    avatar={detail.fileUrl}
                    isSelf={true}
                    tagName={t('本人')}
                    style={{
                      border: '1px solid #eee',
                      boxShadow: '1px 1px 10px 0px #eee',
                    }}
                  />
                  {(detail.companionVos || [])?.map((visitor, index) => {
                    return (
                      <VisitorInfoCard
                        key={visitor.faceId}
                        intoWarehouse={visitor.needEnterStash}
                        username={visitor.companionName}
                        phone={visitor.companionPhone}
                        company={detail.visitorCompany}
                        carNumber={visitor.visitorLicensePlate}
                        avatar={visitor.fileUrl}
                        isSelf={false}
                        tagName={t('同行人') + (index + 1)}
                        style={{
                          border: '1px solid #eee',
                          boxShadow: '1px 1px 10px 0px #eee',
                        }}
                      />
                    );
                  })}
                </>
              )}
            </div>

            <div className={styles.footer}>
              {item.flowStatus === AppointmentFlowStatusEnum.AUDIT_FAIL && (
                <Button type="primary" text onClick={() => handleReEdit(Number(item.id))}>
                  {t('重新编辑')}
                </Button>
              )}
              {item.flowStatus === AppointmentFlowStatusEnum.AUDIT_SUCCESS && (
                <Button type="primary" text onClick={() => handleClickPass(Number(item.id))}>
                  {t('查看通行证')}
                </Button>
              )}
              {detail && currentDetailId === Number(item.id) ? (
                <Button
                  className={styles.btn}
                  type="primary"
                  text
                  onClick={() => {
                    handleCloseDetail();
                  }}
                >
                  {t('收起')}
                </Button>
              ) : (
                <Button
                  className={styles.btn}
                  type="primary"
                  text
                  loading={loading && currentDetailId === Number(item.id)}
                  onClick={() => {
                    handleCloseDetail();
                    handleShowDetail(Number(item.id));
                  }}
                >
                  {t('展开')}
                </Button>
              )}
            </div>
          </div>
        );
      })}

      {data?.length === 0 && (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: 'calc(100vh - 50px)',
            flexDirection: 'column',
          }}
        >
          <Icon name="m-empty" fontSize="4rem" color="#ccc" />
          <div style={{ marginTop: 15, color: 'gray' }}>{t('暂无预约记录')}</div>
        </div>
      )}
    </div>
  );
};

export default AppointmentRecord;
