import { useParams } from 'react-router-dom';
import ApproveSuccessImg from '@/_/assets/images/access-control/approve-success.png';
import Avatar1 from '@/_/assets/images/access-control/avatar1.png';
import Avatar2 from '@/_/assets/images/access-control/avatar2.png';
import Avatar3 from '@/_/assets/images/access-control/avatar3.png';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { Tag } from 'shineout-mobile';
import VisitorInfoCard from './components/VisitorInfoCard';
import styles from './style.less';

/**
 * @description AppointmentRecordDetail
 * @returns {unknown} desc
 */
const AppointmentRecordDetail: React.FC = () => {
  usePageTitle(t('预约记录详情'));
  const { id } = useParams();
  console.log('id', id);
  return (
    <div className={styles.container}>
      <div className={styles.title}>{t('申请时间： 2023-06-14 19:46:21')}</div>
      <div className={styles.subTitle}>{t('被访问信息')}</div>
      <div className={styles.itemBox}>
        <div className={styles.content}>
          <div className={styles.row}>{t('预约时间： 2023-06-14 19:46:21')}</div>
          <div className={styles.row}>{t('被访问人： 李四')}</div>
          <div className={styles.row}>{t('访问手机：{}', 1888888888)}</div>
          <div className={styles.row}>
            {t('访问园区：')}
            <Tag>{t('园区1')}</Tag>
            <Tag>{t('园区2')}</Tag>
            <Tag>{t('园区3')}</Tag>
          </div>
          <div className={styles.row}>{t('来访事由： 协商采购事宜')}</div>
        </div>
      </div>
      <div className={styles.seal}>
        <img src={ApproveSuccessImg} alt={t('审核通过')} />
      </div>
      <div className={styles.subTitle}>{t('访客信息')}</div>
      <div className={styles.tongxingren}>
        <VisitorInfoCard
          intoWarehouse={true}
          username={t('张三')}
          phone="1888888888"
          company={t('天河服饰有限公司')}
          carNumber={t('粤A 888888')}
          avatar={Avatar1}
          isSelf={true}
          tagName={t('同行人')}
        />
        <VisitorInfoCard
          intoWarehouse={true}
          username={t('王五')}
          phone="1888888888"
          company={t('天河服饰有限公司')}
          avatar={Avatar2}
          isSelf={false}
          tagName={t('同行人1')}
        />
        <VisitorInfoCard
          intoWarehouse={false}
          username={t('李六')}
          phone="1888888888"
          company={t('天河服饰有限公司')}
          avatar={Avatar3}
          isSelf={false}
          tagName={t('同行人2')}
        />
        <VisitorInfoCard
          intoWarehouse={false}
          username={t('李六')}
          phone="1888888888"
          company={t('天河服饰有限公司')}
          isSelf={false}
          tagName={t('同行人2')}
        />
      </div>
    </div>
  );
};

export default AppointmentRecordDetail;
