.container {
  position: relative;
  z-index: 1;
  height: 100vh;
  color: #eff2f5;

  .title {
    margin-top: 20px;
    margin-left: 20px;
    font-size: 2.5rem;
    font-weight: 500;
    color: #35383d;
  }

  .subTitle {
    margin-top: 20px;
    margin-bottom: 10px;
    margin-left: 20px;
    font-size: 2rem;
    font-weight: 500;
    color: #35383d;
  }

  .tongxingren {
    position: relative;
    margin: 0 15px;
  }

  .itemBox {
    position: relative;
    padding: 10px;
    margin: 10px 15px;
    background-color: white;
    border-radius: 1rem;

    .content {
      padding: 10px 0;
      text-align: left;

      .row {
        margin-bottom: 10px;
        font-size: 1.5rem;
        color: #454950;
      }

      .row span:first-child {
        margin-left: 3px;
      }

      .row span {
        margin-right: 8px;
      }
    }
  }

  .seal {
    position: absolute;
    top: -10px;
    right: 10px;
    z-index: 0;

    img {
      width: 120px;
    }
  }

  .fail {
    .title {
      color: #999da8 !important;
    }

    .row {
      color: #999da8 !important;
    }
  }

  .detail {
    position: relative;
  }
}
