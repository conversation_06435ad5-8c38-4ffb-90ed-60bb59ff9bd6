.container {
  position: relative;
  display: flex;
  padding: 15px 12px;
  margin-bottom: 10px;
  overflow: auto;
  overflow: hidden;
  background-color: white;
  border-radius: 1rem;
  flex-direction: row;

  .avatar {
    width: 142px;
    min-width: 142px;
    overflow: hidden;
    border-radius: 1rem;
    flex: 1;

    .img {
      width: 100%;
      pointer-events: none;
      background-size: cover;
      aspect-ratio: 1/1;
      background-position: center;
    }
  }

  .content {
    flex-grow: 1;
    padding-left: 15px;

    .large {
      font-size: 1.8rem;
      font-weight: 500;
      color: #35383d;

      .username {
        display: inline-block;
        max-width: 7rem;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: keep-all;
      }

      .tag {
        position: relative;
        top: -5px;
        margin-left: 10px;
      }
    }

    .large:first-child {
      margin-top: 5px;
      margin-bottom: 10px;
    }

    .info {
      display: flex;
      flex-direction: column;
      // height: 55px;
      justify-content: center;

      .small {
        font-size: 1.5rem;
        color: #454950;
        word-break: break-all;
      }
    }
  }
}
