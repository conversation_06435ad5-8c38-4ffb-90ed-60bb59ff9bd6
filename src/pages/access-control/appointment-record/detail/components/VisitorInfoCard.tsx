import NoFaceImg from '@/_/assets/images/access-control/no-face.png';
import { t } from '@shein-bbl/react';
import { Divider, Tag } from 'shineout-mobile';
import styles from './style.less';

type IProps = {
  intoWarehouse?: boolean;
  username: string;
  phone: string;
  company?: string;
  carNumber?: string;
  avatar?: string;
  isSelf: boolean;
  tagName: string;
  style?: React.CSSProperties;
};

/**
 * @description VisitorInfoCard
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const VisitorInfoCard: React.FC<IProps> = (props) => {
  const { avatar, username, phone, company, carNumber, isSelf, tagName, intoWarehouse, style } =
    props;
  return (
    <div className={styles.container} style={style}>
      <div className={styles.avatar}>
        <div
          className={styles.img}
          style={{
            transform:
              avatar === '' || avatar === undefined || avatar === null
                ? 'rotateY(0deg)'
                : 'rotateY(180deg)',
            backgroundImage: `url(${
              avatar === '' || avatar === undefined || avatar === null ? NoFaceImg : avatar
            })`,
          }}
        />
        <Tag
          style={{
            position: 'absolute',
            top: 15,
            left: 12,
          }}
          size="large"
          shape="mark"
          type={intoWarehouse ? 'success' : 'danger'}
        >
          {intoWarehouse ? t('进仓') : t('不进仓')}
        </Tag>
      </div>
      <div className={styles.content}>
        <div className={styles.large}>
          <span className={styles.username}>{username}</span>
          <Tag
            className={styles.tag}
            color={isSelf ? '#E3EDFA' : '#FAF2E7'}
            textColor={isSelf ? '#197AFA' : '#FFA940'}
          >
            {tagName}
          </Tag>
        </div>
        <div className={styles.large}>{phone}</div>
        <Divider style={{ margin: '10px 0 5px 0' }} />
        <div className={styles.info}>
          <div
            className={styles.small}
            style={{ marginTop: 5, marginBottom: 10, color: '#454950' }}
          >
            {carNumber === '' || carNumber === undefined ? t('暂无车牌信息') : carNumber}
          </div>
          {company ? (
            <div
              className={styles.small}
              style={{
                color: '#454950',
              }}
            >
              {company}
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};
export default VisitorInfoCard;
