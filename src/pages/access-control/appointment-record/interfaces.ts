import { EAllowCarry } from '../interfaces';
import { AppointmentFlowStatusEnum } from './typings';

interface IAppointmentParksItem {
  /** 园区id */
  id: number;
  /** 园区名称 */
  parkName: string;
}

type IAppointmentParks = IAppointmentParksItem[];

export interface ICompanionVosItem {
  /** 同行人手机号 */
  companionPhone: string;
  /** 同行人姓名 */
  companionName: string;
  /** 同行人车牌号 */
  visitorLicensePlate: string;
  /** 是否进入库区 */
  needEnterStash: boolean;
  /** 人脸id */
  faceId: number;
  /** 人脸图片url */
  fileUrl: string;
}

type ICompanionVos = ICompanionVosItem[];

export interface IAppointmentRecordDetailResponse {
  /** 访客姓名 */
  visitorName: string;
  /** 访客公司 */
  visitorCompany: string;
  /** 访客车牌号 */
  visitorLicensePlate: string;
  /** 来访开始时间 */
  appointmentBeginTime: string;
  /** 来访结束时间 */
  appointmentEndTime: string;
  /** 人脸图片url */
  fileUrl: string;
  /** 来访原因 */
  appointmentReason: string;
  /** 访问园区列表 */
  appointmentParks: IAppointmentParks;
  /** 访客手机号 */
  visitorPhone: string;
  /** 同行人列表 */
  companionVos: ICompanionVos;
  /** 申请时间 */
  submitAppointmentTime: string;
  /** 被访人工号 */
  appointmentWorkNum: string;
  /** 预约记录id */
  id: number;
  /** 被访人英文名 */
  appointmentEnName: string;
  /** 失败原因 */
  rejectedReason: string;
  /** 是否进入园区 */
  needEnterStash: boolean;
  /** 流程状态（1-审核中，2-审核失败，3-审核通过） */
  flowStatus: AppointmentFlowStatusEnum;
}

interface IAppointmentParksItem {
  /** 园区id */
  id: number;
  /** 园区名称 */
  parkName: string;
}

export interface IAppointmentRecordItem {
  /** 来访开始时间 */
  appointmentBeginTime: string;
  /** 来访结束时间 */
  appointmentEndTime: string;
  /** 被访人工号 */
  appointmentWorkNum: string;
  /** 被访人英文名 */
  appointmentEnName: string;
  /** 来访原因 */
  appointmentReason: string;
  /** 访问园区列表 */
  appointmentParks: IAppointmentParks;
  /** 访客预约记录id */
  id: number;
  /** 申请时间 */
  submitAppointmentTime: string;
  /** 流程状态（1-审核中，2-审核失败，3-审核通过） */
  flowStatus: AppointmentFlowStatusEnum;
  /** 失败原因 */
  rejectedReason: string;
  /** 是否携带手机进入库区 */
  carryPhone?: EAllowCarry;
  /** 是否携带电脑进入库区 */
  carryComputer?: EAllowCarry;
  /** 是否能上网 */
  canConnectWifi?: boolean;
}
export type IAppointmentRecordResponse = IAppointmentRecordItem[];

export interface IWifiInfoResponse {
  /** 流程编号（上网账号） */
  flowNum?: string;
  /** wifi密码 */
  wifiPassword?: string;
}
