.container {
  position: relative;
  z-index: 1;
  color: #f4f5f8;
  -webkit-overflow-scrolling: touch;

  .itemBox {
    position: relative;
    padding: 10px;
    margin: 10px 15px;
    background-color: white;
    border-radius: 1rem;

    .title {
      padding: 15px 5px;
      font-size: 1.8rem;
      font-weight: 500;
      color: #35383d;
      border-bottom: #e6eaf0 1px solid;
    }

    .content {
      padding: 15px 5px 0;
      text-align: left;

      .row {
        width: 100%;
        margin-bottom: 10px;
        font-size: 1.5rem;
        color: #454950;

        /* stylelint-disable-next-line max-nesting-depth */
        .label {
          display: inline-block;
          width: 80px;
          min-width: 80px;
        }
      }

      .row > span:first-child {
        margin-left: 3px;
      }

      .row > span {
        margin-right: 8px;
      }

      .parkRow {
        display: flex;
        margin-bottom: 10px;
        font-size: 1.5rem;
        color: #454950;

        /* stylelint-disable-next-line max-nesting-depth */
        .parkList {
          flex-grow: 1;
          flex-wrap: wrap;
          margin-left: 6px;
        }
      }
    }

    .footer {
      padding-right: 10px;
      margin: 2px 0;
      text-align: right;

      .btn {
        padding: 10px 15px;
        margin-left: 2rem;
      }
    }

    .seal {
      position: absolute;
      top: 2rem;
      right: 1.5rem;
      z-index: 0;

      img {
        width: 9rem;
      }
    }
  }

  .fail {
    .title {
      color: #999da8 !important;
    }

    .row {
      color: #999da8 !important;
    }

    .label {
      color: #999da8 !important;
    }

    .parkRow {
      color: #999da8 !important;
    }

    :global(.sm-tag) {
      color: #999da8 !important;
    }
  }
}
