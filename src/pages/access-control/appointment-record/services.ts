import { get, post } from '@/utils';
import { getFetchHeaderConfig } from '../utils';
import {
  IAppointmentRecordDetailResponse,
  IAppointmentRecordResponse,
  IWifiInfoResponse,
} from './interfaces';

/**
 * 查询预约记录明细
 * @param params
 * @returns
 * https://soapi.sheincorp.cn/application/3580/routes/169594/doc
 */
export const getAppointmentRecordDetail = (params: { id: number }) => {
  return get<IAppointmentRecordDetailResponse>(
    '/ams/h5/appointment/selectAppointmentRecordDetail?id=' + params.id,
    // params,
  );
};

/**
 *查询预约记录
 * https://soapi.sheincorp.cn/application/3580/routes/169592/doc
 */
export const getAppointmentRecord = () => {
  return post<IAppointmentRecordResponse>(
    '/ams/h5/appointment/selectAppointmentRecord',
    {},
    getFetchHeaderConfig(),
  );
};

/**
 *查询上网账号密码
 * https://soapi.sheincorp.cn/application/3694/routes/240706/doc
 */
export const getWifiInfo = (params: { id: number }) => {
  return get<IWifiInfoResponse>('/ams/h5/appointment/selectWifiInfo', params);
};
