import { useRef, useState } from 'react';
import CardImg from '@/_/assets/images/access-control/card.png';
import carryComputerImg from '@/_/assets/images/access-control/carry-computer.png';
import carryPhoneImg from '@/_/assets/images/access-control/carry-phone.png';
import { usePageTitle } from '@/_/hooks';
import EllipsisText from '@/pages/access-control/components/EllipsisText';
import { EAllowCarry } from '@/pages/access-control/interfaces';
import { EMPTY_STR } from '@/share';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';
import { isNil } from 'lodash';
import moment from 'moment';
import { Drawer } from 'shineout-mobile';
import { IPassCardProps } from '../../../interfaces';
import styles from './style.less';

/**
 * @description PassCard
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const PassCard: React.FC<IPassCardProps> = ({ data, currentDatetime, nav }) => {
  usePageTitle(t('访客通行证'));

  const beginTimeObj = moment(data.appointmentBeginTime, 'YYYY-MM-DD HH:mm:ss');
  const endTimeObj = moment(data.appointmentEndTime, 'YYYY-MM-DD HH:mm:ss');
  const reasonRef = useRef<HTMLDivElement>(null);
  /**
   * @description handleClickReason
   * @returns {unknown} desc
   */
  const handleClickReason = () => {
    if (reasonRef.current.clientWidth !== reasonRef.current.scrollWidth) {
      setMoreReasonVisible(true);
    }
  };
  const [moreReasonVisible, setMoreReasonVisible] = useState<boolean>(false);

  return (
    <div className={styles.container}>
      <div className={styles.title}>{t('SHEIN访客通行证')}</div>
      <div
        className={styles.content}
        style={{
          backgroundImage: `url(${CardImg})`,
        }}
      >
        <div className={styles.topPart}>
          <div className={styles.subTitle}>{t('通行园区')}</div>
          <div className={styles.btnGroup}>
            {(data?.appointmentParks || []).map((item) => (
              <span className={styles.parkBtn} key={item.id}>
                {item.parkName}
              </span>
            ))}
            {data.appointmentParks?.length === 0 ||
              (isNil(data.appointmentParks) && <span className={styles.parkBtn}>-</span>)}
          </div>
          <div className={classNames(styles.subTitle, styles.mt)}>{t('通行权限')}</div>
          <div className={styles.passIcons}>
            {data.carryPhone === EAllowCarry.YES && (
              <div className={styles.passIcon}>
                <img src={carryPhoneImg} alt="" />
              </div>
            )}
            {data.carryComputer === EAllowCarry.YES && (
              <div className={styles.passIcon}>
                <img src={carryComputerImg} alt="" />
              </div>
            )}
            {data.carryPhone !== EAllowCarry.YES && data.carryComputer !== EAllowCarry.YES && (
              <div className={styles.passIcon}>{EMPTY_STR}</div>
            )}
          </div>
          <div className={classNames(styles.subTitle, styles.mt)}>{t('通行时间')}</div>
          <div className={styles.carNumber}>
            {data.visitorLicensePlate?.toUpperCase() || EMPTY_STR}
          </div>
          <div className={styles.passTime}>
            <div className={styles.row}>
              <div className={styles.start}>{beginTimeObj.format('HH:mm')}</div>
              <div className={styles.duration}>
                <div className={styles.startPoint}></div>
                <div className={styles.dash}></div>
                <div className={styles.endPoint}></div>
              </div>
              <div className={styles.end}>{endTimeObj.format('HH:mm')}</div>
            </div>
            <div className={classNames(styles.row, styles.datetime)}>
              {beginTimeObj.format('YYYY/MM/DD')}
            </div>
          </div>
        </div>
        <div className={styles.bottomPart}>
          <div className={styles.column}>
            <div className={styles.guest}>
              <div className={styles.title}>{t('来访人')}</div>
              <div className={styles.item}>
                <EllipsisText text={data.visitorName} rows={1} />
              </div>
            </div>
            <div className={styles.master}>
              <div className={styles.title}>{t('被访人')}</div>
              <div className={styles.item}>
                <EllipsisText text={data.appointmentEnName} rows={1} />
              </div>
            </div>
          </div>
          <div className={styles.reason} ref={reasonRef} onClick={() => handleClickReason()}>
            {data.appointmentReason}
          </div>
          <div className={styles.tips}>{t('如需上网，请到预约记录查询账号密码')}</div>
        </div>
      </div>
      <div className={styles.time}>{currentDatetime}</div>
      {nav}
      <Drawer overlay visible={moreReasonVisible} onClose={() => setMoreReasonVisible(false)}>
        <div
          style={{
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <div
            style={{
              textAlign: 'center',
              lineHeight: '2rem',

              backgroundColor: '#fff',
              width: '50%',
              padding: 30,
              wordBreak: 'break-all',
            }}
          >
            {data.appointmentReason}
          </div>
        </div>
      </Drawer>
    </div>
  );
};

export default PassCard;
