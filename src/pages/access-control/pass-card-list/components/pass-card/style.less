/* stylelint-disable max-nesting-depth */
.container {
  position: relative;
  width: 100%;
  padding-bottom: 7rem;
  text-align: center;
  background: url('@/_/assets/images/access-control/pass-card-background.png') no-repeat center
    center;
  background-size: cover;

  .title {
    position: relative;
    padding-top: 2rem;
    font-size: 2.5rem;
    font-weight: 500;
    color: white;
    text-align: center;
  }

  .content {
    position: relative;
    top: 2rem;
    left: 4%;
    z-index: 2;
    width: 92%;
    padding: 16px;
    font-size: 1.2em;
    text-align: left;
    background-size: 100%;
    background-repeat: round;

    .topPart {
      .subTitle {
        position: relative;
        margin-bottom: 18px;
        margin-left: 5px;
        font-size: 1.5rem;
        font-weight: 500;
        color: white;
      }

      .btnGroup {
        position: relative;
        max-height: 100px;
        margin-left: 4px;
        overflow-y: auto;

        .parkBtn {
          display: inline-block;
          padding: 1rem;
          margin-top: 1rem;
          margin-right: 1rem;
          font-size: 1.6rem;
          font-weight: 500;
          color: white;
          background-color: #1d64ce;
          border-radius: 5px;
          box-shadow: inset;
        }
      }

      .btnGroup::-webkit-track {
        background: white;
        /* stylelint-disable-next-line number-leading-zero */
        opacity: 0.1;
      }

      .btnGroup::-webkit-scrollbar-thumb {
        background: #1d64ce;
      }

      .carNumber {
        margin: -30px 0 -12px;
        font-size: 2.8rem;
        font-weight: 500;
        color: white;
        text-align: center;
      }

      .passTime {
        display: flex;
        flex-direction: column;
        margin: 10px 0;

        .row {
          display: flex;
          flex-direction: row;
          text-align: center;

          .start {
            width: 80px;
            padding-right: 12px;
            font-size: 2.2rem;
            font-weight: 500;
            color: white;
          }

          .duration {
            position: relative;
            display: flex;
            font-size: 1rem;
            flex-grow: 1;
            flex-direction: row;
            align-items: center;

            .startPoint {
              position: relative;
              width: 8px;
              height: 8px;
              border: white 1px solid;
              border-radius: 50%;
            }

            .dash {
              position: relative;
              width: 100%;
              height: 0;
              border-bottom: white 1px dashed;
              flex-grow: 1;
            }

            .endPoint {
              position: relative;
              width: 8px;
              height: 8px;
              border: white 1px solid;
              border-radius: 50%;
            }
          }

          .end {
            width: 80px;
            padding-left: 12px;
            font-size: 2.2rem;
            font-weight: 500;
            color: white;
            text-align: center;
          }
        }

        .datetime {
          margin-bottom: 50px;
          font-size: 1.4rem;
          color: white;
          text-align: center;
          flex-grow: 1;
          justify-content: center;
        }
      }
    }

    .bottomPart {
      padding-right: 8px;
      padding-left: 8px;
      margin-top: 2rem;

      .column {
        display: flex;
        flex-direction: row;

        .guest {
          width: 50%;
          text-align: left;

          .title {
            margin-bottom: 10px;
            font-size: 1.2rem;
            font-weight: 500;
            color: white;
            text-align: left;
          }

          .item {
            margin-bottom: 25px;
            font-size: 1.4rem;
            font-weight: 500;
            color: white;
          }
        }

        .master {
          width: 50%;
          margin-left: 7rem;
          text-align: left;

          .title {
            margin-bottom: 10px;
            font-size: 1.2rem;
            font-weight: 500;
            color: white;
            text-align: left;
          }

          .item {
            margin-bottom: 10px;
            font-size: 1.4rem;
            font-weight: 500;
            color: white;
          }
        }
      }

      .reason {
        width: 100%;
        margin-top: -10px;
        margin-bottom: 15px;
        overflow: hidden;
        font-size: 1.4rem;
        font-weight: 500;
        color: white;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
      }

      .tips {
        font-size: 1.4rem;
        color: #d9001b;
        text-align: center;
      }
    }
  }

  .passIcons {
    display: flex;
    margin-top: -8px;
    align-items: center;
    justify-content: center;

    .passIcon {
      width: 70px;
      color: #fff;
      text-align: center;

      & + .passIcon {
        margin-left: 40px;
      }

      > img {
        display: block;
        width: 100%;
      }
    }
  }

  .time {
    position: relative;
    top: 5rem;
    z-index: 3;
    display: inline-block;
    font-size: 2.5rem;
    font-weight: 500;
    color: white;
  }
}

.mt {
  margin-top: 4.2rem;
}
