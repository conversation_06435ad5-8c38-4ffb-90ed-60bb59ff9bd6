import { get, post } from '@/utils';
import { IVisitorPassLResponse } from '../home/<USER>';

/**
 * 查询访客通行证
 * https://soapi.sheincorp.cn/application/3580/routes/169595/doc
 */
export const getVisitorPassList = () => {
  return post<IVisitorPassLResponse>('/ams/h5/visitorPass/selectVisitorPassList');
};

/**
 * 获取系统时间
 * https://soapi.sheincorp.cn/application/3694/routes/170339/doc
 * @returns
 */
export const getSysTime = () => {
  return get<string>('/ams/h5/visitorPass/getSystemTime', {});
};
