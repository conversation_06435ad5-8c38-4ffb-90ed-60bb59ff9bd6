.container {
  position: relative;
  // height: 100vh;
  // overflow: hidden;
  width: 100%;
  // height: 100vh;
  height: auto;
  min-height: 0;
  overflow: auto;
  background-color: white;
  -webkit-overflow-scrolling: touch;
  flex: 1;

  .passPassList {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .currentPass {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .btnLeft {
    position: absolute;
    top: 45vh;
    left: 1rem;
    z-index: 5;
  }

  .btnRight {
    position: absolute;
    top: 45vh;
    right: 1rem;
    z-index: 5;
  }

  .navGroup {
    position: relative;
    top: 7rem;
    z-index: 5;
    width: 100%;
    padding-bottom: 3rem;
    text-align: center;

    .dot {
      display: inline-block;
      width: 7px;
      height: 7px;
      margin: 0 10px;
      background-color: white;
      border-radius: 50%;
    }

    .currentDot {
      display: inline-block;
      width: 10px !important;
      height: 10px !important;
      margin: 0 10px;
      background-color: white;
      border-radius: 50%;
    }
  }
}
