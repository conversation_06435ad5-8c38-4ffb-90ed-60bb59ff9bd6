import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import Icon from '@/_/components/Icon';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';
import { forEach } from 'lodash';
import moment from 'moment';
import { Toast } from 'shineout-mobile';
import { IVisitorPassLResponse } from '../home/<USER>';
import { getUnixTime } from '../utils';
import PassCard from './components/pass-card';
import PassCardOverdue from './components/pass-card-overdue';
import { getSysTime, getVisitorPassList } from './services';
import styles from './style.less';

const PassCardList: React.FC = () => {
  const { id } = useParams();
  console.log('id', id);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [systemTimestamp, setSystemTimestamp] = useState(0);
  const [data, setData] = useState<IVisitorPassLResponse>();
  useEffect(() => {
    getSysTime().then((res) => {
      if (res) {
        setSystemTimestamp(
          // eslint-disable-next-line @shein-bbl/bbl/translate-i18n-byT
          getUnixTime(res.replace('年', '-').replace('月', '-').replace('日', '')),
        );

        const timer = window.setInterval(() => {
          setSystemTimestamp((prevCount) => {
            return prevCount + 1;
          });
        }, 1000);

        getVisitorPassList().then((res) => {
          if (res?.length === 0) {
            Toast.info(t('当前没有可用的通行证'));
          }
          setData(res);
          forEach(res, (item, index) => {
            if (item.amsAppointmentRecordId === Number(id)) {
              setCurrentIndex(index);
            }
          });
        });

        return () => {
          clearInterval(timer);
        };
      }
      return () => {
        console.log(t('取消挂载'));
      };
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /**
   * @description checkIsOverdue
   * @param {unknown} appointmentEndTime desc
   * @returns {unknown} desc
   */
  const checkIsOverdue = (appointmentEndTime) => {
    if (!appointmentEndTime) return false;
    if (getUnixTime(appointmentEndTime) > systemTimestamp) {
      return false;
    } else {
      return true;
    }
  };

  if (data?.length === 0 || !data) return null;

  return (
    <div className={styles.container}>
      <div className={styles.passPassList}>
        <div className={styles.currentPass}>
          {data?.[currentIndex] && (
            <>
              {checkIsOverdue(data?.[currentIndex]?.appointmentEndTime) ? (
                <PassCardOverdue
                  data={data[currentIndex]}
                  nav={
                    <div className={styles.navGroup}>
                      {data.map((item, index) => {
                        return (
                          <span
                            onClick={() => setCurrentIndex(index)}
                            key={item.id}
                            className={
                              currentIndex === index
                                ? classNames(styles.dot, styles.currentDot)
                                : styles.dot
                            }
                          ></span>
                        );
                      })}
                    </div>
                  }
                  currentDatetime={moment(systemTimestamp * 1000).format('YYYY/MM/DD HH:mm:ss')}
                />
              ) : (
                <PassCard
                  data={data[currentIndex]}
                  currentDatetime={moment(systemTimestamp * 1000).format('YYYY/MM/DD HH:mm:ss')}
                  nav={
                    <div className={styles.navGroup}>
                      {data.map((item, index) => {
                        return (
                          <span
                            onClick={() => setCurrentIndex(index)}
                            key={item.id}
                            className={
                              currentIndex === index
                                ? classNames(styles.dot, styles.currentDot)
                                : styles.dot
                            }
                          ></span>
                        );
                      })}
                    </div>
                  }
                />
              )}
            </>
          )}
        </div>
      </div>
      {data?.length > 1 && (
        <div className={styles.btnLeft}>
          <Icon
            style={{
              margin: '1rem',
            }}
            name="arr-left"
            fontSize={'3rem'}
            color={currentIndex === 0 ? 'gray' : 'white'}
            onClick={() => {
              if (currentIndex > 0) setCurrentIndex(currentIndex - 1);
            }}
          />
        </div>
      )}
      {data?.length > 1 && (
        <div className={styles.btnRight}>
          <Icon
            style={{
              margin: '1rem',
            }}
            name="arr-right"
            fontSize={'3rem'}
            color={currentIndex === data.length - 1 ? 'gray' : 'white'}
            onClick={() => {
              if (currentIndex < data.length - 1) setCurrentIndex(currentIndex + 1);
            }}
          />
        </div>
      )}
    </div>
  );
};

export default PassCardList;
