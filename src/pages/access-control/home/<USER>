import { get, post } from '@/utils';
import { IGtRegisterResponse, IPhoneLoginParams, ISendMessagesParams } from './interfaces';

/**
 * 极验初始化验证请求
 * https://soapi.sheincorp.cn/application/3694/routes/176664/doc
 */
export const gtRegister = () =>
  // params: { riskType: 'click' | 'slide' | 'fullpage' }
  {
    return post<IGtRegisterResponse>(
      '/ams/h5/login/gtRegister',
      {},
      {
        showErrorMsg: true,
      },
    );
  };

/**
 * 发送登录验证码短信通知
 * https://soapi.sheincorp.cn/application/3580/routes/169142/doc
 */
export const sendMessages = (params: ISendMessagesParams) => {
  return post<boolean>('/ams/h5/login/sendMessage', params, {
    showErrorMsg: false,
  });
};

/**
 * 登录
 * https://soapi.sheincorp.cn/application/3580/routes/169179/doc
 */
export const phoneLogin = (params: IPhoneLoginParams) => {
  return post<string>('/ams/h5/login/createAuthToken', params);
};

/**
 * @description logout
 * @returns {unknown} desc
 */
export const logout = () => {
  return get<string>('/ams/h5/login/logout ');
};
