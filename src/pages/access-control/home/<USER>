import ArrowIcon from '@/_/assets/images/access-control/arrow.png';
import BookIcon from '@/_/assets/images/access-control/book.png';
import BgImg from '@/_/assets/images/access-control/home-wave.png';
import PointIcon from '@/_/assets/images/access-control/point.png';
import StampIcon from '@/_/assets/images/access-control/stamp.png';
import TimeIcon from '@/_/assets/images/access-control/time.png';
import Icon from '@/_/components/Icon';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { useMount } from '@shein-lego/use';
import { Button, Dialog } from 'shineout-mobile';
import { selectFlowData } from '../appointment/services';
import { getVisitorPassList } from '../pass-card-list/services';
import { clearTokenInCookie } from '../utils';
import { logout } from './services';
import styles from './style.less';

const Home: React.FC = () => {
  usePageTitle(t('首页'));
  useMount(() => {
    selectFlowData()
      .then((res) => {
        if (res === null) {
          console.log('existAppointment.err');
        } else {
          console.log('existAppointment', res);
        }
      })
      .catch();
  });

  /**
   * 登出
   */
  const handleLogout = () => {
    console.log('handleLogout');

    Dialog.confirm({
      title: t('登出'),
      message: t('是否退出当前帐户'),
      onOk: () => {
        logout()
          // .then(() => {})
          .finally(() => {
            clearTokenInCookie();
            location.replace(`/#/access-control/login?redirect=home`);
          });
      },
    });
  };

  /**
   * 访问通行证
   */
  const handleClickEnterPass = () => {
    getVisitorPassList().then((res) => {
      if (res?.length === 0 || res === null) {
        Dialog.alert({
          title: t('当前暂无预约'),
          message: t('您当前没有进行中的预约或预约未到开始时间'),
          onOk: () => console.log('onOk callback!'),
        });
      } else {
        window.location.href = '/#/access-control/pass-card-list/0';
      }
    });
  };
  return (
    <div className={styles.main}>
      <img className={styles.headerBg} src={BgImg} alt={t('背景图片')} />
      <div className={styles.container}>
        <div className={styles.title}>
          <div>
            <Icon name="shein-logo" />
          </div>
          <div>{t('物流园区访客预约平台')}</div>
        </div>
        <div className={styles.btnGroup}>
          <div
            className={styles.inPark}
            onClick={() => {
              window.location.href = '/#/access-control/appointment';
            }}
          >
            <div className={styles.cnWord}>{t('预约入园')}</div>
            <div className={styles.enWord}>Reservation</div>
            <div>
              <img className={styles.icon} src={ArrowIcon} alt={t('箭头图标')} />
              <img className={styles.mainIcon} src={TimeIcon} alt={t('时间图标')} />
            </div>
          </div>

          <div
            className={styles.passCard}
            onClick={() => {
              handleClickEnterPass();
            }}
          >
            <div className={styles.cnWord}>{t('访客通行证')}</div>
            <div className={styles.enWord}>Pass</div>
            <div>
              <img className={styles.icon} src={PointIcon} alt={t('点点图标')} />
              <img className={styles.mainIcon} src={StampIcon} alt={t('盖章图标')} />
            </div>
          </div>
        </div>
        <div
          className={styles.actionBtn}
          onClick={() => {
            window.location.href = '/#/access-control/appointment-record';
          }}
        >
          <img src={BookIcon} alt={t('预约图标')} className={styles.icon} />
          <span className={styles.title}>{t('预约记录')}</span>
          <Icon name="triangle-right" className={styles.right} fontSize={24}></Icon>
        </div>

        <div className={styles.cookbook}>
          <div className={styles.section}>
            <div className={styles.title}>{t('预约步骤')}</div>
            <ul>
              <li>{t('点击我要预约，输入预约手机。')}</li>
              <li>{t('填写访客的个人信息。')}</li>
              <li>{t('填写被访人信息，并根据需要补充预约资料。')}</li>
            </ul>
          </div>
          <div className={styles.section}>
            <div className={styles.title}>{t('注意事项')}</div>
            <ul>
              <li>{t('审核通过后，相关信息会以短信的形式发送至您的预约手机。')}</li>
              <li>{t('您亦可通过预约记录实时查看您的申请记录及相应审批状态。')}</li>
              <li>{t('在被批准的预约时间内，可通过出示“大门凭证”以进出园区。')}</li>
            </ul>
          </div>
        </div>
        <div className={styles.logout}>
          <Button type="default" size="small" onClick={() => handleLogout()}>
            <Icon name="logout" fontSize={14} color="#666c7c" />
            <span
              style={{
                marginTop: 0,
                marginLeft: 3,
                color: '#666c7c',
              }}
            >
              {t('登出')}
            </span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Home;
