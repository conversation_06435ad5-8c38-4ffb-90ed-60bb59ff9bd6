import { EAllowCarry } from '../interfaces';

export interface IGtRegisterResponse {
  /** 验证唯一 */
  cid: string;
  /** 验证流水号 */
  challenge: string;
  /** 验证类型 */
  type: string;
  /** 静态资源列表 */
  staticServers: string[];
  /** fullpage JS 路径 */
  fullpage: string;
  /** click Js 路径 */
  click: string;
  /** slide Js 路径 */
  slide: string;
  /** 验证服务器地址 */
  apiServer: string;
  /** 请求id */
  requestId: string;
  /** 是否降级 */
  enabled?: boolean;
}

export interface ISendMessagesParams {
  /** 请求id */
  requestId: string;
  /** 验证流水号 */
  challenge: string;
  /** 用户手机号 */
  phone: string;
}

export interface IPhoneLoginParams {
  /** 请求id */
  requestId: string;
  /** 短信验证码 */
  figureCode: string;
  /** 用户手机号 */
  phone: string;
}

interface IAppointmentParksItem {
  /** 园区id */
  id: number;
  /** 园区名称 */
  parkName: string;
}

export type IAppointmentParks = IAppointmentParksItem[];

export interface IVisitorPassVosItem {
  /** id */
  id: number;
  /** 访客姓名 */
  visitorName: string;
  /** 访客合车牌 */
  visitorLicensePlate: string;
  /** 访问园区列表 */
  appointmentParks: IAppointmentParks;
  /** 来访开始时间 */
  appointmentBeginTime: string;
  /** 来访结束时间 */
  appointmentEndTime: string;
  /** 被访人英文名 */
  appointmentEnName: string;
  /** 来访原因 */
  appointmentReason: string;
  /** 预约记录id */
  amsAppointmentRecordId: number;
  /** 是否携带手机进入库区 */
  carryPhone?: EAllowCarry;
  /** 是否携带电脑进入库区 */
  carryComputer?: EAllowCarry;
}

export type IVisitorPassLResponse = IVisitorPassVosItem[];
