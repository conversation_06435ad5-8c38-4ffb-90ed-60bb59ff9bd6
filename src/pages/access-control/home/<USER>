/* stylelint-disable max-nesting-depth */
#app {
  background-color: white !important;
}

.main {
  position: relative;
  padding-bottom: 16rem;
  background-color: white;

  .headerBg {
    position: absolute;
    z-index: 0;
    width: 100%;
  }

  .container {
    position: relative;
    z-index: 1;
    width: 100%;
    height: fit-content;

    .title {
      position: relative;
      top: 2rem;
      left: 3rem;

      div {
        position: relative;
        top: 1.5rem;
        z-index: 1;
        font-size: 2.2rem;
        font-weight: 500;
        color: white;
      }
    }

    .btnGroup {
      position: relative;
      top: 10rem;
      z-index: 1;
      display: flex;
      text-align: center;
      flex-direction: row;
      justify-content: center;

      .inPark {
        width: 41%;
        margin-right: 3%;
        text-align: left;
        cursor: pointer;
        background: linear-gradient(-135deg, #6cecd6, #19a487);
        border-radius: 1rem;
        box-sizing: border-box;
        aspect-ratio: 1/1;
        box-shadow: rgb(235 64 20 / 10%) 1rem 1rem 2rem 0;

        .cnWord {
          position: relative;
          top: 2.5rem;
          left: 1.5rem;
          display: inline-block;
          overflow: hidden;
          font-size: 1.8rem;
          font-weight: 500;
          color: white;
        }

        .enWord {
          position: relative;
          top: 3rem;
          left: 1.5rem;
          font-size: 1.5rem;
          letter-spacing: 2px;
          color: white;
        }

        .icon {
          position: relative;
          top: 9.5rem;
          left: 1.5rem;
          z-index: 1;
          float: left;
          width: 4.5rem;
        }

        .mainIcon {
          position: relative;
          left: 2rem;
          z-index: 1;
          float: right;
          width: 15rem;
        }
      }

      .passCard {
        width: 41%;
        margin-left: 3%;
        text-align: left;
        cursor: pointer;
        background: linear-gradient(-135deg, #6ccaf9, #3a77f1);
        border-radius: 1rem;
        box-sizing: border-box;
        aspect-ratio: 1/1;
        box-shadow: rgb(235 64 20 / 10%) 1rem 1rem 2rem 0;

        .cnWord {
          position: relative;
          top: 2.5rem;
          left: 1.5rem;
          display: inline-block;
          overflow: hidden;
          font-size: 1.8rem;
          font-weight: 500;
          color: white;
        }

        .enWord {
          position: relative;
          top: 3rem;
          left: 1.5rem;
          font-size: 1.5rem;
          letter-spacing: 2px;
          color: white;
        }

        .icon {
          position: relative;
          top: 9.5rem;
          left: 1.5rem;
          z-index: 1;
          float: left;
          width: 4rem;
        }

        .mainIcon {
          position: relative;
          left: 2rem;
          z-index: 1;
          float: right;
          width: 15rem;
        }
      }
    }

    .actionBtn {
      position: relative;
      top: 12.5rem;
      left: 6%;
      z-index: 1;
      display: flex;
      width: 88%;
      padding: 10px;
      cursor: pointer;
      background-color: white;
      border: 1px solid #f5f6fb;
      border-radius: 1rem;
      box-shadow: rgb(20 23 55/10%) 1rem 1rem 2rem 0;
      flex-direction: row;
      align-items: center;

      .icon {
        position: relative;
        left: 1rem;
        display: block;
        width: 3rem;
        height: 3rem;
        font-size: 2.4rem;
      }

      .title {
        position: relative;
        top: 0;
        left: 2rem;
        display: block;
        font-size: 1.4rem;
        color: #333e59;
        flex-grow: 1;
      }

      .right {
        position: relative;
        display: block;
        font-size: 3rem;
        color: #aac8a7;
      }
    }

    .cookbook {
      position: relative;
      top: 17rem;
      left: 6%;
      width: 88%;
      padding: 10px;
      margin-bottom: 10rem;
      background-color: #f4f5f8;
      border-radius: 2rem;

      .section {
        position: relative;
        left: 1rem;
        width: 100%;
        padding-right: 10px;
        padding-bottom: 1rem;
      }

      .section ul {
        margin-left: 1rem;
        list-style-type: disc;
        list-style-position: outside;
      }

      .section li {
        padding: 5px 0;
        font-size: 12px;
        color: #666c7c;
      }

      .section li::marker {
        font-size: 16px;
        /* stylelint-disable-next-line prettier/prettier */
        color: #d5e1fc;
      }

      .section .title {
        position: relative;
        top: 0;
        left: 0;
        display: block;
        margin-top: 10px;
        margin-bottom: 10px;
        margin-left: 1.2rem;
        font-size: 1.4rem;
        font-weight: 500;
        color: #666c7c;
      }
    }

    .logout {
      position: relative;
      top: 10rem;
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: center;
    }
  }
}
