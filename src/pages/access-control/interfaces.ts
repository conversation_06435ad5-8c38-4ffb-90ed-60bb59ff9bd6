import React from 'react';
import { IVisitorPassVosItem } from './home/<USER>';

interface IAppointmentParksItem {
  /** 园区id */
  id: number;
  /** 园区名称 */
  parkName: string;
}

type IAppointmentParks = IAppointmentParksItem[];

export interface ICompanionDtosItem {
  _hash?: string;
  /** 同行人手机号 */
  companionPhone: string;
  /** 同行人姓名 */
  companionName: string;
  /** 同行人车牌号 */
  visitorLicensePlate: string;
  /** 是否进入库区 */
  needEnterStash: boolean;
  /** 人脸id */
  faceId?: number;
  /** 人脸图片url */
  fileUrl?: string;
}

export type ICompanionDtos = ICompanionDtosItem[];

export interface IAppointmentItem {
  /** 来访开始时间 */
  appointmentBeginTime: string;
  /** 来访结束时间 */
  appointmentEndTime: string;
  /** 被访人工号 */
  appointmentWorkNum: string;
  /** 被访人英文名 */
  appointmentEnName: string;
  /** 人脸图片url */
  fileUrl: string;
  /** 来访原因 */
  appointmentReason: string;
  /** 访问园区列表 */
  appointmentParks: IAppointmentParks;
  /** 预约流程表单步数 0-第2步，1-第3步，2-第4步 */
  appointmentStep?: number;
  /** 人脸id */
  faceId: number;
  /** 访客手机号,加密存储 */
  visitorPhone: string;
  /** 是否进入库区 */
  needEnterStash: boolean;
  /** 同行人列表 */
  companionDtos: ICompanionDtos;
  /** 预约流程表单id */
  appointmentId: number;
}

export interface ISaveVisitorRequest {
  /** 访客姓名 */
  visitorName: string;
  /** 访客公司 */
  visitorCompany: string;
  /** 访客合车牌 */
  visitorLicensePlate: string;
  /** 是否需要校验参数的标记（点击下一步：true, 自动保存：false） */
  needValidate: boolean;
  /** 预约流程id */
  appointmentId: number;
}

export interface IFaceItem {
  faceId: number;
  fileUrl: string;
}

interface IAppointmentParksItem {
  /** 园区id */
  id: number;
  /** 园区名称 */
  parkName: string;
}

export interface ISelectVisitorResponse {
  /** 访客姓名 */
  visitorName: string;
  /** 访客公司 */
  visitorCompany: string;
  /** 访客车牌号 */
  visitorLicensePlate: string;
}

interface IAppointmentParksItem {
  /** 园区id */
  id: number;
  /** 园区名称 */
  parkName: string;
}
interface IAppointmentParksItem {
  /** 园区id */
  id: number;
  /** 园区名称 */
  parkName: string;
}

interface IVisitorDto {
  /** 访客姓名 */
  visitorName: string;
  /** 访客公司 */
  visitorCompany: string;
  /** 访客车牌号 */
  visitorLicensePlate: string;
}

interface IAppointmentParksItem {
  /** 园区id */
  id: number;
  /** 园区名称 */
  parkName: string;
}

export enum EAllowCarry {
  /** 否 */
  NO = 1,
  /** 是 */
  YES = 2,
}

export interface IFormAppointmentInfoDto {
  /** 来访开始时间 */
  appointmentBeginTime: string;
  /** 来访结束时间 */
  appointmentEndTime: string;
  /** 被访人工号 */
  appointmentWorkNum: string;
  /** 被访人英文名 */
  appointmentEnName: string;
  /** 人脸图片url */
  fileUrl: string;
  /** 来访原因 */
  appointmentReason: string;
  /** 访问园区列表 */
  appointmentParks: IAppointmentParks;
  /** 预约流程表单步数 0-第2步，1-第3步，2-第4步 */
  appointmentStep: number;
  /** 人脸id */
  faceId: number;
  /** 访客手机号,加密存储 */
  visitorPhone: string;
  /** 是否进入库区 */
  needEnterStash: boolean;
  /** 同行人列表 */
  companionDtos: ICompanionDtos;
  /** 预约流程表单id */
  appointmentId: number;
  /** 是否携带手机进入库区 */
  carryPhone: EAllowCarry;
  /** 是否携带电脑进入库区 */
  carryComputer: EAllowCarry;
}

export interface IAppointmentAllInfo {
  /** 是否需要校验参数的标记（点击下一步：true, 自动保存：false） */
  needValidate?: boolean;
  /** 表单访客信息 */
  visitorDto: IVisitorDto;
  /** 表单访问信息 */
  formAppointmentInfoDto: IFormAppointmentInfoDto;
}

export interface IPassCardProps {
  data: IVisitorPassVosItem;
  currentDatetime: string;
  nav: React.ReactNode;
}
