.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(180deg, #f6fbff 0%, #bddeff 100%);
}

.content {
  display: flex;
  width: 100%;
  max-width: 400px;
  padding: 60px 0 50px;
  background-color: #fff;
  border-radius: 8px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 12px #0000001a;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.logoText {
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  color: #197afa;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.qrCodeContainer {
  display: flex;
  margin: 20px 0;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  color: #666;
  text-align: center;
  flex-direction: column;
  align-items: center;
}

.qrCodeTip {
  margin-top: 20px;
}

.refreshTip {
  margin: 12px;
  font-size: 14px;
  color: #1677ff;
  cursor: pointer;
}

.notBound {
  display: flex;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  color: #141737;
  text-align: center;
  flex-direction: column;
  align-items: center;
}

.notBoundTip {
  margin-bottom: 8px;
}

.snCodeInfo {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  color: #666c7c;
  text-align: center;
}
