import { get } from '../_/utils/fetch';
// // 导入 mock 数据
// import getCodeMock from './mock/getCode.json';
// import getUlpTokenMock from './mock/getUlpToken.json';
// import getUlpTokenEmptyMock from './mock/getUlpTokenEmpty.json';

export interface IGetLoginCodeParams {
  /** sn码 */
  snCode: string;
}

export interface IGetLoginCodeResponse {
  /** 设备登录二维码字符串 */
  loginCodeStr: string;
  /** 设备是否绑定档口 */
  alreadyBindingStall: boolean;
  /** 设备登录id */
  loginCodeId: string;
}

/**
 * 食堂设备端获取登录二维码
 * https://soapi.sheincorp.cn/application/3694/routes/245459/doc
 * @param {IGetLoginCodeParams} params
 * @returns {Promise<IGetLoginCodeResponse>} 返回值
 */
export const getLoginCodeAPI = (params: IGetLoginCodeParams) => {
  // // 开发环境使用 mock 数据
  // if (process.env.NODE_ENV === 'development') {
  //   return Promise.resolve(getCodeMock.data);
  // }
  return get<IGetLoginCodeResponse>('/canteen/device/login/getCode', params);
};

export interface IGetUlpTokenParams {
  /** 二维码id */
  loginCodeId: string;
}

// // 用于模拟轮询的请求计数器
// const requestCount = 0;

/**
 * 食堂设备端获取token
 * https://soapi.sheincorp.cn/application/3694/routes/245462/doc
 * @param {IGetUlpTokenParams} params
 * @returns {Promise<string>} ulptoken
 */
export const getUlpTokenAPI = (params: IGetUlpTokenParams) => {
  // // 开发环境使用 mock 数据
  // if (process.env.NODE_ENV === 'development') {
  //   // 增加请求计数
  //   requestCount += 1;

  //   // 前5次请求返回空，第6次及以后返回 token
  //   if (requestCount >= 6) {
  //     return Promise.resolve(getUlpTokenMock.data);
  //   }
  //   return Promise.resolve(getUlpTokenEmptyMock.data);
  // }
  return get<string>('/canteen/device/login/getUlpToken', params);
};
