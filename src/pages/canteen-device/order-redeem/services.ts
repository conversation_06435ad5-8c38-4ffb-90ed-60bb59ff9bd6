import { post } from '@/utils';
// // 导入 mock 数据
// import infoMock from './mock/info.json';
// import scanMock from './mock/scan.json';

export interface IGetRedeemInfoParams {
  /** 设备序列号 */
  snCode: string;
}

export interface IGetRedeemInfoResponse {
  /** 餐段类型code */
  mealType: number;
  /** 餐段类型名称 */
  mealTypeName: string;
  /** 档口名称 */
  stallName: string;
  /** 总预定单数量 */
  totalOrder: number;
  /** 已核销数量 */
  redeemedOrder: number;
}

/**
 * 获取档口和餐数信息
 * https://soapi.sheincorp.cn/application/3694/routes/245569/doc
 * @param {IGetRedeemInfoParams} params
 * @returns {Promise<IGetRedeemInfoResponse>} 返回值
 */
export const getRedeemInfoAPI = (params: IGetRedeemInfoParams) => {
  // // 开发环境使用 mock 数据
  // if (process.env.NODE_ENV === 'development') {
  //   return Promise.resolve(infoMock.data);
  // }
  return post<IGetRedeemInfoResponse>('/canteen/device/redeem/info', params);
};

export interface IPostRedeemScanParams {
  /** 二维码字符串 */
  qrcode: string;
  /** 设备编码 */
  snCode: string;
}

/**
 * 扫描核销
 * https://soapi.sheincorp.cn/application/3694/routes/245571/doc
 * @param {IPostRedeemScanParams} params
 * @returns {Promise<boolean>} 返回值
 */
export const postRedeemScanAPI = (params: IPostRedeemScanParams) => {
  // // 开发环境使用 mock 数据
  // if (process.env.NODE_ENV === 'development') {
  //   return Promise.resolve(scanMock.data);
  // }
  return post<boolean>('/canteen/device/redeem/scan', params);
};
