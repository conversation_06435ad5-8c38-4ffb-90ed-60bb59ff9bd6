/* 订单核销页面样式 */
.container {
  position: relative;
  display: flex;
  height: 100vh;
  overflow: hidden;
  background-color: #f4f5f8;
  flex-direction: column;

  /* 添加滑动过渡效果 */
  transition: transform .3s ease-out;
}

/* 顶部导航栏和标签页 */
.tabs {
  position: relative;
  display: flex;
  height: 50px;
  padding: 0 12px;
  background-color: #fff;
  border-bottom: 1px solid #e8ebf0;
}

.backButton {
  display: flex;
  width: 40px;
  height: 50px;
  color: #141737;
  justify-content: center;
  align-items: center;
}

.tab,
.tabActive {
  position: relative;
  display: flex;
  font-size: 16px;
  color: #666c7c;
  flex: 1;
  justify-content: center;
  align-items: center;
}

.tabActive {
  font-weight: 500;
  color: #141737;
}

.tabActive::after {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 60%;
  height: 2px;
  background-color: #197afa;
  content: '';
  transform: translateX(-50%);
}

.setting {
  display: flex;
  width: 40px;
  height: 50px;
  justify-content: center;
  align-items: center;
}

/* 档口信息卡片 */
.infoCard {
  padding: 16px 16px 20px;
  background-color: #fff;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
}

.stallInfo {
  display: flex;
  margin-bottom: 16px;
  align-items: center;
}

.stallName {
  overflow: hidden;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  color: #141737;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.dateInfo {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  color: #666c7c;
  text-align: right;
}

.mealType {
  display: inline-block;
  padding: 2px 8px;
  margin-left: 4px;
  font-size: 14px;
  color: #fff8f0;
  background-color: #ff6b00;
  border-radius: 4px;
}

.refresh {
  position: absolute;
  top: 24px;
  right: 0;
  display: flex;
  width: 32px;
  height: 32px;
  color: #666c7c;
  cursor: pointer;
  justify-content: center;
  align-items: center;
}

.orderInfo {
  display: flex;
  padding: 16px;
  background: #f7f8fa;
  border-radius: 4px;
}

.orderItem {
  flex: 1;
  text-align: center;
}

.orderCount {
  margin-bottom: 8px;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 26px;
  color: #141737;
  text-align: center;
}

.orderLabel {
  display: inline-block;
  padding: 4px 12px;
  font-size: 14px;
  color: #666c7c;
  background-color: #f4f5f8;
  border-radius: 12px;
}

/* 扫码区域 */
.scanArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.scanImage {
  margin-bottom: 20px;
}

.scanTip {
  font-size: 16px;
  color: #666c7c;
  text-align: center;
}

/* 按钮区域 */
.buttonArea {
  padding: 0 20px 20px;
  background-color: #fff;
}

.scanButton {
  width: 100%;
  height: 48px;
  font-size: 16px;
}
