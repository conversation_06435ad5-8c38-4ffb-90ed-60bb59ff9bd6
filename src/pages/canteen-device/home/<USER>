import { get } from '@/utils/fetch';
// // 导入 mock 数据
// import getDataMock from './mock/getData.json';

export interface IGetHomeDataParams {
  /** 设备sn码 */
  snCode: string;
}

export interface IGetHomeDataResponse {
  /** 食堂名称 */
  canteenName?: string;
  /** 设备名称 */
  stallName?: string;
  /** 档口收款金额 */
  totalAmount?: number;
  /** 档口核销订单数量 */
  orderCount?: string;
  /** 档口logo */
  merchantLogoUrl?: string;
}

/**
 * 获取食堂设备端首页数据
 * https://soapi.sheincorp.cn/application/3694/routes/245688/doc
 * @param {IGetHomeDataParams} params
 * @returns 首页数据
 */
export const getHomeDataAPI = (params: IGetHomeDataParams): Promise<IGetHomeDataResponse> => {
  // // 开发环境使用 mock 数据
  // if (process.env.NODE_ENV === 'development') {
  //   return Promise.resolve(getDataMock.data);
  // }
  return get<IGetHomeDataResponse>('/canteen/device/order/getTodayOperationalData', params);
};
