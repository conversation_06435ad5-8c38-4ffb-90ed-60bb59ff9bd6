.container {
  display: flex;
  min-height: 100vh;
  padding: 16px 0 0;
  background: linear-gradient(180deg, #f6fbff 0%, #bddeff 100%);
  background-color: #f5f5f5;
  flex-direction: column;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;

  .loadingText {
    margin-top: 16px;
    font-size: 14px;
    color: #666;
  }
}

.header {
  padding: 16px;
  margin: 0 16px;
  margin-bottom: 16px;
  border-radius: 8px;
}

.stallInfo {
  display: flex;
  align-items: center;
}

.stallLogo {
  display: flex;
  width: 48px;
  height: 48px;
  margin-right: 12px;
  overflow: hidden;
  background-color: #f0f0f0;
  border-radius: 50%;
  justify-content: center;
  align-items: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.stallText {
  max-width: calc(100% - 60px); /* 减去logo的宽度和margin */
  min-width: 0; /* 确保flex子项可以收缩到比内容更小 */
  flex: 1;
}

.stallName {
  width: 100%; /* 确保宽度占满父容器 */
  margin-bottom: 4px;
  overflow: hidden;
  font-size: 18px;
  font-weight: 500;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.canteenName {
  display: flex;
  width: 100%; /* 确保宽度占满父容器 */
  font-size: 14px;
  color: #666;
  align-items: center;
}

/* 为canteenName中的文本部分添加省略号 */
.canteenName span {
  min-width: 0; /* 确保flex子项可以收缩 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.businessData {
  padding: 16px;
  margin: 0 16px;
  background: linear-gradient(90deg, #deedff 0%, #ececfd 100%);
  border-radius: 6px 6px 0 0;
}

.dataTitleWrapper {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.dataTitle {
  font-size: 16px;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  color: #666c7c;
}

.dataVisibilityIcon {
  padding: 4px;
  cursor: pointer;
}

.dataContent {
  display: flex;
  justify-content: space-between;
}

.dataItem {
  flex: 1;
  text-align: center;
}

.dataValue {
  margin-bottom: 6px;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 26px;
  color: #141737;
  text-align: center;
}

.dataLabel {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  color: #666c7c;

  /* 157.143% */
}

.menuList {
  padding: 16px;
  background: linear-gradient(180deg, #fff 0%, #f7f8fa 24.76%);
  border-radius: 10px 10px 0 0;
  flex: 1;
}

.menuItem {
  position: relative;
  display: flex;
  height: 78px;
  padding: 12px;
  margin-bottom: 12px;
  background: #fff;
  border-radius: 4px;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
  align-self: stretch;
  box-shadow: 0 0 14.7px 0 rgb(3 53 126 / 2%);

  &:not(:last-child)::after {
    position: absolute;
    right: 16px;
    bottom: 0;
    left: 16px;
    height: 1px;
    background-color: #f0f0f0;
    content: '';
  }
}

.menuIcon {
  display: flex;
  width: 40px;
  height: 40px;
  margin-right: 12px;
  background-color: #f0f0f0;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
}

.menuName {
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  color: var(--neutral-text-5141737, var(--neutral-text-5141737, #141737));
  flex: 1;
}

.menuArrow {
  color: #999;
}

.footer {
  text-align: center;
}

.snCode {
  font-size: 14px;
  color: #999;
}

.copyBtn {
  margin-left: 8px;
  color: #1890ff;
}
