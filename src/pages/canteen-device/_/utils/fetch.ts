import { get as baseGet, post as basePost } from '@/utils';
import { t } from '@shein-bbl/react';
import { Toast } from 'shineout-mobile';
import { getDeviceSNCode } from './snCode';

/**
 * 获取带有SN码的请求配置
 * @param {object} config 原始请求配置
 * @returns {object} 添加了SN码的请求配置
 */
export const getRequestConfigWithSNCode = (config?: any): any => {
  const snCode = getDeviceSNCode();

  // 如果没有snCode，会在请求前显示错误提示
  if (!snCode) {
    Toast.fail(t('无法获取设备sn码，请联系管理员'));
  }

  return {
    ...(config || {}),
    headers: {
      ...(config?.headers || {}),
      snCode: snCode || '',
    },
  };
};

/**
 * 封装的GET请求方法，自动添加snCode到header
 * @param {string} url 请求URL
 * @param {any} params 请求参数
 * @param {any} config 请求配置
 * @returns {Promise<T>} 请求Promise
 */
const get = <T>(url: string, params?: any, config?: any): Promise<T> => {
  return baseGet<T>(url, params, getRequestConfigWithSNCode(config));
};

/**
 * 封装的POST请求方法，自动添加snCode到header
 * @param {string} url 请求URL
 * @param {any} data 请求数据
 * @param {any} config 请求配置
 * @returns {Promise<T>} 请求Promise
 */
const post = <T>(url: string, data?: any, config?: any): Promise<T> => {
  return basePost<T>(url, data, getRequestConfigWithSNCode(config));
};

export { get, post };
