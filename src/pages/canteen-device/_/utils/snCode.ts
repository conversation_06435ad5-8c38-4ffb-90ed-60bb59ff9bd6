import { t } from '@shein-bbl/react';
import { Toast } from 'shineout-mobile';

// 为window.androidDevice添加类型定义
declare global {
  interface Window {
    androidDevice?: {
      getSN?: () => string;
      [key: string]: any;
    };
  }
}

/**
 * 获取设备SN码
 * @returns {string|null} 设备SN码，如果获取失败则返回null
 */
const getDeviceSNCode = (): string | null => {
  try {
    // 优先从localStorage获取
    const localSn = localStorage.getItem('canteenDeviceSN');
    if (localSn) {
      return localSn;
    }
    if (window.androidDevice && window.androidDevice.getSN) {
      const snCode = window.androidDevice.getSN();
      localStorage.setItem('canteenDeviceSN', snCode);
      return snCode || null;
    }
    return null;
  } catch (error) {
    console.error(t('获取设备SN码失败:'), error);
    return null;
  }
};

/**
 * 检查是否有设备SN码，如果没有则显示错误提示
 * @returns {boolean} 是否有设备SN码
 */
const checkDeviceSNCode = (): boolean => {
  const snCode = getDeviceSNCode();
  if (!snCode) {
    Toast.fail(t('无法获取设备sn码，请联系管理员'));
    return false;
  }
  return true;
};

export { checkDeviceSNCode, getDeviceSNCode };
