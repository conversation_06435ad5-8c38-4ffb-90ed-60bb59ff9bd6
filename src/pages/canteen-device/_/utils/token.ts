const TOKEN_KEY = 'ULP_TOKEN';

// 过期时间7天
const ExpirationTime = 7 * 24 * 60 * 60 * 1000;

/**
 * 检查是否存在有效的食堂设备前端token
 * @returns {boolean} 如果存在有效token则返回true，否则返回false
 */
function checkIsLogin(): boolean {
  return document.cookie.indexOf(TOKEN_KEY) !== -1 && getCookie() !== '';
}

/**
 * 设置食堂设备前端token的cookie
 * @param {string} token - 需要设置的token值
 * @returns {void}
 */
function setCookie(token: string): void {
  const expires = new Date();
  expires.setTime(expires.getTime() + ExpirationTime);
  document.cookie = `${TOKEN_KEY}=${token || ''};expires=${expires.toUTCString()};path=/`;
}

/**
 * 获取食堂设备前端token的cookie值
 * @returns {string} 获取到的token值，如果不存在则返回空字符串
 */
function getCookie(): string {
  const name = `${TOKEN_KEY}=`;
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    const c = ca[i].trim();
    if (c.indexOf(name) === 0) {
      return c.substring(name.length, c.length);
    }
  }
  return '';
}

/**
 * 移除食堂设备前端token的cookie
 * @returns {void}
 */
function removeCookie(): void {
  document.cookie = `${TOKEN_KEY}=;expires=${new Date(0).toUTCString()};path=/`;
}

export { checkIsLogin, getCookie, removeCookie, setCookie };
