import { get } from '../_/utils/fetch';

export interface IGetBindingInfoParams {
  /** 设备sn码 */
  snCode: string;
}

export interface IGetBindingInfoResponse {
  /** 食堂名称 */
  canteenName?: string;
  /** 设备名称 */
  stallName?: string;
  /** 员工工号 */
  workNum?: string;
  /** 员工姓名 */
  staffName?: string;
}

/**
 * 设备查询绑定信息
 * https://soapi.sheincorp.cn/application/3694/routes/245481/doc
 * @param {IGetBindingInfoParams} params
 * @returns {Promise<IGetBindingInfoResponse>} 返回值
 */
export const getBindingInfoAPI = (params: IGetBindingInfoParams) => {
  return get<IGetBindingInfoResponse>('/canteen/device/setting/getBindingInfo', params);
};
