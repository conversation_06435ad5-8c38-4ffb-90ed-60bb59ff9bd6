/**
 * @file 现场收银
 */
import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '@/_/components/Icon';
import { usePageTitle } from '@/_/hooks';
import { canteenCashierContinuousScanStorage } from '@/_/utils/storage';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { Button, Drawer, Loading, Toast } from 'shineout-mobile';
import AdditionCalculator from '../../canteen/_/components/addition-calculator';
import { getDeviceSNCode } from '../_/utils/snCode';
import styles from './index.less';
import { cancelOrderAPI, getOrderStatusAPI, scanPayAPI } from './services';

// 扩展Window接口
declare global {
  interface Window {
    onScanResult?: (result: string) => void;
  }
}

// 轮询间隔时间
const POLLING_INTERVAL = 1000;

/**
 * 现场收银页面
 */
const OnSiteScanPay = () => {
  usePageTitle(t('现场收银'));
  const navigate = useNavigate();

  // 是否现场收银连续扫码
  const isContinuousScan = Boolean(String(canteenCashierContinuousScanStorage.getItem()) === '1');

  // 连续收银记录的金额
  const [amount, setAmount] = useState<string>('');
  // 是否显示支付弹窗
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  // 支付状态：null-未支付，'paying'-支付中，'success'-支付成功，'cancelled'-已取消
  const [paymentStatus, setPaymentStatus] = useState<null | 'paying' | 'success' | 'cancelled'>(
    null,
  );
  // 倒计时（秒）
  const [countdown, setCountdown] = useState(5);
  // 当前订单信息
  const [currentOrder, setCurrentOrder] = useState<{
    orderNo: string;
    amount: string;
  } | null>(null);

  // 定时器引用
  const countdownTimerRef = useRef<NodeJS.Timeout | null>(null);
  const pollingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 清理定时器
  const clearTimers = usePersistFn(() => {
    if (countdownTimerRef.current) {
      clearTimeout(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }
    if (pollingTimerRef.current) {
      clearTimeout(pollingTimerRef.current);
      pollingTimerRef.current = null;
    }
  });

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      clearTimers();
    };
  }, [clearTimers]);

  // 导航到首页
  const navigateToHome = usePersistFn(() => {
    navigate('/canteen-device/home');
  });

  // 导航到订单核销页面
  const navigateToOrderRedeem = usePersistFn(() => {
    navigate('/canteen-device/order-redeem');
  });

  // 导航到设置页面
  const navigateToSetting = usePersistFn(() => {
    navigate('/canteen-device/setting');
  });

  // 确认金额，触发扫码功能
  const handleConfirmAmount = usePersistFn(() => {
    if (!amount || Number(amount) <= 0) {
      Toast.fail(t('请输入有效金额'));
      return;
    }
    handleScan();
  });

  // 关闭支付弹窗
  const handleClosePaymentModal = usePersistFn(() => {
    clearTimers();
    setShowPaymentModal(false);
    setPaymentStatus(null);
    setCountdown(5);
    setCurrentOrder(null);
  });

  // 开始扫码
  const handleScan = usePersistFn(async () => {
    // if (process.env.NODE_ENV === 'development') {
    //   handleScanResult('mock-qrcode');
    //   return;
    // }

    try {
      window.onScanResult = async (qrcode: string) => {
        if (qrcode) {
          await handleScanResult(qrcode);
        } else {
          Toast.fail(t('扫码失败，请重试'));
        }
      };

      // 调用原生扫码功能
      if (window.androidDevice) {
        window?.androidDevice?.startScan();
      } else {
        Toast.fail(t('设备不支持扫码功能'));
      }
    } catch (error) {
      console.error(t('扫码失败:'), error);
      Toast.fail(t('扫码失败，请重试'));
    }
  });

  // 处理扫码结果
  const handleScanResult = usePersistFn(async (qrcode: string) => {
    try {
      const result = await scanPayAPI({
        qrcode,
        snCode: getDeviceSNCode(),
      });
      if (result && result.orderNo) {
        // 保存订单信息
        setCurrentOrder({
          orderNo: result.orderNo,
          amount: amount,
        });

        // 显示支付弹窗
        setShowPaymentModal(true);
        setPaymentStatus('paying');

        // 开始倒计时和轮询
        startPaymentCountdown();
        startOrderStatusPolling(result.orderNo);
      } else {
        Toast.fail(t('扫码失败，请重试'));
      }
    } catch (error) {
      console.error(t('扫码失败:'), error);
      Toast.fail(t('扫码失败，请重试'));
    }
  });

  // 开始支付倒计时
  const startPaymentCountdown = usePersistFn(() => {
    setCountdown(5);

    // 每秒更新倒计时显示
    const updateCountdown = () => {
      setCountdown((prev) => {
        if (prev <= 1) {
          return 0;
        }
        setTimeout(updateCountdown, 1000);
        return prev - 1;
      });
    };
    setTimeout(updateCountdown, 1000);
  });

  // 开始轮询订单状态
  const startOrderStatusPolling = usePersistFn((orderNo: string) => {
    const pollOrderStatus = async () => {
      try {
        const result = await getOrderStatusAPI({
          orderNo,
          snCode: getDeviceSNCode(),
        });

        if (result && result.orderStatus) {
          // 根据订单状态处理
          if (result.orderStatus === '2') {
            // 已完成
            clearTimers();
            setPaymentStatus('success');
            Toast.success(t('收银成功'));
            return;
          } else if (result.orderStatus === '3') {
            // 已取消
            clearTimers();
            setPaymentStatus('cancelled');
            Toast.success(t('支付已取消'));
            return;
          }
        }

        // 继续轮询
        pollingTimerRef.current = setTimeout(pollOrderStatus, POLLING_INTERVAL);
      } catch (error) {
        console.error(t('查询订单状态失败:'), error);
        // 继续轮询
        pollingTimerRef.current = setTimeout(pollOrderStatus, POLLING_INTERVAL);
      }
    };

    // 开始第一次轮询
    pollingTimerRef.current = setTimeout(pollOrderStatus, POLLING_INTERVAL);
  });

  // 取消支付
  const handleCancelPayment = usePersistFn(async () => {
    if (!currentOrder) return;

    try {
      const result = await cancelOrderAPI({
        orderNo: currentOrder.orderNo,
        snCode: getDeviceSNCode(),
      });

      if (result) {
        clearTimers();
        setPaymentStatus('cancelled');
        Toast.success(t('取消成功'));
      } else {
        Toast.fail(t('取消失败，请重试'));
      }
    } catch (error) {
      console.error(t('取消支付失败:'), error);
      Toast.fail(t('取消失败，请重试'));
    }
  });

  // 继续收银
  const handleContinueCollection = usePersistFn((paymentStatus: 'success' | 'cancelled') => {
    handleClosePaymentModal();
    if (paymentStatus === 'success' && !isContinuousScan) {
      setAmount('');
    }
  });

  // 滑动相关状态
  const [touchStartX, setTouchStartX] = useState(0);
  const [swipeDistance, setSwipeDistance] = useState(0);

  // 处理触摸开始事件
  const handleTouchStart = usePersistFn((e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX);
    setSwipeDistance(0);
  });

  // 处理触摸移动事件
  const handleTouchMove = usePersistFn((e: React.TouchEvent) => {
    if (touchStartX === 0) return;

    const currentX = e.touches[0].clientX;
    const distance = currentX - touchStartX;

    // 只允许向右滑动（正值）
    if (distance > 0) {
      // 限制最大滑动距离为屏幕宽度的40%
      const maxDistance = window.innerWidth * 0.4;
      const limitedDistance = Math.min(distance, maxDistance);
      setSwipeDistance(limitedDistance);
    }
  });

  // 处理触摸结束事件
  const handleTouchEnd = usePersistFn((e: React.TouchEvent) => {
    const endX = e.changedTouches[0].clientX;

    // 计算滑动距离
    const distance = endX - touchStartX;

    // 如果向右滑动超过50像素，则导航到订单核销页面
    if (distance > 50) {
      navigateToOrderRedeem();
    }

    // 重置滑动距离
    setSwipeDistance(0);
  });

  // 计算容器样式
  const containerStyle = {
    transform: `translateX(${swipeDistance}px)`,
  };

  return (
    <div
      className={styles.container}
      style={containerStyle}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* 顶部导航栏和标签页 */}
      <div className={styles.tabs}>
        <div className={styles.backButton} onClick={navigateToHome}>
          <Icon name="arrow-left" fontSize={20} />
        </div>
        <div className={styles.tab} onClick={navigateToOrderRedeem}>
          {t('订单核销')}
        </div>
        <div className={styles.tabActive}>{t('现场收银')}</div>
        <div className={styles.setting} onClick={navigateToSetting}>
          <Icon name="setting" fontSize={20} />
        </div>
      </div>

      {paymentStatus === 'success' || paymentStatus === 'cancelled' ? (
        <>
          {/* 支付结果页面 */}
          <div className={styles.resultPage}>
            <div className={styles.resultIcon}>
              {paymentStatus === 'success' ? (
                <Icon name="m-success-multicolor-shineout-multic" fontSize={60} />
              ) : (
                <Icon name="pc-close-circle" fontSize={60} color="#ff4d4f" />
              )}
            </div>
            <div className={styles.resultText}>
              {paymentStatus === 'success' ? t('收银成功') : t('支付已取消')}
            </div>
            <div className={styles.resultAmount}>¥ {amount || '0.00'}</div>
            <Button
              type="primary"
              onClick={() => handleContinueCollection(paymentStatus)}
              className={styles.continueButton}
            >
              {t('继续收银')}
            </Button>
          </div>
        </>
      ) : (
        <>
          {/* 连续收银已开启提示 */}
          {isContinuousScan && (
            <div className={styles.continuousCollectionTip}>
              <Icon name="pc-info-circle" fontSize={16} color="#197afa" />
              <span>{t('连续收银已开启')}</span>
            </div>
          )}
          {/* 计算器组件 */}
          <AdditionCalculator input={amount} setInput={setAmount} onConfirm={handleConfirmAmount} />
        </>
      )}

      {/* 支付中弹窗 */}
      <Drawer
        visible={showPaymentModal && paymentStatus === 'paying'}
        onClose={handleClosePaymentModal}
        className={styles.scanModal}
      >
        <div className={styles.scanContent}>
          <div className={styles.scanTitle}>
            <span>{t('合计金额')}: </span>
            <span style={{ color: '#F56C0A' }}>¥ {amount || '0.00'}</span>
          </div>
          <div className={styles.payingStatus}>
            <div className={styles.loadingIcon}>
              <Loading fontSize={24} />
            </div>
            <div className={styles.payingText}>{t('支付中...')}</div>
          </div>
          {countdown > 0 ? (
            <div className={styles.countdownText}>{t('{}秒后可取消支付', countdown)}</div>
          ) : (
            <Button
              type="primary"
              onClick={handleCancelPayment}
              className={styles.scanCancelButton}
            >
              {t('取消支付')}
            </Button>
          )}
        </div>
      </Drawer>
    </div>
  );
};

export default OnSiteScanPay;
