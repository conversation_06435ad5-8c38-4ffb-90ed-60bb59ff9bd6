/**
 * @description getCookieValue
 * @param {string} cookieName cookie名称
 * @returns {string} cookie值
 */
function getCookieValue(cookieName: string): string {
  const cookies = document.cookie.split(';');
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();
    if (cookie.indexOf(cookieName + '=') === 0) {
      return cookie.substring(cookieName.length + 1);
    }
  }
  return '';
}

/**
 * @description setCookie
 * @param {string} name cookie名称
 * @param {string} value cookie值
 * @param {number} days 过期天数
 * @returns {void} 无返回值
 */
function setCookie(name: string, value: string, days?: number): void {
  let expires = '';
  if (days) {
    const date = new Date();
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
    expires = '; expires=' + date.toUTCString();
  }
  document.cookie = name + '=' + value + expires + '; path=/';
}

/**
 * @description getUlpToken
 * @returns {unknown} desc
 */
export const getUlpToken = () => {
  return getCookieValue('ulp_token');
};

/**
 * @description setUlpToken
 * @param {unknown} token desc
 * @returns {unknown} desc
 */
export const setUlpToken = (token) => {
  setCookie('ulp_token', token, 1);
};

// 环境变量
const env = process.env.BUILD_TYPE;

const requestUrl = {
  local: 'http://localhost:8080/',
  dev: 'https://lpmpm-dev01.dotfashion.cn/',
  test: 'https://lpmpm-test01.dotfashion.cn/',
  sit: 'https://lpmpm-sit01.dotfashion.cn/',
  prod: 'https://lpmpm.dotfashion.cn/',
};

export const baseUrl = requestUrl[env];

/**
 * @description getBaseUrl
 * @param {unknown} isLocal desc
 * @returns {unknown} desc
 */
export const getBaseUrl = (isLocal) => {
  return isLocal ? requestUrl.local : requestUrl[env];
};
