import { useEffect, useMemo, useState } from 'react';
import { getSessionUserInfo } from '@/utils';
import { useMount } from '@shein-lego/use';
import useOpenDefaultBrowserNotAutoStart from './useOpenDefaultBrowserNotAutoStart';
import { getUlpToken } from './utils';

// 环境变量
const env = process.env.BUILD_TYPE;

/**
 * @description useChromeDebugBtn
 * @returns {unknown} desc
 */
const useChromeDebugBtn = () => {
  const [isShowChromeDebugBtn, setIsShowChromeDebugBtn] = useState(false);
  const { openDefaultBrowser, start } = useOpenDefaultBrowserNotAutoStart();
  const [userInfo, setUserInfo] = useState('{}');
  const [ulpToken, setUlpToken] = useState('');

  const isLegalEnv = useMemo(() => {
    if (env === 'test') {
      return true;
    }
    return false;
  }, []);
  const isInWeChat = !!window.navigator.userAgent.match(/wxwork/i);
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    window.navigator.userAgent,
  );

  const isCanGetInfo = useMemo(() => {
    return userInfo !== '{}' && ulpToken !== '';
  }, [userInfo, ulpToken]);

  useMount(() => {
    setTimeout(() => {
      const userInfo = getSessionUserInfo();
      const ulpToken = getUlpToken();
      setUlpToken(ulpToken);
      setUserInfo(userInfo);
    }, 1000);
  });

  useEffect(() => {
    if (isLegalEnv && isCanGetInfo && isInWeChat && !isMobile) {
      setIsShowChromeDebugBtn(true);
      start();
    }
  }, [isCanGetInfo, isInWeChat, isLegalEnv, isMobile, start]);

  return {
    isShowChromeDebugBtn,
    userInfo,
    ulpToken,
    openDefaultBrowser,
  };
};

export default useChromeDebugBtn;
