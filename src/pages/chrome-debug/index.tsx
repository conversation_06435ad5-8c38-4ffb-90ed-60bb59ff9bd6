import { useCallback, useEffect, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import YQT from '@/_/assets/images/yuanqutong.png';
import { setCode, setSessionUserInfo } from '@/utils';
import { t } from '@shein-bbl/react';
import ImagePreview from '../consult/components/ImagePreview';
import { setUlpToken } from './utils';

// 环境变量
const env = process.env.BUILD_TYPE;

/**
 * @description ChromeDebug
 * @returns {unknown} desc
 */
const ChromeDebug = () => {
  const location = useLocation();
  const Query = new URLSearchParams(location.search);
  const ulpToken = Query.get('ulpToken');
  const userInfo = Query.get('userInfo');

  const setLocalValue = useCallback(() => {
    setSessionUserInfo(userInfo);
    setCode('code');
    setUlpToken(ulpToken);
  }, [ulpToken, userInfo]);

  const isLegalEnv = useMemo(() => {
    if (env === 'test') {
      return true;
    }
    return false;
  }, []);

  useEffect(() => {
    if (isLegalEnv) {
      setLocalValue();
      setTimeout(() => {
        window.location.href = '/#/';
        window.location.reload();
      }, 3000);
    }
  }, [isLegalEnv, setLocalValue]);

  return (
    <>
      <div
        style={{
          display: 'flex',
          height: '100vh',
          width: '100vw',
          maxWidth: '750px',
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'column',
        }}
      >
        <div style={{ width: 80, marginTop: -50 }}>
          <ImagePreview src={YQT} />
        </div>
        <div style={{ margin: '10px 0', color: '#333', fontSize: 16 }}>
          {isLegalEnv ? t('正在跳转中...') : t('非法操作')}
        </div>
      </div>
    </>
  );
};

export default ChromeDebug;
