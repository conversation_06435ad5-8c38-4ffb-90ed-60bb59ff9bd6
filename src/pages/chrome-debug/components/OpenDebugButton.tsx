import { useState } from 'react';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { Button, Drawer, NPicker } from 'shineout-mobile';
import useDraggable from '../useDraggable';
import { getBaseUrl } from '../utils';

/**
 * @description OpenDebugButton
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const OpenDebugButton: React.FC<{
  openDefaultBrowser: (s: string) => void;
  ulpToken: string;
  userInfo: string;
}> = (props) => {
  const { openDefaultBrowser, ulpToken, userInfo } = props;
  const [redirecting, setRedirecting] = useState(false);
  const { btnPos, btnRef, onTouchStart, onTouchEnd, onTouchMove } = useDraggable();
  const [pickerVisible, setPickerVisible] = useState(false);

  const openChromeDebug = usePersistFn((isLocal = false) => {
    setRedirecting(true);
    setTimeout(() => {
      openDefaultBrowser(
        `${getBaseUrl(
          isLocal,
        )}#/chrome-debug?ulpToken=${ulpToken}&userInfo=${window.encodeURIComponent(userInfo)}`,
      );
    }, 2000);
  });

  /**
   * @description handleClick
   * @returns {unknown} desc
   */
  const handleClick = () => {
    setPickerVisible(true);
  };

  return (
    <>
      <Button
        size="small"
        type="warning"
        onClick={handleClick}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
        ref={btnRef}
        style={{
          position: 'fixed',
          right: btnPos.x ?? 16,
          bottom: btnPos.y ?? '10vh',
          zIndex: 1001,
          boxShadow: '0 0 0.61538462em rgba(0, 0, 0, 0.4)',
        }}
      >
        {redirecting ? t('跳转中...') : t('打开Chrome调试')}
      </Button>
      <Drawer visible={pickerVisible} position="bottom">
        <NPicker
          title={t('打开方式')}
          data={[
            {
              name: t('当前环境'),
              value: 'current',
            },
            {
              name: t('8080代理'),
              value: 'proxy',
            },
          ]}
          format="value"
          renderItem={(item) => item.name}
          onOk={(data) => {
            openChromeDebug(data === 'proxy');
            setPickerVisible(false);
          }}
          onCancel={() => {
            setPickerVisible(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default OpenDebugButton;
