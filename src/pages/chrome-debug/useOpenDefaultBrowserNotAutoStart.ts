import { useCallback, useState } from 'react';
import { getHost, post } from '@/utils';
import { usePersistFn } from '@shein-lego/use';
// https://developer.work.weixin.qq.com/document/path/90513
// import wx from 'weixin-js-sdk';

type IUseOpenDefaultBrowserProps = () => {
  openDefaultBrowser: (url: string) => void;
  start: () => void;
};

type IApiResponse = {
  agentId: string;
  signature: string;
  noncestr: string;
  timestamp: string;
  traceId: string;
};

type IApiParams = {
  url: string;
};

/**
 * @description getAppIdByEnv
 * @returns {unknown} desc
 */
const getAppIdByEnv = () => {
  const host = getHost();
  const appIdMap = {
    dev: 'ww6e132f02586f7900',
    test: 'ww6e132f02586f7900',
    sit: 'ww5f025a4c596cbdd8',
    prod: 'wweb4abba124fabaad',
  };
  if (host.indexOf('lpmpm-dev') > -1) {
    return appIdMap.dev;
  } else if (host.indexOf('lpmpm-test') > -1) {
    return appIdMap.test;
  } else if (host.indexOf('lpmpm-sit') > -1) {
    return appIdMap.sit;
  } else if (host.indexOf('lpmpm') > -1) {
    return appIdMap.prod;
  }
  return appIdMap.dev;
};

const useOpenDefaultBrowserNotAutoStart: IUseOpenDefaultBrowserProps = () => {
  const [isChecked, setIsChecked] = useState(false);
  const start = useCallback(() => {
    if (isChecked) {
      return;
    }
    const params: IApiParams = {
      url: location.href.split('#')[0],
    };
    post<IApiResponse>('wechat/msg/jssdk', params).then((data) => {
      wx.config({
        /** 必须这么写，否则wx.invoke调用形式的jsapi会有问题 */
        beta: true,
        /** 开启调试模式,调用的所有api的返回值会在客户端alert出来 */
        debug: false,
        /** 必填，企业微信的corpID，必须是本企业的corpID，不允许跨企业使用 */
        appId: getAppIdByEnv(),
        /** 必填，生成签名的时间戳 */
        timestamp: data.timestamp,
        /** 必填，生成签名的随机串 */
        nonceStr: data.noncestr,
        /** 必填，签名，见 附录-JS-SDK使用权限签名算法 */
        signature: data.signature,
        /** 必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来 */
        jsApiList: ['openDefaultBrowser'],
      });
      wx.ready(() => {
        /** 需要检测的JS接口列表，这一步非必要 */
        wx.checkJsApi({
          jsApiList: ['openDefaultBrowser'],
          success: function (res: Record<string, any>): void {
            console.log('wx.checkJsApi.openDefaultBrowser', res);
            setIsChecked(true);
          },
        });
      });
      wx.error((err) => {
        console.log('wx.error', err);
      });
    });
  }, [isChecked]);

  const openDefaultBrowser = usePersistFn((url) => {
    wx.invoke('openDefaultBrowser', { url }, (res) => {
      if (res.err_msg === 'openDefaultBrowser:ok') {
        wx.closeWindow();
        window.close();
      }
    });
  });
  return { openDefaultBrowser, start };
};

export default useOpenDefaultBrowserNotAutoStart;
