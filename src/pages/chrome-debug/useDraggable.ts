import { useRef } from 'react';
import { useObjectState } from '@/_/hooks';
import { useMount } from 'ahooks';
import { isNil } from 'lodash';

const useDraggable = () => {
  const switchPosRef = useRef({
    hasMoved: false, // exclude click event
    x: 0, // right
    y: 0, // bottom
    startX: 0,
    startY: 0,
    endX: 0,
    endY: 0,
  });
  const [btnPos, setBtnPos] = useObjectState({
    x: 0,
    y: 80,
  });
  const btnRef = useRef(null);

  /**
   * Get an safe [x, y] position for switch button
   */
  const _getSwitchButtonSafeAreaXY = (x: number, y: number) => {
    const docWidth = Math.max(document.documentElement.offsetWidth, window.innerWidth);
    const docHeight = Math.max(document.documentElement.offsetHeight, window.innerHeight);
    // check edge
    const btnSwitch = btnRef.current;
    if (x + btnSwitch.offsetWidth > docWidth) {
      // eslint-disable-next-line no-param-reassign
      x = docWidth - btnSwitch.offsetWidth;
    }
    if (y + btnSwitch.offsetHeight > docHeight) {
      // eslint-disable-next-line no-param-reassign
      y = docHeight - btnSwitch.offsetHeight;
    }
    if (x < 0) {
      // eslint-disable-next-line no-param-reassign
      x = 0;
    }
    if (y < 20) {
      // eslint-disable-next-line no-param-reassign
      y = 20;
    } // safe area for iOS Home indicator
    return [x, y];
  };

  const setSwitchPosition = (switchX: number, switchY: number) => {
    // eslint-disable-next-line no-param-reassign
    [switchX, switchY] = _getSwitchButtonSafeAreaXY(switchX, switchY);
    switchPosRef.current.x = switchX;
    switchPosRef.current.y = switchY;
    setBtnPos({
      x: switchX,
      y: switchY,
    });
    localStorage.setItem('chrome_debug_btn_x', switchX + '');
    localStorage.setItem('chrome_debug_btn_y', switchY + '');
  };

  useMount(() => {
    if (btnRef.current) {
      const initSwitchX = localStorage.getItem('chrome_debug_btn_x');
      const initSwitchY = localStorage.getItem('chrome_debug_btn_y');
      setSwitchPosition(
        !isNil(initSwitchX) && !Number.isNaN(+initSwitchX) ? +initSwitchX : 16,
        !isNil(initSwitchY) && !Number.isNaN(initSwitchY) ? +initSwitchY : window.innerHeight * 0.3,
      );
    }
  });

  /**
   * @description onTouchStart
   * @param {unknown} e desc
   * @returns {unknown} desc
   */
  const onTouchStart = (e) => {
    switchPosRef.current.startX = e.touches[0].pageX;
    switchPosRef.current.startY = e.touches[0].pageY;
    switchPosRef.current.hasMoved = false;
  };

  /**
   * @description onTouchEnd
   * @returns {unknown} desc
   */
  const onTouchEnd = () => {
    if (!switchPosRef.current.hasMoved) {
      return;
    }
    switchPosRef.current.startX = 0;
    switchPosRef.current.startY = 0;
    switchPosRef.current.hasMoved = false;
    setSwitchPosition(switchPosRef.current.endX, switchPosRef.current.endY);
  };

  /**
   * @description onTouchMove
   * @param {unknown} e desc
   * @returns {unknown} desc
   */
  const onTouchMove = (e) => {
    if (e.touches.length <= 0) {
      return;
    }
    const offsetX = e.touches[0].pageX - switchPosRef.current.startX,
      offsetY = e.touches[0].pageY - switchPosRef.current.startY;
    let x = Math.floor(switchPosRef.current.x - offsetX),
      y = Math.floor(switchPosRef.current.y - offsetY);
    [x, y] = _getSwitchButtonSafeAreaXY(x, y);
    setBtnPos({
      x,
      y,
    });
    switchPosRef.current.endX = x;
    switchPosRef.current.endY = y;
    switchPosRef.current.hasMoved = true;
    e.preventDefault();
  };
  return {
    btnPos,
    btnRef,
    onTouchStart,
    onTouchEnd,
    onTouchMove,
  };
};

export default useDraggable;
