import React, { useState } from 'react';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { Tab } from 'shineout-mobile';
import Done from './components/Done';
import Wait from './components/Wait';
import styles from './index.less';

/**
 * @description Main
 * @returns {unknown} desc
 */
const Main: React.FC = () => {
  usePageTitle(t('审批列表'));
  const [activeTab, setActiveTab] = useState('wait');

  return (
    <div className={styles.page}>
      <Tab
        active={activeTab}
        onChange={(tab) => {
          setActiveTab(tab);
        }}
      >
        <Tab.Panel name="wait" tab={t('待审批')}>
          {activeTab === 'wait' && <Wait />}
        </Tab.Panel>
        <Tab.Panel name="done" tab={t('已审批')}>
          {activeTab === 'done' && <Done />}
        </Tab.Panel>
      </Tab>
    </div>
  );
};

export default Main;
