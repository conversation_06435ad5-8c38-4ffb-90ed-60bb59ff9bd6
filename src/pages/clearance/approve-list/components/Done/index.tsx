import React, { useRef } from 'react';
import Icon from '@/_/components/Icon';
import { useObjectState } from '@/_/hooks';
import ApplyCardList from '@/pages/clearance/components/ApplyCardList';
import { formatDate } from '@/utils';
import { t } from '@shein-bbl/react';
import { useMount } from '@shein-lego/use';
import moment from 'moment';
import { Calendar, Toast } from 'shineout-mobile';
import { getList } from '../../api';
import { EApproveStatus, IApproveListItem } from '../../interfaces';
import styles from './index.less';

/**
 * @description Main
 * @returns {unknown} desc
 */
const Main: React.FC = () => {
  const [state, setState] = useObjectState({
    dateRange: [moment().subtract(1, 'months'), moment()],
    showCalendar: false,
    listData: [] as IApproveListItem[],
  });
  const pageNumberRef = useRef(1);
  const noMoreRef = useRef(false);
  const isLoadingListRef = useRef(false);

  /**
   * @description handleGetList
   * @param {unknown} startDate desc
   * @param {unknown} endDate desc
   * @param {unknown} fresh desc
   * @returns {unknown} desc
   */
  const handleGetList = (startDate: string, endDate: string, fresh?: boolean) => {
    if (noMoreRef.current) {
      return;
    }
    getList({
      approveStatus: EApproveStatus.DONE,
      pageSize: 10,
      pageNumber: pageNumberRef.current,
      submitTimeFrom: startDate,
      submitTimeTo: endDate,
    })
      .then((res) => {
        const newData = state.listData.concat(res.rows);
        setState({
          listData: fresh ? res.rows : newData,
        });
        if (newData.length === res.total || !res.rows.length) {
          noMoreRef.current = true;
        }
      })
      .finally(() => {
        isLoadingListRef.current = false;
      });
  };

  useMount(() => {
    handleGetList(formatDate(state.dateRange[0]), formatDate(state.dateRange[1]));
  });

  /**
   * @description handleLoadMore
   * @returns {unknown} desc
   */
  const handleLoadMore = () => {
    if (isLoadingListRef.current) {
      return;
    }
    isLoadingListRef.current = true;
    pageNumberRef.current = pageNumberRef.current + 1;
    handleGetList(formatDate(state.dateRange[0]), formatDate(state.dateRange[1]));
  };

  return (
    <div className={styles.page}>
      <div
        className={styles.timeLine}
        onClick={() =>
          setState({
            showCalendar: true,
          })
        }
      >
        <Icon name="calendar" />
        <span>
          {`${formatDate(state.dateRange[0], false, 'YYYY/MM/DD')} -
          ${formatDate(state.dateRange[1], false, 'YYYY/MM/DD')}`}
        </span>
      </div>
      <ApplyCardList data={state.listData} showApplicant onLoadMore={handleLoadMore} />
      <Calendar
        round
        type="range"
        visible={state.showCalendar}
        confirmButtonText={t('确认')}
        minDate={moment().subtract(3, 'years').toDate()}
        maxDate={moment().toDate()}
        defaultPosition={moment().toDate()}
        drawer={{ position: 'bottom', height: 486, closeable: true }}
        onConfirm={(dates) => {
          console.log('dates', dates);
          if ((dates || []).length !== 2) {
            return Toast.fail(t('请选择时间范围'));
          }
          setState({
            dateRange: [moment(dates[0]), moment(dates[1])],
            showCalendar: false,
          });
          noMoreRef.current = false;
          pageNumberRef.current = 1;
          handleGetList(formatDate(moment(dates[0])), formatDate(moment(dates[1])), true);
        }}
        onCancel={() =>
          setState({
            showCalendar: false,
          })
        }
        range={31}
      />
    </div>
  );
};

export default Main;
