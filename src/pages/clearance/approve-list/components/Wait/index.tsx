import React, { useRef, useState } from 'react';
import { useObjectState } from '@/_/hooks';
import ApplyCardList from '@/pages/clearance/components/ApplyCardList';
import { t } from '@shein-bbl/react';
import { useMount } from '@shein-lego/use';
import { Button, Dialog, Radio, Toast } from 'shineout-mobile';
import { batchApprove, getList } from '../../api';
import { EApprovalFlag, EApproveStatus, IApproveListItem } from '../../interfaces';
import styles from './index.less';

/**
 * @description Main
 * @returns {unknown} desc
 */
const Main: React.FC = () => {
  const [listData, setListData] = useState<IApproveListItem[]>([]);
  const pageNumberRef = useRef(1);
  const noMoreRef = useRef(false);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [state, setState] = useObjectState({
    approveLoading: false,
  });
  const isLoadingListRef = useRef(false);

  /**
   * @description handleGetList
   * @returns {unknown} desc
   */
  const handleGetList = () => {
    if (noMoreRef.current) {
      return;
    }
    getList({
      approveStatus: EApproveStatus.WAIT,
      pageSize: 10,
      pageNumber: pageNumberRef.current,
    })
      .then((res) => {
        const newData = listData.concat(res.rows);
        setListData(newData);
        if (newData.length === res.total || !res.rows.length) {
          noMoreRef.current = true;
        }
      })
      .finally(() => {
        isLoadingListRef.current = false;
      });
  };

  useMount(() => {
    handleGetList();
  });

  /**
   * @description handleLoadMore
   * @returns {unknown} desc
   */
  const handleLoadMore = () => {
    if (isLoadingListRef.current) {
      return;
    }
    isLoadingListRef.current = true;
    pageNumberRef.current = pageNumberRef.current + 1;
    handleGetList();
  };

  /**
   * @description handleBatchApprove
   * @returns {unknown} desc
   */
  const handleBatchApprove = () => {
    Dialog.confirm({
      title: t('批量通过'),
      message: t('是否通过所有已选择的申请？'),
      onOk: () => {
        setState({
          approveLoading: true,
        });
        batchApprove({
          approvalFlag: EApprovalFlag.PASS,
          releaseApplyIdList: selectedIds,
        })
          .then(() => {
            Toast.success(t('审批成功'));
            setListData((prev) => prev.filter((item) => !selectedIds.includes(item.id)));
            setSelectedIds([]);
          })
          .finally(() => {
            setState({
              approveLoading: false,
            });
          });
      },
    });
  };

  return (
    <div className={styles.page}>
      <ApplyCardList
        showApplicant
        data={listData}
        onLoadMore={handleLoadMore}
        checkedIds={selectedIds}
        onSelectChange={(id, checked) => {
          if (checked) {
            setSelectedIds((prev) => prev.concat(id));
          } else {
            setSelectedIds((prev) => prev.filter((_id) => _id !== id));
          }
        }}
      />
      <footer className={styles.footer}>
        <div
          onClick={() => {
            if (selectedIds.length) {
              setSelectedIds([]);
            } else {
              setSelectedIds(listData.map((item) => item.id));
            }
          }}
        >
          <Radio
            checked={!!listData.length && selectedIds.length === listData.length}
            disabled={!listData.length}
            shape="square"
          >
            {t('全选')}
          </Radio>
        </div>
        <Button
          type="primary"
          round
          onClick={handleBatchApprove}
          disabled={!selectedIds.length}
          loading={state.approveLoading}
          loadingText={t('批量通过')}
        >
          {t('批量通过')}
        </Button>
      </footer>
    </div>
  );
};

export default Main;
