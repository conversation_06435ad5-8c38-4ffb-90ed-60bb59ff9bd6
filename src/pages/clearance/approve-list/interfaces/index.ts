import { EApplyStatus, EDestinationType } from '@/pages/clearance/interfaces';

export enum EApproveStatus {
  /** 待审批 */
  WAIT = 1,
  /** 已审批 */
  DONE = 2,
}

export interface IGetListReq {
  pageSize: number;
  pageNumber: number;
  /** 1-待审批，2-已审批 */
  approveStatus: EApproveStatus;
  /** 提单时间的开始查询时间 */
  submitTimeFrom?: string;
  /** 提单时间的结束查询时间 */
  submitTimeTo?: string;
}

export interface IApproveListItem {
  id: number;
  releaseParkId: number;
  releaseParkName: string;
  releaseSubWarehouseId: number;
  releaseSubWarehouseName: string;
  destinationParkId: number;
  destinationParkName: string;
  destinationSubWarehouseId: number;
  destinationSubWarehouseName: string;
  applicantWorkNum: string;
  applicantName: string;
  submitTime: string;
  applyReason: string;
  /** 待审批列表的状态值 */
  applyStatus: EApplyStatus;
  /** 已审批列表的状态值 */
  actionCode: number;
  destinationType: EDestinationType;
}

export enum EApprovalFlag {
  PASS = 1,
  REJECT = 0,
}
