import { post } from '@/utils';
import { EApprovalFlag, IApproveListItem, IGetListReq } from '../interfaces';

/**
 * 获取审批列表
 * */
export const getList = (params: IGetListReq) => {
  return post<{ total: number; rows: IApproveListItem[] }>(
    '/cargo/releaseApply/selectProcessPage',
    params,
  );
};

/**
 * 批量审批
 * */
export const batchApprove = (params: {
  releaseApplyIdList: number[];
  approvalFlag: EApprovalFlag;
}) => {
  return post('/cargo/releaseApply/batchApproval', params);
};
