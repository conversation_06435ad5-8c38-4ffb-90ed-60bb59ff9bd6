.card {
  position: relative;
  background-color: #fff;
  border-radius: 9px;
  box-shadow: 0 2px 4px 0 rgb(20 23 55 / 5%);

  & + & {
    margin-top: 7px;
  }
}

.titleLine {
  display: flex;
  width: 100%;
  padding: 12px;
  font-size: 14px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

  :global {
    .sm-radio {
      margin-right: 12px;
    }

    .sm-radio-icon {
      border-radius: 50%;
    }
  }
}

.timeStr {
  margin-left: 7px;
  flex: 1;
}

.warehouse {
  display: flex;
  width: 100%;
  padding: 8px 12px;
  font-size: 15px;
  font-weight: 500;
  color: #35383d;
  flex-direction: row;
}

.indicator {
  flex: 1.5;
  margin: 0 5%;
  align-self: flex-start;
}

.warehouseName {
  //overflow: hidden;
  //text-overflow: ellipsis;
  //white-space: nowrap;
  flex: 1;
  line-height: 1.375;
}

.subWarehouse {
  display: flex;
  flex-direction: row;
  padding: 4px 12px 12px;
  justify-content: space-between;
  font-size: 13px;
  color: #35383d;
}

.applyReason {
  display: flex;
  padding: 8px 12px;
  margin-top: 12px;
  color: #999da8;
  border-top: 1px solid #e8ebf0;
  flex-direction: row;

  & > div:first-child {
    white-space: nowrap;
  }
}

.invalid {
  color: #999da8;
}

.radioClickArea {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
}

.applicant {
  padding: 0 12px;
  margin-top: 12px;
  font-size: 12px;
  color: #666c7c;
}
