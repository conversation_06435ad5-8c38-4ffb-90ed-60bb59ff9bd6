import React, { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '@/_/components/Icon';
import { useObjectState } from '@/_/hooks';
import { EApplyStatus } from '@/pages/clearance/interfaces';
import { formatDate } from '@/utils';
import { t } from '@shein-bbl/react';
import { useUpdateEffect } from '@shein-lego/use';
import classNames from 'classnames';
import { Radio } from 'shineout-mobile';
import StatusTag from '../StatusTag';
import WarehouseInfo from '../WarehouseInfo';
import styles from './index.less';
import { IApplyCardProps } from './interfaces';

/**
 * @description Main
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Main: React.FC<IApplyCardProps> = (props) => {
  const { data, onSelectChange, checked, showApplicant } = props;
  const navigate = useNavigate();
  const [state, setState] = useObjectState({
    checked: !!checked,
  });

  const selectable = useMemo(() => {
    return typeof onSelectChange === 'function';
  }, [onSelectChange]);

  const isInvalid = useMemo(() => {
    return data.applyStatus === EApplyStatus.CANCEL;
  }, [data.applyStatus]);

  const showDate = useMemo(() => {
    return (
      ![EApplyStatus.WAIT_SUBMIT, EApplyStatus.REJECT, EApplyStatus.WITHDRAW].includes(
        data.applyStatus,
      ) || showApplicant
    );
  }, [data.applyStatus, showApplicant]);

  useUpdateEffect(() => {
    setState({
      checked: checked,
    });
  }, [checked]);

  const invalidClassName = classNames({ [styles.invalid]: isInvalid });

  /**
   * @description handleDetailJump
   * @returns {unknown} desc
   */
  const handleDetailJump = () => {
    const toEdit = [EApplyStatus.WAIT_SUBMIT, EApplyStatus.WITHDRAW, EApplyStatus.REJECT].includes(
      data.applyStatus,
    );
    if (toEdit && !showApplicant) {
      navigate(`/clearance/edit/${data.id}`);
      return;
    }
    if (selectable || showApplicant) {
      navigate(`/clearance/approve/${data.id}`);
      return;
    }
    navigate(`/clearance/detail/${data.id}`);
  };

  return (
    <div className={styles.card} onClick={handleDetailJump}>
      <div className={styles.titleLine}>
        {selectable && (
          <div
            onClick={(e) => {
              e.stopPropagation();
              const newChecked = !state.checked;
              setState({
                checked: newChecked,
              });
              onSelectChange?.(data?.id, newChecked);
            }}
          >
            <div className={styles.radioClickArea} />
            <Radio shape="square" checked={state.checked} />
          </div>
        )}
        <div>
          <StatusTag status={data.applyStatus} />
        </div>
        {showDate && (
          <div className={classNames(styles.timeStr, invalidClassName)}>
            {formatDate(data.submitTime, false, 'YYYY/MM/DD HH:mm:ss')}
          </div>
        )}
        <div className={styles.arrow}>
          <Icon name="m-arrow-right-2" />
        </div>
      </div>
      <WarehouseInfo
        data={{
          invalid: isInvalid,
          releaseParkName: data.releaseParkName,
          releaseSubWarehouseName: data.releaseSubWarehouseName,
          destinationType: data.destinationType,
          destinationParkName: data.destinationParkName,
          destinationSubWarehouseName: data.destinationSubWarehouseName,
        }}
      />
      {showApplicant && (
        <div className={styles.applicant}>
          {t('申请人：')}
          {`${data.applicantName}(${data.applicantWorkNum})`}
        </div>
      )}
      <div className={classNames(styles.applyReason, invalidClassName)}>
        <div>{t('申请说明：')}</div>
        <div className="truncate">{data.applyReason || '—'}</div>
      </div>
    </div>
  );
};

export default Main;
