import { EApplyStatus, EDestinationType } from '@/pages/clearance/add/interfaces';

export interface IApplyCardProps {
  data: {
    /** 记录ID，用于选择、跳转详情等 */
    id: number;
    /** 申请状态 */
    applyStatus: EApplyStatus;
    /** 目的地类型 */
    destinationType: EDestinationType;
    /** 放行园区 */
    releaseParkName: string;
    /** 放行仓 */
    releaseSubWarehouseName: string;
    /** 目的地园区 */
    destinationParkName: string;
    /** 目的地仓 */
    destinationSubWarehouseName: string;
    /** 申请原因 */
    applyReason: string;
    /** 提交时间 */
    submitTime: string;
    /** 申请人工号 */
    applicantWorkNum?: string;
    /** 申请人英文名 */
    applicantName?: string;
  };
  /** 是否选中，当由外部受控时使用 */
  checked?: boolean;
  /** 单选框选择状态切换 */
  onSelectChange?: (id: number, checked: boolean) => void;
  /** 是否显示申请人 */
  showApplicant?: boolean;
}
