import React from 'react';
import Icon from '@/_/components/Icon';
import classNames from 'classnames';
import styles from './index.less';

interface IMainProps {
  className?: string;
  style?: React.CSSProperties;
}

/**
 * @description Main
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Main: React.FC<IMainProps> = (props) => {
  const { className, style } = props;

  return (
    <div className={classNames(styles.indicator, className)} style={style}>
      <div className={styles.dot}></div>
      <div className={styles.arrow}>
        <Icon name="m-arrow-fill-right" color="#ccd0d7" fontSize={26} />
      </div>
      <div className={styles.dot}></div>
      <div className={styles.line}></div>
    </div>
  );
};

export default Main;
