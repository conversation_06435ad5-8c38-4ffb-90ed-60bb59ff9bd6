import React from 'react';
import Icon from '@/_/components/Icon';
import { t } from '@shein-bbl/react';

interface IMainProps {
  /** icon name */
  icon?: string;
  /** 无数据描述 */
  desc?: string;
}

/**
 * @description Main
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Main: React.FC<IMainProps> = (props) => {
  const { icon = 'chawushuju', desc = t('无数据') } = props;

  return (
    <div className="flex h-full flex-col justify-center items-center">
      <Icon name={icon} fontSize={64} color="#999da8" />
      <div style={{ color: '#999da8', marginTop: 6 }}>{desc}</div>
    </div>
  );
};

export default Main;
