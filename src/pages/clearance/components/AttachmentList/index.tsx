import React from 'react';
import { useObjectState } from '@/_/hooks';
import classNames from 'classnames';
import { ImagePreviewer } from 'shineout-mobile';

interface IAttachmentListProps {
  data: { fileName?: string; fileUrl?: string }[];
  className?: string;
}

const Main: React.FC<IAttachmentListProps> = (props) => {
  const { data, className } = props;
  const [state, setState] = useObjectState({
    showImagePreview: false,
    currentPreviewImage: '',
  });

  return (
    <>
      <div className={classNames('sm-upload p-md', className)}>
        {(data || [])?.map((item, index) => (
          <div
            className="sm-upload-image"
            /* eslint-disable-next-line react/no-array-index-key */
            key={index}
            onClick={() =>
              setState({
                showImagePreview: true,
                currentPreviewImage: item.fileUrl,
              })
            }
          >
            <img src={item.fileUrl} alt="" />
          </div>
        ))}
      </div>
      <ImagePreviewer
        images={[state.currentPreviewImage]}
        visible={state.showImagePreview}
        onClose={() =>
          setState({
            showImagePreview: false,
          })
        }
      />
    </>
  );
};

export default Main;
