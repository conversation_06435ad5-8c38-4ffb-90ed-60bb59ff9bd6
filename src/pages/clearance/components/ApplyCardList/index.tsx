import React, { useState } from 'react';
import styles from '@/pages/clearance/apply-list/index.less';
import ApplyCard from '@/pages/clearance/components/ApplyCard';
import Empty from '@/pages/clearance/components/Empty';
import { useUpdateEffect } from '@shein-lego/use';
import { IApplyCardProps } from '../ApplyCard/interfaces';

interface IApplyCardListProps extends Omit<IApplyCardProps, 'data' | 'checked'> {
  onLoadMore?: () => void;
  data: IApplyCardProps['data'][];
  /** 选中的列表 */
  checkedIds?: number[];
}

const Main: React.FC<IApplyCardListProps> = (props) => {
  const { onLoadMore, data, onSelectChange, checkedIds, showApplicant } = props;
  const [dataChange, setDataChange] = useState(false);

  useUpdateEffect(() => {
    if (!dataChange) {
      setDataChange(true);
    }
  }, [data]);

  return (
    <div
      className={styles.list}
      onScroll={(e) => {
        // 判断元素是否滚动到了底部
        const { scrollHeight, scrollTop, clientHeight } = e.target as HTMLDivElement;
        const bottomDistance = scrollHeight - scrollTop - clientHeight;
        // 由于安卓机的scrollTop会有小数点，所以这里判断的时候不能用 === 0
        if (bottomDistance <= 10) {
          onLoadMore?.();
        }
      }}
    >
      {data.length > 0 &&
        data.map((item) => (
          <ApplyCard
            key={item.id}
            showApplicant={!!showApplicant}
            onSelectChange={onSelectChange}
            data={item}
            checked={checkedIds ? checkedIds.includes(item.id) : undefined}
          />
        ))}
      {!data.length && dataChange && <Empty />}
    </div>
  );
};

export default Main;
