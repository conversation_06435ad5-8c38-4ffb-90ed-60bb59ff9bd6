import React, { useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '@/_/components/Icon';
import { useObjectState, usePageTitle } from '@/_/hooks';
import FooterContainer from '@/pages/clearance/add/components/FooterContainer';
import { EApplyStatus } from '@/pages/clearance/interfaces';
import { formatDate } from '@/utils';
import { t } from '@shein-bbl/react';
import { useMount } from '@shein-lego/use';
import moment from 'moment';
import { Button, Calendar, Tab } from 'shineout-mobile';
import ApplyCardList from '../components/ApplyCardList';
import TabBar from '../components/TabBar';
import { getApplyList } from './api';
import styles from './index.less';
import { IListItem } from './interfaces';

type ITabNames = 'waitSubmit' | 'approving' | 'finished';
type TabValue<T> = {
  [K in ITabNames]: T;
};

const TabStatuses: { [K in ITabNames]: EApplyStatus[] } = {
  waitSubmit: [EApplyStatus.WAIT_SUBMIT, EApplyStatus.REJECT, EApplyStatus.WITHDRAW],
  approving: [EApplyStatus.APPROVAL],
  finished: [EApplyStatus.FINISHED, EApplyStatus.CANCEL],
};

/**
 * @description Main
 * @returns {unknown} desc
 */
const Main: React.FC = () => {
  usePageTitle(t('放行申请列表'));
  const navigate = useNavigate();
  const [state, setState] = useObjectState({
    activeTab: 'waitSubmit' as ITabNames,
    dateRange: [moment().subtract(1, 'months'), moment()],
    showCalendar: false,
    tabList: {
      waitSubmit: [],
      approving: [],
      finished: [],
    } as TabValue<IListItem[]>,
  });
  const tabCurrentPageNumber = useRef<TabValue<number>>({
    waitSubmit: 1,
    approving: 1,
    finished: 1,
  });
  const noMoreRef = useRef<TabValue<boolean>>({
    waitSubmit: false,
    approving: false,
    finished: false,
  });
  const hasInitGetListRef = useRef<TabValue<boolean>>({
    waitSubmit: false,
    approving: false,
    finished: false,
  });
  const isLoadingListRef = useRef<TabValue<boolean>>({
    waitSubmit: false,
    approving: false,
    finished: false,
  });

  /**
   * @description resetTabRelativeData
   * @returns {unknown} desc
   */
  const resetTabRelativeData = () => {
    tabCurrentPageNumber.current = {
      waitSubmit: 1,
      approving: 1,
      finished: 1,
    };
    noMoreRef.current = {
      waitSubmit: false,
      approving: false,
      finished: false,
    };
    hasInitGetListRef.current = {
      waitSubmit: false,
      approving: false,
      finished: false,
    };
  };

  /**
   * @description resetInactiveTabList
   * @returns {unknown} desc
   */
  const resetInactiveTabList = () => {
    const activeTab = state.activeTab;
    const newTabList = {} as TabValue<IListItem[]>;
    Object.keys(state.tabList).forEach((key) => {
      if (key !== activeTab) {
        newTabList[key] = [];
      } else {
        newTabList[key] = state.tabList[key];
      }
    });
    setState({
      tabList: newTabList,
    });
  };

  /**
   * @description getTabList
   * @param {unknown} activeTab desc
   * @param {unknown} startDate desc
   * @param {unknown} endDate desc
   * @param {unknown} fresh desc
   * @returns {unknown} desc
   */
  const getTabList = (
    activeTab: ITabNames,
    startDate: string,
    endDate: string,
    fresh?: boolean,
  ) => {
    const PAGE_SIZE = 10;
    if (noMoreRef.current[activeTab]) {
      return;
    }
    getApplyList({
      applyStatuses: TabStatuses[activeTab],
      pageNumber: tabCurrentPageNumber.current[activeTab],
      pageSize: PAGE_SIZE,
      submitDateFrom: startDate,
      submitDateTo: endDate,
    })
      .then((res) => {
        const newTabList = {
          ...state.tabList,
          [activeTab]: fresh ? res.rows : state.tabList[activeTab].concat(res.rows),
        };
        setState({
          tabList: newTabList,
        });
        if (newTabList[activeTab].length === res.total) {
          noMoreRef.current = {
            ...noMoreRef.current,
            [activeTab]: true,
          };
        }
        hasInitGetListRef.current = {
          ...hasInitGetListRef.current,
          [activeTab]: true,
        };
      })
      .finally(() => {
        isLoadingListRef.current[activeTab] = false;
      });
  };

  useMount(() => {
    getTabList(state.activeTab, formatDate(state.dateRange[0]), formatDate(state.dateRange[1]));
  });

  /**
   * @description handleTabChange
   * @param {unknown} activeTab desc
   * @returns {unknown} desc
   */
  const handleTabChange = (activeTab: ITabNames) => {
    setState({
      activeTab: activeTab,
    });
    tabCurrentPageNumber.current[activeTab] = 1;
    noMoreRef.current[activeTab] = false;
    hasInitGetListRef.current[activeTab] = false;
    if (!hasInitGetListRef.current[activeTab]) {
      getTabList(activeTab, formatDate(state.dateRange[0]), formatDate(state.dateRange[1]), true);
    }
  };

  /**
   * @description handleLoadMore
   * @returns {unknown} desc
   */
  const handleLoadMore = () => {
    const activeTab = state.activeTab;
    if (isLoadingListRef.current[activeTab]) {
      return;
    }
    tabCurrentPageNumber.current = {
      ...tabCurrentPageNumber.current,
      [activeTab]: tabCurrentPageNumber.current[activeTab] + 1,
    };
    isLoadingListRef.current[activeTab] = true;
    getTabList(activeTab, formatDate(state.dateRange[0]), formatDate(state.dateRange[1]));
  };

  return (
    <div className={styles.page}>
      <div
        className={styles.timeLine}
        onClick={() =>
          setState({
            showCalendar: true,
          })
        }
      >
        <Icon name="calendar" color="#35383D" />
        <span>
          {`${formatDate(state.dateRange[0], false, 'YYYY/MM/DD')} -
          ${formatDate(state.dateRange[1], false, 'YYYY/MM/DD')}`}
        </span>
      </div>
      <Tab active={state.activeTab} onChange={handleTabChange}>
        <Tab.Panel name="waitSubmit" tab={t('待提交')}>
          {state.activeTab === 'waitSubmit' && (
            <ApplyCardList data={state.tabList.waitSubmit} onLoadMore={handleLoadMore} />
          )}
        </Tab.Panel>
        <Tab.Panel name="approving" tab={t('审批中')}>
          {state.activeTab === 'approving' && (
            <ApplyCardList data={state.tabList.approving} onLoadMore={handleLoadMore} />
          )}
        </Tab.Panel>
        <Tab.Panel name="finished" tab={t('已完结')}>
          {state.activeTab === 'finished' && (
            <ApplyCardList data={state.tabList.finished} onLoadMore={handleLoadMore} />
          )}
        </Tab.Panel>
      </Tab>
      <FooterContainer className="bg-transparent mt-0">
        <Button type="primary" round onClick={() => navigate('/clearance/add')}>
          {t('新增')}
        </Button>
      </FooterContainer>
      <TabBar active="apply" inactivePageRoute="/clearance/release-list" />
      <Calendar
        round
        type="range"
        visible={state.showCalendar}
        confirmButtonText={t('确认')}
        minDate={moment().subtract(3, 'years').toDate()}
        maxDate={moment().toDate()}
        defaultPosition={moment().toDate()}
        drawer={{ position: 'bottom', height: 486, closeable: true }}
        onConfirm={(dates) => {
          setState({
            dateRange: [moment(dates[0]), moment(dates[1])],
            showCalendar: false,
          });
          resetTabRelativeData();
          resetInactiveTabList();
          getTabList(
            state.activeTab,
            formatDate(moment(dates[0])),
            formatDate(moment(dates[1])),
            true,
          );
        }}
        onCancel={() =>
          setState({
            showCalendar: false,
          })
        }
      />
    </div>
  );
};

export default Main;
