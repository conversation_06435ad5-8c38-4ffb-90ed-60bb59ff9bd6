import { EApplyStatus, EDestinationType } from '@/pages/clearance/interfaces';

export interface IGetListReq {
  /** 0-待发起，1-审批中，2-已驳回，3-已完结，4-已撤回，5-已作废 */
  applyStatuses: EApplyStatus[];
  /** yyyy-MM-dd */
  submitDateFrom: string;
  /** yyyy-MM-dd */
  submitDateTo: string;
  pageSize: number;
  pageNumber: number;
}

export interface IListItem {
  id: number;
  orderNum: string;
  applicantWorkNum: string;
  applicantEnName: string;
  currentAssigneeWorkNum: string;
  currentAssigneeEnName: string;
  applicantDepartmentAllName: string;
  releaseParkName: string;
  releaseSubWarehouseName: string;
  /** 园区名称+仓库名称 */
  releaseName: string;
  destinationParkName: string;
  destinationSubWarehouseName: string;
  /** 园区名称+仓库名称 */
  destinationName: string;
  /** yyyy-MM-dd HH:mm:ss */
  submitTime: string;
  /** yyyy/MM/dd HH:mm:ss */
  submitTimeStr: string;
  applyReason: string;
  /** 字符串，多个用"、"分割 */
  cargoTypeDescription: string;
  /** 0-待发起，1-审批中，2-已驳回，3-已完结，4-已撤回，5-已作废 */
  applyStatus: number;
  /** -1-已作废，0-待出库，1-待核销，2-已核销 */
  passStatus: number;
  /** 0-库外， 1-仓库 */
  destinationType: EDestinationType;
  /** 1-审批人审批，2-白名单跳过 */
  finishedType: string;
}
