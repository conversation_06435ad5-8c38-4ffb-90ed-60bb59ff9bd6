.page {
  display: flex;
  height: 100vh;
  background-color: #f7f8fa;
  flex-direction: column;

  :global {
    .sm-tabs {
      display: flex;
      min-height: 0;
      background-color: #f7f8fa;
      flex: 1;
      flex-direction: column;
    }

    .sm-tabs-content {
      min-height: 0;
      flex: 1;
      overflow: auto;

      & > div {
        height: 100%;
      }
    }
  }
}

.timeLine {
  padding: 8px 12px;
  font-family: PingFangSC-Regular, 'PingFang SC', sans-serif;
  font-weight: 500;
  color: #35383d;
  background-color: #fff;

  :global {
    .so-icon {
      margin-right: 6px;
    }
  }
}

.list {
  height: 100%;
  padding: 6px 12px;
  overflow: auto;
  scroll-behavior: smooth;
}
