import { t } from '@shein-bbl/react';
export interface IAttachmentsItem {
  id?: number;
  fileName: string;
  ossKey?: string;
  fileUrl: string;
}

export type IAttachments = IAttachmentsItem[];

export interface IDetailsItem {
  id?: number;
  /** 用于内部渲染 */
  _uid?: number;
  /** 0-其他 */
  cargoTypeId?: number;
  cargoId?: number;
  cargoNum?: number;
  cargoUnitName?: string;
  cargoUnitId?: number;
}

export type IDetails = IDetailsItem[];

export enum EDestinationType {
  /** 库外 */
  OUT = 0,
  /** 仓库 */
  WAREHOUSE = 1,
}

export const destinationTypeMap = {
  [EDestinationType.WAREHOUSE]: t('是'),
  [EDestinationType.OUT]: t('否'),
};

export const destinationTypeOptions = [
  {
    label: t('是'),
    value: EDestinationType.WAREHOUSE,
  },
  {
    label: t('否'),
    value: EDestinationType.OUT,
  },
];

export enum ESpecAssigneeFlag {
  /** 不指定 */
  NO = 0,
  /** 指定 */
  YES = 1,
}

export const specAssigneeFlagMap = {
  [ESpecAssigneeFlag.NO]: t('不指定'),
  [ESpecAssigneeFlag.YES]: t('指定'),
};

export const specAssigneeFlagOptions = [
  {
    label: t('是'),
    value: ESpecAssigneeFlag.YES,
  },
  {
    label: t('否'),
    value: ESpecAssigneeFlag.NO,
  },
];

export interface ISubmitParams {
  submit: boolean;
  id?: number;
  releaseParkId: number;
  releaseSubWarehouseId: number;
  /** 0-库外， 1-仓库 */
  destinationType: number;
  /** 目的地类型为仓库时必填 */
  destinationParkId?: number;
  /** 目的地类型为仓库时必填 */
  destinationSubWarehouseId?: number;
  applyReason: string;
  /** 0-不指定，1-指定 */
  specAssigneeFlag: number;
  /** 指定审批人时必填 */
  specAssigneeWorkNum?: string;
  details: IDetails;
  attachments: IAttachments;
  otherTypeDesc?: string;
}

export enum EStatus {
  ENABLE = 1,
  DISABLE = 0,
}

export interface ISubWarehouseTreeItem {
  parkId: number;
  parkName: string;
  parkStatus: EStatus;
  subWarehouse?: {
    /* 禁用状态 0-禁用 1-启用 */
    status: EStatus;
    warehouseId: number;
    warehouseName: string;
  }[];
}

export interface IStaffItem {
  id: number;
  staffName: string;
  enName: string;
  workNum: string;
}

export interface IItemTypeNameTreeItem {
  id: number;
  cargoTypeName: string;
  goodsList: {
    id: number;
    cargoName: string;
  }[];
}

export interface IUnitItem {
  /** 主键id */
  id: number;
  /** 单位名称 */
  unitName?: string;
  /** 创建日期 */
  addTime?: string;
  /** 单位状态；0-禁用，1-启用 */
  unitStatus?: number;
  /** 创建人英文名（工号） */
  creator?: string;
}

export interface IDetailResDetailItem {
  id: number;
  cargoTypeName: number;
  cargoName: number;
  releaseApplyId: number;
  cargoTypeId: number;
  cargoId: number;
  cargoNum: number;
  cargoUnitId: number;
  cargoUnitName: string;
}

export enum EApplyStatus {
  /** 待提交 */
  WAIT_SUBMIT = 0,
  /** 审批中 */
  APPROVAL = 1,
  /** 被驳回 */
  REJECT = 2,
  /** 已完结 */
  FINISHED = 3,
  /** 已撤回 */
  WITHDRAW = 4,
  /** 已作废 */
  CANCEL = 5,
}

export const applyStatusMap = {
  [EApplyStatus.WAIT_SUBMIT]: t('待提交'),
  [EApplyStatus.APPROVAL]: t('审批中'),
  [EApplyStatus.REJECT]: t('被驳回'),
  [EApplyStatus.FINISHED]: t('已完结'),
  [EApplyStatus.WITHDRAW]: t('已撤回'),
  [EApplyStatus.CANCEL]: t('已作废'),
};

export interface IDetailRes {
  attachments: {
    id: number;
    fileName: string;
    fileUrl: string;
    ossKey: string;
  }[];
  details: IDetailResDetailItem[];
  orderNum: string;
  applicantWorkNum: string;
  applicantName: string;
  applicantOaDepartmentId: number;
  applicantDepartmentAllName: string;
  currentAssigneeName: string;
  currentAssigneeWorkNum: string;
  /** 0-待提交，1-审批中，2-被驳回，3-已完结，4-已撤回，5-已作废 */
  applyStatus: number;
  releaseParkId: number;
  releaseParkName: string;
  releaseSubWarehouseId: number;
  releaseSubWarehouseName: string;
  destinationParkId: number;
  destinationParkName: string;
  destinationSubWarehouseId: number;
  destinationSubWarehouseName: string;
  destinationType: number;
  applyReason: string;
  finishedTime: string;
  submitTime: string;
  finishedType: number;
  specAssigneeFlag: number;
  specAssigneeName: string;
  specAssigneeWorkNum: string;
  id: number;
  otherTypeDesc: string;
}
