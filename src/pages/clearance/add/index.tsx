import React, { useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useObjectState, usePageTitle } from '@/_/hooks';
import { userStore } from '@/_/lego-stores/userStore';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';
import { parseInt } from 'lodash';
import isEmpty from 'lodash/isEmpty';
import isNil from 'lodash/isNil';
import { Button, Cascader, Cell, Field, Icons, Radio, Toast, Upload } from 'shineout-mobile';
import { doSubmit, getDetail, getSubWarehouseTree } from './api';
import ApproveUserSelect from './components/ApproveUserSelect';
import FooterContainer from './components/FooterContainer';
import ItemDetailsForm from './components/ItemDetailsForm';
import {
  destinationTypeOptions,
  EDestinationType,
  ESpecAssigneeFlag,
  EStatus,
  IAttachments,
  IDetails,
  ISubmitParams,
  ISubWarehouseTreeItem,
  specAssigneeFlagOptions,
} from './interfaces';
import styles from './styles/index.less';

const add = <Icons style={{ color: '#CCCFD7', fontSize: 20 }} name="add" />;

interface IMainProps {
  /** 是否为编辑页 */
  edit?: boolean;
  /** 编辑时的单据Id */
  id?: number;
}

const Main: React.FC<IMainProps> = (props) => {
  userStore.useMount();
  const { info } = userStore.useState();
  const { edit, id } = props;
  const navigate = useNavigate();
  usePageTitle(edit ? t('编辑放行申请') : t('新建放行申请'));
  const [formData, setFormData] = useObjectState({
    applicantName: info?.staffName,
    applicantWorkNum: info?.workNum,
    /** 放行仓, 二维数组 [片区Id, 园区Id] */
    releaseWareHouse: [] as number[],
    destinationType: EDestinationType.WAREHOUSE as EDestinationType,
    /** 目的地, 二维数组 [片区Id, 园区Id] */
    destination: [] as number[],
    applyReason: '',
    details: [{ cargoNum: 1 }] as IDetails,
    otherTypeDesc: '',
    attachments: [] as IAttachments,
    specAssigneeFlag: ESpecAssigneeFlag.NO as ESpecAssigneeFlag,
    approver: null as {
      enName: string;
      workNum: string;
    },
  });
  const isDetailsContainOther = useMemo(() => {
    return formData.details.some((item) => item.cargoTypeId === 0 && item.cargoId === 0);
  }, [formData.details]);
  /**
   * 企业微信的BUG，但input设置accept时，如果选择的文件不是图片，会被企业微信强制编成图片且size是0
   * 所以添加此来区分是否在微信中来方便本地开发
   * */
  const isInWeChat = !!window.navigator.userAgent.match(/wxwork/i);
  const [subWarehouseTree, setSubWarehouseTree] = useState<
    {
      id: number;
      name: string;
      status: EStatus;
      children?: {
        id: number;
        name: string;
        status: EStatus;
      }[];
    }[]
  >([]);
  const [pageState, setPageState] = useObjectState({
    showUserSelect: false,
    saveLoading: false,
    submitLoading: false,
    initialized: false,
  });

  useEffect(() => {
    getSubWarehouseTree().then((data) => {
      const convertData = data.map((item: ISubWarehouseTreeItem) => {
        return {
          id: item.parkId,
          name: item.parkName,
          status: item.parkStatus,
          children: item.subWarehouse?.map((child) => {
            return {
              id: child.warehouseId,
              name: child.warehouseName,
              status: child.status,
            };
          }),
        };
      });
      setSubWarehouseTree(convertData);
    });
  }, []);

  useEffect(() => {
    if (edit) {
      getDetail({ id }).then((data) => {
        const {
          applicantName,
          applicantWorkNum,
          releaseParkId,
          releaseSubWarehouseId,
          destinationType,
          destinationParkId,
          destinationSubWarehouseId,
          applyReason,
          details,
          otherTypeDesc,
          attachments,
          specAssigneeFlag,
          specAssigneeWorkNum,
          specAssigneeName,
        } = data;
        const defaultFormData: any = {
          applicantName,
          applicantWorkNum,
          destinationType,
          applyReason,
          details,
          otherTypeDesc,
          specAssigneeFlag,
          attachments: attachments || [],
        };
        if (!isNil(releaseParkId) && !isNil(releaseSubWarehouseId)) {
          defaultFormData.releaseWareHouse = [releaseParkId, releaseSubWarehouseId];
        }
        if (!isNil(destinationParkId) && !isNil(destinationSubWarehouseId)) {
          defaultFormData.destination = [destinationParkId, destinationSubWarehouseId];
        }
        if (!isEmpty(specAssigneeWorkNum) && !isEmpty(specAssigneeName)) {
          defaultFormData.approver = {
            enName: specAssigneeName,
            workNum: specAssigneeWorkNum,
          };
        }
        setFormData(defaultFormData);
        setPageState({
          initialized: true,
        });
      });
    }
  }, [edit, id, setFormData, setPageState]);

  /**
   * @description bindFormValue
   * @param {unknown} field desc
   * @param {unknown} value desc
   * @returns {unknown} desc
   */
  const bindFormValue = (field: string, value: any) => {
    setFormData({
      [field]: value,
    });
  };

  /**
   * @description getCascaderValue
   * @param {unknown} data desc
   * @returns {unknown} desc
   */
  const getCascaderValue = (data: number[]) => {
    if (!data?.length) {
      return undefined;
    }
    if (data.length === 1) {
      return [`${data[0]}_p`];
    }
    return [`${data[0]}_p`, `${data[1]}_w`];
  };

  /**
   * @description getSubmitData
   * @param {unknown} submit desc
   * @returns {unknown} desc
   */
  const getSubmitData = (submit = false) => {
    const submitData: ISubmitParams = {
      submit,
      releaseParkId:
        formData.releaseWareHouse?.length === 2 ? formData.releaseWareHouse[0] : undefined,
      releaseSubWarehouseId:
        formData.releaseWareHouse?.length === 2 ? formData.releaseWareHouse[1] : undefined,
      destinationType: formData.destinationType,
      applyReason: formData.applyReason,
      specAssigneeFlag: formData.specAssigneeFlag,
      details: formData.details.map((item) => {
        const temp = {
          ...item,
        };
        delete temp._uid;
        if (isNil(temp.cargoTypeId) && isNil(temp.cargoId)) {
          delete temp.cargoTypeId;
          delete temp.cargoId;
        }
        return temp;
      }),
      attachments: formData.attachments,
    };
    if (
      formData.destinationType === EDestinationType.WAREHOUSE &&
      formData.destination?.length === 2
    ) {
      submitData.destinationParkId = formData.destination[0];
      submitData.destinationSubWarehouseId = formData.destination[1];
    }
    if (formData.specAssigneeFlag === ESpecAssigneeFlag.YES) {
      submitData.specAssigneeWorkNum = formData.approver?.workNum;
    }
    if (isDetailsContainOther) {
      submitData.otherTypeDesc = formData.otherTypeDesc;
    }
    if (edit) {
      submitData.id = id;
    }
    return submitData;
  };

  /**
   * @description toApplyList
   * @returns {unknown} desc
   */
  const toApplyList = () => {
    navigate('/clearance/apply-list');
  };

  /**
   * @description handleSave
   * @returns {unknown} desc
   */
  const handleSave = () => {
    setPageState({
      saveLoading: true,
    });
    doSubmit(getSubmitData())
      .then(() => {
        Toast.success(t('保存成功'));
        setPageState({
          saveLoading: false,
        });
        toApplyList();
      })
      .catch(() => {
        setPageState({
          saveLoading: false,
        });
      });
  };

  /**
   * @description handleSubmit
   * @returns {unknown} desc
   */
  const handleSubmit = () => {
    if (formData.releaseWareHouse?.length !== 2) {
      Toast.fail(t('请选择放行仓'));
      return;
    }
    if (formData.destinationType === EDestinationType.WAREHOUSE) {
      if (formData.destination?.length < 2) {
        Toast.fail(t('请选择目的地'));
        return;
      }
    }
    if (!formData.applyReason?.trim()) {
      Toast.fail(t('请输入申请说明'));
      return;
    }
    const isDetailsError = formData.details?.some((item) => {
      return (
        (isNil(item.cargoTypeId) && isNil(item.cargoId)) ||
        item.cargoNum < 1 ||
        item.cargoNum > 9999 ||
        isNil(item.cargoUnitId)
      );
    });
    if (isDetailsError) {
      Toast.fail(t('请填写完整货物明细(明细表存在留空)'));
      return;
    }
    if (isDetailsContainOther && !formData.otherTypeDesc?.trim()) {
      Toast.fail(t('请填写其他类型说明'));
      return;
    }
    if (formData.specAssigneeFlag === ESpecAssigneeFlag.YES && isNil(formData.approver)) {
      Toast.fail(t('请选择审批人'));
      return;
    }
    setPageState({
      submitLoading: true,
    });
    doSubmit(getSubmitData(true))
      .then(() => {
        Toast.success(t('提交成功'));
        setPageState({
          submitLoading: false,
        });
        toApplyList();
      })
      .catch(() => {
        setPageState({
          submitLoading: false,
        });
      });
  };

  if (edit && !pageState.initialized) {
    return null;
  }

  return (
    <div className={classNames('h-[100vh]', styles.page)}>
      <div className={styles.contentContainer}>
        <section className={styles.section}>
          <Cell
            label={t('申请人')}
            value={
              <span
                style={{
                  color: '#666C7C',
                }}
              >{`${formData.applicantName}(${formData.applicantWorkNum})`}</span>
            }
          />
          <Cascader
            label={t('放行仓')}
            data={subWarehouseTree}
            renderItem={(item) => {
              return item.status === EStatus.DISABLE ? `${item.name}${t('(已禁用)')}` : item.name;
            }}
            keygen={(item) => (item.children ? `${item.id}_p` : `${item.id}_w`)}
            title={t('请选择放行仓')}
            placeholder={t('请选择')}
            renderResult={(result) => {
              return (
                <span
                  style={{
                    color: '#666C7C',
                  }}
                >
                  {result?.length < 2 || result?.every((item) => isEmpty(item))
                    ? t('请选择')
                    : result?.map((d) => d?.name).join('/')}
                </span>
              );
            }}
            value={getCascaderValue(formData.releaseWareHouse)}
            disabled={(item) => item.status === EStatus.DISABLE}
            onChange={(value) => {
              bindFormValue(
                'releaseWareHouse',
                value.map((str) => parseInt(str)),
              );
            }}
          />
          <Cell
            label={t('目的地是否为仓库')}
            value={
              <Radio.Group
                inline
                value={formData?.destinationType}
                onChange={(value: EDestinationType) => {
                  bindFormValue('destinationType', value);
                }}
              >
                {destinationTypeOptions.map((item) => (
                  <Radio value={item.value} shape="square" key={item.value}>
                    {item.value === formData?.destinationType ? (
                      <span
                        style={{
                          color: '#666C7C',
                        }}
                      >
                        {item.label}
                      </span>
                    ) : (
                      <>{item.label}</>
                    )}
                  </Radio>
                ))}
              </Radio.Group>
            }
          />
          {formData?.destinationType === EDestinationType.WAREHOUSE && (
            <Cascader
              label={t('目的地')}
              data={subWarehouseTree}
              renderItem={(item) => {
                return item.status === EStatus.DISABLE ? `${item.name}${t('(已禁用)')}` : item.name;
              }}
              disabled={(item) => item.status === EStatus.DISABLE}
              title={t('请选择目的地')}
              placeholder={t('请选择')}
              keygen={(item) => (item.children || item.id === 0 ? `${item.id}_p` : `${item.id}_w`)}
              value={getCascaderValue(formData.destination)}
              renderResult={(result) => {
                return (
                  <span
                    style={{
                      color: '#666C7C',
                      marginTop: 3,
                      display: 'inline-block',
                    }}
                  >
                    {result?.length < 2 || result?.every((item) => isEmpty(item))
                      ? t('请选择')
                      : result.map((d) => d?.name).join('/')}
                  </span>
                );
              }}
              onChange={(value) => {
                bindFormValue(
                  'destination',
                  value.map((str) => parseInt(str)),
                );
              }}
            />
          )}
          <Field
            style={{ color: '#666C7C' }}
            type="textarea"
            maxLength={50}
            showWordLimit
            label={t('申请说明')}
            align="right"
            rows={2}
            placeholder={t('请输入')}
            value={formData.applyReason}
            onChange={(e) => bindFormValue('applyReason', e.target.value)}
            className={styles.applyReason}
          />
        </section>
        <section className={classNames(styles.section)}>
          <div className="p-md pb-0">
            <div className={styles.detailsTitle}>
              <span>{t('货物明细')}</span>
              <small>{t('表格行支持左滑删除数据')}</small>
            </div>
            <ItemDetailsForm
              defaultValue={formData.details}
              onChange={(data) => bindFormValue('details', data)}
            />
          </div>
          {isDetailsContainOther && (
            <Field
              type="textarea"
              maxLength={50}
              showWordLimit
              align="right"
              rows={2}
              label={t('其他类型说明')}
              placeholder={t('请输入')}
              value={formData.otherTypeDesc}
              onChange={(e) => bindFormValue('otherTypeDesc', e.target.value)}
            />
          )}
        </section>
        <section className={classNames(styles.section, 'p-md')}>
          <div className={styles.affixTitle}>
            <span>{t('附件')}</span>
            <small className={styles.affixTip}>{t('(选填)')}</small>
          </div>
          <Upload
            icon={add}
            action="/attachmentFile/uploadImage"
            accept={isInWeChat ? undefined : 'image/jpg,image/jpeg,image/png'}
            limit={9}
            name="fileList"
            validator={{
              size: (size) => size > 10 * 1024 * 1024 && new Error(t('图片必须小于10MB')),
              custom: (file) => {
                const { size, type } = file;
                if (!/image\/(jpe?g|png|gif|bmp)/.test(type)) {
                  return new Error(t('只能上传jpg、jpeg、png、gif、bmp格式的图片'));
                }
                if (size > 10 * 1024 * 1024) {
                  return new Error(t('图片必须小于10MB'));
                }
                return undefined;
              },
            }}
            renderItem="fileUrl"
            multiple
            onSuccess={(responseJson) => {
              try {
                const res = JSON.parse(responseJson);
                if (!res.data) {
                  Toast.fail(res.msg);
                  return;
                }
                return res.data;
              } catch {
                Toast.fail(t('上传文件出错'));
                // do nothing
              }
            }}
            value={formData.attachments}
            onChange={(list) =>
              bindFormValue(
                'attachments',
                list.filter((item) => Boolean(item)),
              )
            }
          />
        </section>
        <section className={styles.section}>
          <Cell
            label={
              <div>
                <span>{t('指定审批人')}</span>
                <span
                  style={{
                    marginLeft: '3px',
                    fontSize: '12px',
                    color: '#999da8',
                    display: 'block',
                  }}
                >
                  {t('(选“否”默认上级审批)')}
                </span>
              </div>
            }
            value={
              <Radio.Group
                inline
                value={formData.specAssigneeFlag}
                onChange={(value: ESpecAssigneeFlag) => {
                  bindFormValue('specAssigneeFlag', value);
                }}
              >
                {specAssigneeFlagOptions.map((item) => (
                  <Radio value={item.value} shape="square" key={item.value}>
                    {formData?.specAssigneeFlag === item.value ? (
                      <span
                        style={{
                          color: '#666C7C',
                        }}
                      >
                        {item.label}
                      </span>
                    ) : (
                      <>{item.label}</>
                    )}
                  </Radio>
                ))}
              </Radio.Group>
            }
          />
          {formData?.specAssigneeFlag === ESpecAssigneeFlag.YES && (
            <Cell
              label={t('审批人')}
              placeholder={t('点击选择审批人')}
              isLink
              onClick={() =>
                setPageState({
                  showUserSelect: true,
                })
              }
              value={
                formData?.approver ? (
                  <span
                    style={{
                      color: '#666C7C',
                      display: 'flex',
                      justifyContent: 'flex-end',
                      alignItems: 'center',
                      marginTop: 2,
                    }}
                  >
                    {`${formData.approver.enName}(${formData.approver.workNum})`}
                  </span>
                ) : undefined
              }
            />
          )}
          <ApproveUserSelect
            visible={pageState.showUserSelect}
            onClose={() =>
              setPageState({
                showUserSelect: false,
              })
            }
            onOk={(data) =>
              bindFormValue('approver', {
                enName: data.enName,
                workNum: data.workNum,
              })
            }
          />
        </section>
      </div>
      <FooterContainer className="bg-white mt-[6px]">
        <Button
          type="primary"
          plain
          round
          onClick={handleSave}
          disabled={pageState.submitLoading}
          loading={pageState.saveLoading}
          loadingText={t('保存')}
        >
          {t('保存')}
        </Button>
        <Button
          type="primary"
          round
          onClick={handleSubmit}
          disabled={pageState.saveLoading}
          loading={pageState.submitLoading}
          loadingText={t('提交')}
        >
          {t('提交')}
        </Button>
      </FooterContainer>
    </div>
  );
};

export default Main;
