.page {
  display: flex;
  flex-direction: column;

  :global {
    .sm-cell-value {
      text-align: right !important;
    }
  }
}

.applyReason {
  :global(.sm-input) {
    color: #666c7c;

    &::placeholder {
      color: #666c7c;
    }
  }
}

.contentContainer {
  min-height: 0;
  flex: 1;
  overflow: auto;
}

.section {
  background-color: #fff;

  & + & {
    margin-top: 6px;
  }

  :global {
    .sm-cell + .sm-popup::after,
    .sm-popup + .sm-cell::after {
      position: absolute;
      top: 0;
      right: 12px;
      left: 12px;
      display: block;
      pointer-events: none;
      content: ' ';
      transform: scaleY(.5);
      box-sizing: border-box;
    }

    .sm-popup {
      position: relative;
    }

    .sm-radio-icon {
      border-radius: 50%;
    }

    .sm-cell-label {
      width: auto !important;
    }
  }

  :global(> .sm-popup::after) {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 1px;
    background-color: #e6eaf0;
    content: '';
    transform: scaleY(.5);
  }
}

.detailsTitle {
  margin-bottom: 12px;
  font-family: PingFangSC-Regular, 'PingFang SC', sans-serif;
  font-size: 12px;
  color: #35383d;

  & > small {
    margin-left: 4px;
    font-family: PingFangSC-Regular, 'PingFang SC', sans-serif;
    font-size: 10px;
    color: #ffa940;
  }
}

.affixTitle {
  margin-bottom: 12px;
  font-family: PingFangSC-Regular, 'PingFang SC', sans-serif;
  color: #35383d;
}

.affixTip {
  margin-left: 3px;
  font-size: 12px;
  color: #999da8;
}
