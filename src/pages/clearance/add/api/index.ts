import { get, post } from '@/utils';
import {
  IDetailRes,
  IItemTypeNameTreeItem,
  IStaffItem,
  ISubmitParams,
  ISubWarehouseTreeItem,
  IUnitItem,
} from '../interfaces';

/**
 * 获取园区-子仓数
 * */
export const getSubWarehouseTree = () => {
  return get<ISubWarehouseTreeItem[]>('/subWarehouse/getSubWarehouseTree2');
};

/**
 * 查询在职员工
 * */
export const queryStaffList = (params: { enName: string }) => {
  return get<IStaffItem[]>('/Home/staff/selectStaffByEnName', params);
};

/**
 * 物品类型-物品名称
 * */
export const getItemTypeNameTree = () => {
  return get<IItemTypeNameTreeItem[]>('/cargo/goods/selectList');
};

/**
 * 单位
 * */
export const getUnitList = () => {
  return get<IUnitItem[]>('/cargo/unit/selectUnitList');
};

/**
 * 保存 & 新增
 * */
export const doSubmit = (params: ISubmitParams) => {
  return post('/cargo/releaseApply/save', params);
};

/**
 * 申请详情
 * */
export const getDetail = (params: { id: number }) => {
  return get<IDetailRes>('/cargo/releaseApply/detail', params);
};
