import React, { useContext, useRef, useState } from 'react';
import { useObjectState } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { useUpdateEffect } from '@shein-lego/use';
import { <PERSON><PERSON>, NPicker, Stepper, SwipeCell } from 'shineout-mobile';
import ItemDetails from '../../context/ItemDetails';
import styles from './index.less';
import { IFormItem } from './interfaces';
import ItemCascader from './ItemCascader';

interface IMainProps {
  defaultValue?: IFormItem;
  onChange?: (value: IFormItem, rowIndex: number) => void;
  deletable?: boolean;
  rowIndex: number;
  onRemove?: (rowIndex: number) => void;
}

/**
 * @description Main
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Main: React.FC<IMainProps> = (props) => {
  const { defaultValue, onChange, deletable, rowIndex, onRemove } = props;
  const onChangeRef = useRef(onChange);
  onChangeRef.current = onChange;
  const { itemTypeNameTree, unitList } = useContext(ItemDetails);
  const [showPicker, setShowPicker] = useState(false);

  const [formValue, setFormValue] = useObjectState<IFormItem>({
    item: null,
    quantity: 1,
    unit: null,
    ...(defaultValue || {}),
  });

  useUpdateEffect(() => {
    onChangeRef.current?.(formValue, rowIndex);
  }, [formValue, rowIndex]);

  /**
   * @description getCascaderValue
   * @returns {unknown} desc
   */
  const getCascaderValue = () => {
    if (!formValue?.item) {
      return undefined;
    }
    if (formValue?.item?.cargoTypeId !== undefined && formValue?.item?.cargoId !== undefined) {
      return [formValue?.item?.cargoTypeId, formValue?.item?.cargoId];
    }
    if (formValue?.item?.cargoTypeId !== undefined) {
      return [formValue?.item?.cargoTypeId];
    }
    return undefined;
  };

  return (
    <SwipeCell
      key={deletable ? 1 : 0}
      right={
        deletable ? (
          <Button type="danger" className="h-full" onClick={() => onRemove?.(rowIndex)}>
            {t('删除')}
          </Button>
        ) : undefined
      }
    >
      <div className={styles.row}>
        <div>
          <ItemCascader
            options={itemTypeNameTree}
            value={getCascaderValue()}
            onChange={(value) => {
              setFormValue({
                item: {
                  cargoTypeId: value[0],
                  cargoId: value[1],
                },
              });
            }}
          />
        </div>
        <div>
          <Stepper
            max={9999}
            min={1}
            integer
            value={formValue.quantity}
            onChange={(value) =>
              setFormValue({
                quantity: value,
              })
            }
          />
        </div>
        <div>
          <Button
            className="pl-sm w-full inline-block text-left"
            text
            onClick={() => setShowPicker(true)}
          >
            {t(!formValue?.unit ? '请选择' : formValue.unit?.unitName)}
          </Button>
          <NPicker
            data={unitList}
            drawer={{
              visible: showPicker,
            }}
            renderItem="unitName"
            onCancel={() => setShowPicker(false)}
            onOk={(value) => {
              setFormValue({
                unit: value,
              });
              setShowPicker(false);
            }}
          />
        </div>
      </div>
    </SwipeCell>
  );
};

export default Main;
