import React, { useMemo, useRef, useState } from 'react';
import Icon from '@/_/components/Icon';
import { useObjectState } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { useMount, useUpdateEffect } from '@shein-lego/use';
import classNames from 'classnames';
import isNil from 'lodash/isNil';
import { Button } from 'shineout-mobile';
import { getItemTypeNameTree, getUnitList } from '../../api';
import ItemDetails from '../../context/ItemDetails';
import { IDetailsItem, IItemTypeNameTreeItem, IUnitItem } from '../../interfaces';
import styles from './index.less';
import { IFormItem } from './interfaces';
import Row from './Row';

interface IMainProps {
  defaultValue?: IDetailsItem[];
  onChange?: (data: IDetailsItem[]) => void;
}

let uid = 0;

/**
 * @description Main
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Main: React.FC<IMainProps> = (props) => {
  const { defaultValue, onChange } = props;
  const onChangeRef = useRef(onChange);
  onChangeRef.current = onChange;

  const [options, setOptions] = useObjectState({
    itemTypeNameTree: [] as IItemTypeNameTreeItem[],
    unitList: [] as IUnitItem[],
  });
  const [details, setDetails] = useState<Partial<IDetailsItem>[]>(
    defaultValue?.map((item) => ({
      ...item,
      _uid: uid++,
    })) || [
      {
        _uid: uid++,
      },
    ],
  );
  const isDetailsContainOther = useMemo(() => {
    return details.some((item) => item.cargoTypeId === 0 && item.cargoId === 0);
  }, [details]);

  useMount(() => {
    getItemTypeNameTree().then((data) => {
      setOptions({
        itemTypeNameTree: data,
      });
    });
    getUnitList().then((data) => {
      setOptions({
        unitList: data,
      });
    });
  });

  /**
   * @description handleRowChange
   * @param {unknown} value desc
   * @param {unknown} rowIndex desc
   * @returns {unknown} desc
   */
  const handleRowChange = (value: IFormItem, rowIndex: number) => {
    setDetails((prev) => {
      const newDetails = prev.map((item, index) => {
        if (index === rowIndex) {
          return {
            ...item,
            cargoTypeId: value?.item?.cargoTypeId,
            cargoId: value?.item?.cargoId,
            cargoNum: value?.quantity,
            cargoUnitName: value?.unit?.unitName,
            cargoUnitId: value?.unit?.id,
          };
        }
        return item;
      });
      onChangeRef.current?.(newDetails);
      return newDetails;
    });
  };

  useUpdateEffect(() => {
    onChangeRef.current?.(details);
  }, [details]);

  /**
   * @description handleRemoveRow
   * @param {unknown} rowIndex desc
   * @returns {unknown} desc
   */
  const handleRemoveRow = (rowIndex: number) => {
    setDetails((prev) => {
      return prev.slice(0, rowIndex).concat(prev.slice(rowIndex + 1));
    });
  };

  return (
    <div>
      <div className={styles.header}>
        <div>{t('物品类型/物品名称')}</div>
        <div>{t('数量')}</div>
        <div className="pl-[14px]">{t('单位')}</div>
      </div>
      <ItemDetails.Provider
        value={{
          itemTypeNameTree: options.itemTypeNameTree,
          unitList: options.unitList,
        }}
      >
        {details.map((item, index) => (
          <Row
            key={item._uid}
            deletable={details?.length > 1}
            rowIndex={index}
            defaultValue={{
              item:
                !isNil(item.cargoTypeId) && !isNil(item.cargoId)
                  ? {
                      cargoTypeId: item.cargoTypeId,
                      cargoId: item.cargoId,
                    }
                  : null,
              quantity: item.cargoNum,
              unit: !isNil(item.cargoUnitId)
                ? {
                    id: item.cargoUnitId,
                    unitName: item.cargoUnitName,
                  }
                : null,
            }}
            onChange={handleRowChange}
            onRemove={handleRemoveRow}
          />
        ))}
      </ItemDetails.Provider>
      {details.length < 15 && (
        <div
          className={classNames(styles.addRow, { [styles.addRowNoBorder]: !isDetailsContainOther })}
        >
          <Button
            type="primary"
            text
            onClick={() =>
              setDetails((prev) =>
                prev.concat({
                  _uid: uid++,
                  cargoNum: 1,
                }),
              )
            }
          >
            <Icon name="m-add-circle-fill" />
            <span>{t('添加')}</span>
          </Button>
        </div>
      )}
    </div>
  );
};

export default Main;
