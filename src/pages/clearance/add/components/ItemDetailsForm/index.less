.grid() {
  display: grid;
  grid-template-columns: 39% 34% 27%;

  & > div {
    padding: 4px 6px;
    align-self: center;
  }
}

.header {
  .grid();

  height: 40px;
  @apply bg-gray-100;
}

.row {
  .grid();

  border-top: 1px solid #e6eaf0;
}

.dropDownMenu {
  box-shadow: none;

  :global {
    .sm-dropdown-menu-item {
      text-align: left;
    }
  }
}

.addRow {
  padding: 12px 0;
  text-align: center;
  border-top: 1px solid #e6eaf0;
  border-bottom: 1px solid #e6eaf0;

  :global(.so-icon) {
    margin-right: 3px;
  }
}

.addRowNoBorder {
  border-bottom: none;
}
