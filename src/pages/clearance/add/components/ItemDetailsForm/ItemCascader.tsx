import React from 'react';
import { t } from '@shein-bbl/react';
import { parseInt } from 'lodash';
import isEmpty from 'lodash/isEmpty';
import { Cascader } from 'shineout-mobile';
import { IItemTypeNameTreeItem } from '../../interfaces';

interface IMainProps {
  options: IItemTypeNameTreeItem[];
  value?: number[];
  onChange?: (value: number[]) => void;
}

/**
 * @description Main
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Main: React.FC<IMainProps> = (props) => {
  const { options, value, onChange } = props;

  const displayOptions = options
    .map((item) => ({
      id: item.id,
      name: item.cargoTypeName,
      children: item.goodsList?.map((child) => ({
        id: child.id,
        name: child.cargoName,
      })),
    }))
    .concat({
      id: 0,
      name: t('其他'),
      children: [{ id: 0, name: t('其他') }],
    });

  /**
   * @description getCascaderValue
   * @returns {unknown} desc
   */
  const getCascaderValue = () => {
    if (value?.length === 2) {
      return [`${value[0]}_t`, `${value[1]}_n`];
    }
    if (value?.length === 1) {
      return [`${value[0]}_t`];
    }
    return undefined;
  };

  return (
    <Cascader
      title={t('请选择物品类型和物品名称')}
      placeholder={t('请选择')}
      data={displayOptions}
      value={getCascaderValue()}
      keygen={(item) => (item.children ? `${item.id}_t` : `${item.id}_n`)}
      renderItem="name"
      onChange={(value) => {
        onChange?.(value.map((str) => parseInt(str)));
      }}
    >
      {(result) => {
        return result?.length < 2 || result?.every((item) => isEmpty(item))
          ? t('请选择')
          : result.map((d) => d?.name).join('/');
      }}
    </Cascader>
  );
};

export default Main;
