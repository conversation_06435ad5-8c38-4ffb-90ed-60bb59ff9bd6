import React from 'react';
import classNames from 'classnames';
import styles from './index.less';

interface IFooterContainer {
  className?: string;
  style?: React.CSSProperties;
}

/**
 * @description FooterContainer
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const FooterContainer: React.FC<IFooterContainer> = (props) => {
  const { className, style, children } = props;

  return (
    <footer className={classNames(className, styles.footer)} style={style}>
      {children}
    </footer>
  );
};

export default FooterContainer;
