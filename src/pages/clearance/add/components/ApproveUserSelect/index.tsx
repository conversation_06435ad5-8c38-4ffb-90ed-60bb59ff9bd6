import React from 'react';
import { useObjectState } from '@/_/hooks';
import Empty from '@/pages/clearance/components/Empty';
import { t } from '@shein-bbl/react';
import { useUpdateEffect } from '@shein-lego/use';
import { Button, Cell, Drawer, Radio, SearchBar } from 'shineout-mobile';
import { queryStaffList } from '../../api';
import { IStaffItem } from '../../interfaces';
import FooterContainer from '../FooterContainer';
import styles from './index.less';

interface IApproveUserSelectProps {
  visible: boolean;
  onOk?: (user: IStaffItem) => void;
  onClose?: () => void;
}

/**
 * @description ApproveUserSelect
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const ApproveUserSelect: React.FC<IApproveUserSelectProps> = (props) => {
  const { visible, onClose, onOk } = props;
  const [state, setState] = useObjectState({
    staffList: [] as IStaffItem[],
    selectedUser: null as IStaffItem,
    searchValue: '',
    noData: false,
  });

  /**
   * @description handleSearch
   * @param {unknown} enName desc
   * @returns {unknown} desc
   */
  const handleSearch = (enName: string) => {
    if (!enName) {
      return;
    }
    queryStaffList({
      enName,
    }).then((data) => {
      setState({
        staffList: data,
        selectedUser: null,
        noData: !data.length,
      });
    });
  };

  useUpdateEffect(() => {
    if (!visible) {
      setState({
        selectedUser: null,
        staffList: [],
        searchValue: '',
        noData: false,
      });
    }
  }, [visible]);

  /**
   * @description handleConfirm
   * @returns {unknown} desc
   */
  const handleConfirm = () => {
    if (state.selectedUser) {
      onOk?.(state.selectedUser);
      onClose?.();
    } else {
      onClose?.();
    }
  };

  return (
    <Drawer
      visible={visible}
      position="right"
      width="100vw"
      onClose={onClose}
      drawerClass="max-w-[750px] flex flex-col"
    >
      <SearchBar
        autoFocus
        clearable
        placeholder={t('请输入英文名搜索，O序列请搜中文名～')}
        onSearch={handleSearch}
        value={state.searchValue}
        onChange={(value) =>
          setState({
            searchValue: value,
          })
        }
      />
      <div className={styles.userContainer}>
        {state.staffList.map((item) => (
          <Cell
            key={item.id}
            onClick={() =>
              setState({
                selectedUser: item,
              })
            }
            label={
              <Radio
                shape="square"
                checked={state.selectedUser?.id === item.id}
              >{`${item.enName}(${item.workNum})`}</Radio>
            }
          />
        ))}
        {state.noData && <Empty icon="pc-no data-blue-multic" desc={t('没有找到对应审批人')} />}
      </div>
      <FooterContainer className="mt-0">
        <Button type="primary" plain round onClick={onClose}>
          {t('取消')}
        </Button>
        <Button type="primary" round onClick={handleConfirm}>
          {t('确认')}
        </Button>
      </FooterContainer>
    </Drawer>
  );
};

export default ApproveUserSelect;
