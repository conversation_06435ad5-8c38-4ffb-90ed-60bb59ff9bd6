import { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getHost, post } from '@/utils';
import { t } from '@shein-bbl/react';
import { Toast } from 'shineout-mobile';

type IApiParams = {
  url: string;
};

type IApiResponse = {
  agentId: string;
  signature: string;
  noncestr: string;
  timestamp: string;
  traceId: string;
};

/**
 * @description getAppIdByEnv
 * @returns {unknown} desc
 */
const getAppIdByEnv = () => {
  const host = getHost();
  const appIdMap = {
    dev: 'ww6e132f02586f7900',
    test: 'ww6e132f02586f7900',
    sit: 'ww5f025a4c596cbdd8',
    prod: 'wweb4abba124fabaad',
  };
  if (host.indexOf('lpmpm-dev') > -1) {
    return appIdMap.dev;
  } else if (host.indexOf('lpmpm-test') > -1) {
    return appIdMap.test;
  } else if (host.indexOf('lpmpm-sit') > -1) {
    return appIdMap.sit;
  } else if (host.indexOf('lpmpm') > -1) {
    return appIdMap.prod;
  }
  return appIdMap.dev;
};

/**
 * 通用微信扫码 Hook
 */
const useWxScan = () => {
  const [scanning, setScanning] = useState<boolean>(false);
  const navigate = useNavigate();

  /**
   * @description handleScanQrCode
   */
  const handleScanQrCode = useCallback(() => {
    setScanning(true);
    // 开始扫码
    wx.invoke(
      'scanQRCode',
      {
        needResult: 1,
        scanType: ['qrCode'],
      },
      (res) => {
        setScanning(false);
        console.log('wx.invoke.scanQRCode', res);

        if (res.err_msg === 'scanQRCode:ok') {
          let result = '';
          try {
            console.log(JSON.parse(res.resultStr));
            result = JSON.parse(res.resultStr).scan_code.scan_result;
          } catch (_error) {
            result = res.resultStr;
          }

          // 直接跳转到返回的url
          window.location.href = result;
        } else if (res.err_msg === 'scanQRCode:cancel') {
          // 用户取消扫码
          console.log(t('用户取消扫码'));
        } else {
          Toast.fail(res.err_msg ?? t('扫码失败'));
        }
      },
      (err) => {
        console.log(t('扫码错误:'), err);
        setScanning(false);
        Toast.fail(t('扫码失败'));
      },
    );
  }, [navigate]);

  /**
   * 开始扫码
   */
  const startScan = useCallback(() => {
    const params: IApiParams = {
      url: location.href.split('#')[0],
    };

    post<IApiResponse>('wechat/msg/jssdk', params).then((data) => {
      wx.config({
        /** 必须这么写，否则wx.invoke调用形式的jsapi会有问题 */
        beta: true,
        /** 开启调试模式,调用的所有api的返回值会在客户端alert出来 */
        debug: false,
        /** 必填，企业微信的corpID，必须是本企业的corpID，不允许跨企业使用 */
        appId: getAppIdByEnv(),
        /** 必填，生成签名的时间戳 */
        timestamp: data.timestamp,
        /** 必填，生成签名的随机串 */
        nonceStr: data.noncestr,
        /** 必填，签名，见 附录-JS-SDK使用权限签名算法 */
        signature: data.signature,
        /** 必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来 */
        jsApiList: ['scanQRCode'],
      });

      wx.ready(() => {
        /** 需要检测的JS接口列表，这一步非必要 */
        wx.checkJsApi({
          jsApiList: ['scanQRCode'],
          success: function (res: Record<string, any>): void {
            console.log('wx.checkJsApi.scanQRCode', res);
            handleScanQrCode();
          },
        });
      });

      wx.error((err) => {
        console.log('wx.error', err);
      });
    });
  }, [handleScanQrCode]);

  return {
    scanning,
    startScan,
  };
};

export default useWxScan;
