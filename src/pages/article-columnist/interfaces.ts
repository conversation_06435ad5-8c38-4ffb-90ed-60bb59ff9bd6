export interface IArticleItem {
  /** id */
  id?: number;
  /** 标题 */
  informationTitle?: string;
  /** 专栏图片url */
  imageUrl?: string;
  /** 发布时间 */
  publishTime?: string;
  /** 资讯链接 */
  informationUrl?: string;
}

export interface IGetPageListParams {
  /** 标题 */
  columnTitle?: string;
  /** 页码 */
  pageNumber: number;
  /** 页容量 */
  pageSize: number;
  /** 业务类型	 */
  businessTypeCode: string;
  /** 专栏类型	 */
  columnTypeCode: string;
}

export interface IBusinessTypeItem {
  /** 业务类型编码 */
  typeCode?: string;
  /** 业务类型名称 */
  typeName?: string;
  /** icon */
  iconName?: string;
}

export interface IColumnTypeItem {
  /** 业务类型编码 */
  typeCode?: string;
  /** 业务类型名称 */
  typeName?: string;
}

export interface IHomeArticleItem {
  /** id */
  id?: number;
  /** 标题 */
  informationTitle?: string;
  /** 资讯链接 */
  informationUrl?: string;
}
