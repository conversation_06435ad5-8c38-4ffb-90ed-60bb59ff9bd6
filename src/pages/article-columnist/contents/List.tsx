import { useNavigate } from 'react-router-dom';
import { t } from '@shein-bbl/react';
import { Loading, SearchBar, Tab } from 'shineout-mobile';
import ArticleItem from '../components/ArticleItem';
import BusinessIcon from '../components/BusinessIcon';
import { ColumnistListContainer } from '../containers';
import styles from './index.less';
const { Panel } = Tab;

/**
 * @description ListContent
 * @returns {unknown} desc
 */
const ListContent = () => {
  const navigate = useNavigate();
  const {
    data,
    state,
    handleChangeBusinessType,
    handleChangeColumnistType,
    handleClickArticle,
    containerRef,
  } = ColumnistListContainer.useContainer();

  /**
   * @description handleClickSearch
   * @returns {unknown} desc
   */
  const handleClickSearch = () => {
    navigate('/article-columnist/searching');
  };

  return (
    <div className={styles.container}>
      <SearchBar
        align="right"
        placeholder={t('请输入搜索关键字')}
        onClick={() => handleClickSearch()}
      />
      {data.pageLoading ? (
        <div className={styles.pageLoading}>
          <Loading />
        </div>
      ) : (
        <div>
          <div className={styles.businessWrapper}>
            {data.businessTypeList.map((business) => {
              return (
                <BusinessIcon
                  data={business}
                  isActive={business.typeCode === data.currentBusinessType?.typeCode}
                  key={business.typeCode}
                  onClick={() => handleChangeBusinessType(business)}
                />
              );
            })}
          </div>
          <div className={styles.articleContainer}>
            <Tab
              active={data.currentColumnType?.typeCode}
              autoWidth
              onChange={(d) =>
                handleChangeColumnistType(data.columnTypeList.find((c) => c.typeCode === d))
              }
            >
              {data.columnTypeList.map((column) => {
                return (
                  <Panel key={column.typeCode} name={column.typeCode} tab={column.typeName}></Panel>
                );
              })}
            </Tab>
            {state.loading ? (
              <div className={styles.articleLoading}>
                <Loading />
              </div>
            ) : (
              <div className={styles.articleWrapper} ref={containerRef}>
                {state.list.map((article) => {
                  return (
                    <ArticleItem
                      key={article.id}
                      data={article}
                      onClick={() => handleClickArticle(article)}
                    />
                  );
                })}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ListContent;
