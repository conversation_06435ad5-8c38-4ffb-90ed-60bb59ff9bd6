import Icon from '@/_/components/Icon';
import { t } from '@shein-bbl/react';
import { Loading, SearchBar, Tag } from 'shineout-mobile';
import ArticleItem from '../components/ArticleItem';
import { ColumnistSearchContainer } from '../containers';
import styles from './index.less';

/**
 * @description SearchContent
 * @returns {unknown} desc
 */
const SearchContent = () => {
  const {
    handleCancelSearch,
    handleSearch,
    handleValueChange,
    data,
    handleClickHistoryItem,
    handleClickResult,
  } = ColumnistSearchContainer.useContainer();
  return (
    <div>
      <SearchBar
        align="right"
        maxLength={20}
        value={data.value}
        placeholder={t('请输入搜索关键字')}
        onChange={(value) => {
          handleValueChange(value);
        }}
        onSearch={(value) => {
          handleSearch(value);
        }}
        onCancel={() => {
          handleCancelSearch();
        }}
      />
      <div>
        {data.value !== '' && data.isSearched === true ? (
          <div className={styles.resultWrapper}>
            {data.loading ? (
              <div className={styles.loading}>
                <Loading />
              </div>
            ) : (
              <div>
                {(data.result || []).length === 0 ? (
                  <div className={styles.empty}>
                    <Icon fontSize={85} name="pc-search empty-blue-multic" />{' '}
                    <span>{t('搜索结果为空')}</span>
                  </div>
                ) : (
                  <div className={styles.searchListWrapper}>
                    {(data.result || []).map((article) => {
                      return (
                        <ArticleItem
                          key={article.id}
                          data={article}
                          onClick={() => handleClickResult(article)}
                        />
                      );
                    })}
                  </div>
                )}
              </div>
            )}
          </div>
        ) : (
          <div style={{ padding: 12 }}>
            <div>{t('历史记录')}</div>
            <div style={{ gap: 10, padding: 6, display: 'flex', flexWrap: 'wrap', marginTop: 6 }}>
              {data.historyKeywords.map((keyword) => {
                return (
                  <Tag
                    shape="round"
                    type="default"
                    key={keyword}
                    onClick={() => handleClickHistoryItem(keyword)}
                  >
                    {keyword}
                  </Tag>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
export default SearchContent;
