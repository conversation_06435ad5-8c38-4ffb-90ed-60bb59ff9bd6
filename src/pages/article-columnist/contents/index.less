.pageLoading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 52px);
}

.businessWrapper {
  display: flex;
  gap: 8px;
  justify-content: space-between;
  padding: 12px;
}

.container {
  background-color: white;
}

.articleContainer {
  //
}

.articleLoading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 194px);
}

.articleWrapper {
  height: calc(100vh - 194px);
  overflow: auto;
}

.resultWrapper {
  position: absolute;
  top: 52px;
  z-index: 2;
  width: 100%;
  height: calc(100vh - 52px);
  overflow: auto;
  background-color: white;

  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 52px);
    color: #ccc;
    gap: 8px;
  }

  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 52px);
  }
}
