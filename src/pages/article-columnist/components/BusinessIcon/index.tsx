import Icon from '@/_/components/Icon';
import classNames from 'classnames';
import { IBusinessTypeItem } from '../../interfaces';
import styles from './styles.less';

interface IProps {
  onClick: () => void;
  isActive?: boolean;
  data: IBusinessTypeItem;
}
/**
 * @description BusinessIcon
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const BusinessIcon: React.FC<IProps> = ({ onClick, data, isActive = false }) => {
  return (
    <div
      className={classNames(styles.item, isActive ? styles.active : null)}
      onClick={() => onClick()}
    >
      <Icon className={styles.icon} name={data.iconName} />
      <div className={styles.title}>{data.typeName}</div>
    </div>
  );
};

export default BusinessIcon;
