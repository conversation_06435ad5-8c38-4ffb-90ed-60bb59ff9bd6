import Icon from '@/_/components/Icon';
import { t } from '@shein-bbl/react';
import { IArticleItem } from '../../interfaces';
import styles from './styles.less';

interface IProps {
  onClick: () => void;
  data: IArticleItem;
}

/**
 * @description DefaultImage
 * @returns {unknown} desc
 */
const DefaultImage = () => {
  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: '#eee',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        color: '#999',
      }}
    >
      {t('暂无图片')}
    </div>
  );
};

/**
 * @description ArticleItem
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const ArticleItem: React.FC<IProps> = ({ onClick, data }) => {
  return (
    <div className={styles.container} onClick={() => onClick()}>
      <div className={styles.leftColumn}>
        {data.imageUrl === undefined || data.imageUrl === '' ? (
          <DefaultImage />
        ) : (
          <img className={styles.image} src={data.imageUrl} />
        )}
      </div>
      <div className={styles.rightColumn}>
        <div className={styles.title}>{data.informationTitle}</div>
        <div className={styles.footer}>
          <div className={styles.time}>{data.publishTime}</div>
          <div className={styles.inspect}>
            {t('查看')}
            <Icon className={styles.icon} name="odec-right" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArticleItem;
