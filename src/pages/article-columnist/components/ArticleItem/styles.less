.container {
  position: inherit;
  display: flex;
  flex-direction: row;
  padding: 12px;
  gap: 12px;

  .leftColumn {
    width: 120px;
    height: 80px;
    min-width: 120px;
    overflow: hidden;
    border-radius: 8px;

    .image {
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
    }
  }

  .rightColumn {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    justify-content: space-between;

    .title {
      font-size: 14px;
      font-weight: 500;
      line-height: 1.5;
    }

    .footer {
      display: flex;
      color: #999da8;

      .time {
        flex-grow: 1;
      }

      .inspect {
        width: 60px;
        text-align: right;
      }
    }
  }
}
