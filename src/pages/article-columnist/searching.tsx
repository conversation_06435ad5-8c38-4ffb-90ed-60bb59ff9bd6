import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { ColumnistSearchContainer } from './containers';
import SearchContent from './contents/Search';

/**
 * @description ColumnistSearchingPage
 * @returns {unknown} desc
 */
const ColumnistSearchingPage = () => {
  usePageTitle(t('园区专栏'));
  return (
    <ColumnistSearchContainer.Provider>
      <SearchContent />
    </ColumnistSearchContainer.Provider>
  );
};

export default ColumnistSearchingPage;
