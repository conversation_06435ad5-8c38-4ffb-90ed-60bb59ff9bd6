import { get, post } from '@/utils';
import {
  IArticleItem,
  IBusinessTypeItem,
  IColumnTypeItem,
  IGetPageListParams,
  IHomeArticleItem,
} from './interfaces';

/**
 * 企微-园区专栏分页查询
 * https://soapi.sheincorp.cn/application/3580/routes/180955/doc
 * @param data
 * @returns
 */
export const getPageList = (data: IGetPageListParams) => {
  return post<{
    rows: IArticleItem[];
    total: number;
  }>('/columnInformation/wechat/page', data);
};

/**
 * 企微-访问资讯埋点
 * https://soapi.sheincorp.cn/application/3580/routes/180960/doc
 * @returns
 */
export const pageVisit = (params: { encryptId: number }) => {
  return post<string>('/columnInformation/wechat/visit', params);
};

/**
 * 企微-获取业务类型列表
 * https://soapi.sheincorp.cn/application/3580/routes/181040/doc
 * @returns
 */
export const getBusinessTypeList = () => {
  return get<IBusinessTypeItem[]>('/columnInformation/wechat/businessTypeList');
};

/**
 * 企微-根据业务类型获取专栏类型列表
 * https://soapi.sheincorp.cn/application/3580/routes/181041/doc
 * @param param
 * @returns
 */
export const getColumnTypeList = (param: { code: string }) => {
  return get<IColumnTypeItem[]>('/columnInformation/wechat/columnTypeList', param);
};

/**
 * 企微-园区专栏-搜索
 * https://soapi.sheincorp.cn/application/3580/routes/181077/doc
 * @param param
 * @returns
 */
export const searchArticle = (param: { columnTitle: string }) => {
  return post<{
    rows: IArticleItem[];
    total: number;
  }>('/columnInformation/wechat/search', param);
};

/**
 * 企微-园区专栏首页
 * https://soapi.sheincorp.cn/application/3580/routes/180948/doc
 * @returns
 */
export const getHomeArticleList = () => {
  return get<IHomeArticleItem[]>('/columnInformation/wechat/portal');
};
