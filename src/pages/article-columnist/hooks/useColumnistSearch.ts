import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { addUserSearchHistoryKeyword, getUserSearchHistoryKeyword } from '@/pages/work-order/_/api';
import { EUserSelectUsedEnum } from '@/pages/work-order/_/interfaces';
import { useRequest } from 'ahooks';
import { IArticleItem } from '../interfaces';
import { pageVisit, searchArticle } from '../services';

const useColumnistSearch = () => {
  const navigate = useNavigate();
  const [value, setValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<IArticleItem[]>();
  const [historyKeywords, setHistoryKeywords] = useState<string[]>([]);
  const usedEnum = EUserSelectUsedEnum.COLUMNIST_SEARCH;
  const [isSearched, setIsSearched] = useState(false);

  const { run: executeAddKeyword } = useRequest(addUserSearchHistoryKeyword, {
    manual: true,
  });
  const { run: executeGetKeyword } = useRequest(getUserSearchHistoryKeyword, {
    manual: true,
    onSuccess(data: string[]): void {
      setHistoryKeywords(data);
    },
  });

  /**
   * 处理搜索
   */
  const handleSearch = (value: string) => {
    setIsSearched(true);
    setLoading(true);
    // 添加搜索记录
    const keyword = value.trim();
    executeAddKeyword({
      searchKeyword: keyword,
      searchType: usedEnum,
    });
    searchArticle({
      columnTitle: value,
    })
      .then((res) => {
        setResult(res.rows);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  /**
   * @description handleValueChange
   * @param {unknown} value desc
   * @returns {unknown} desc
   */
  const handleValueChange = (value) => {
    setValue(value);
    if (value === '') {
      handleGetHistoryRecord();
      setIsSearched(false);
      setResult([]);
    }
  };

  /**
   * 处理获取历史记录
   */
  const handleGetHistoryRecord = () => {
    executeGetKeyword({
      searchType: usedEnum,
      limit: 10,
    });
  };
  /**
   * 处理点击历史记录
   */
  const handleClickHistoryItem = (value) => {
    setValue(value);
    handleSearch(value);
  };

  /**
   * 处理清除搜索框内容
   */
  const handleClearValue = () => {
    setValue('');
    setResult([]);
    setIsSearched(false);
  };

  /**
   * 处理点击搜索结果
   */
  const handleClickResult = (article: IArticleItem) => {
    pageVisit({
      encryptId: article.id,
    }).then(() => {
      window.location.href = article.informationUrl;
    });
  };

  /**
   * @description handleCancelSearch
   * @returns {unknown} desc
   */
  const handleCancelSearch = () => {
    navigate('/article-columnist');
  };

  useEffect(() => {
    handleGetHistoryRecord();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    handleSearch,
    handleClickHistoryItem,
    handleClearValue,
    handleClickResult,
    handleCancelSearch,
    handleValueChange,
    data: {
      value,
      loading,
      result,
      historyKeywords,
      isSearched,
    },
  };
};

export default useColumnistSearch;
