import { useCallback, useEffect, useRef, useState } from 'react';
import { t } from '@shein-bbl/react';
import { useMount, usePersistFn } from '@shein-lego/use';
import { useSetState } from 'ahooks';
import { debounce } from 'lodash';
import { Toast } from 'shineout-mobile';
import { IArticleItem, IBusinessTypeItem, IColumnTypeItem } from '../interfaces';
import { getBusinessTypeList, getColumnTypeList, getPageList, pageVisit } from '../services';

const useColumnistList = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [businessTypeList, setBusinessTypeList] = useState<IBusinessTypeItem[]>([]);
  const [columnTypeList, setColumnTypeList] = useState<IBusinessTypeItem[]>([]);
  const [currentBusinessType, setCurrentBusinessType] = useState<IBusinessTypeItem>();
  const [currentColumnType, setCurrentColumnType] = useState<IBusinessTypeItem>();
  const [pageLoading, setPageLoading] = useState<boolean>(false);
  const [state, setState] = useSetState<{
    loading: boolean;
    pageNumber: number;
    pageSize: number;
    isMore: boolean;
    list: IArticleItem[];
  }>({
    loading: false,
    pageNumber: 1,
    pageSize: 10,
    isMore: true,
    list: [],
  });

  useMount(() => {
    init();
  });

  /**
   * @description init
   * @returns {unknown} desc
   */
  const init = () => {
    setState({
      pageNumber: 1,
    });
    handleGetBusinessType().then(() => {
      requestAnimationFrame(handleGetList);
    });
  };

  /**
   * 获取列表
   * */
  const handleGetList = usePersistFn(() => {
    setState({
      loading: true,
    });
    if (currentBusinessType?.typeCode === undefined || currentColumnType?.typeCode === undefined) {
      // Toast.fail(t('业务类型或专栏类型未选择'));
      setState({
        loading: false,
        list: [],
      });
      setPageLoading(false);
      return;
    }
    getPageList({
      pageNumber: 1,
      pageSize: state.pageSize,
      businessTypeCode: currentBusinessType?.typeCode,
      columnTypeCode: currentColumnType?.typeCode,
    })
      .then((res) => {
        setState({
          list: res.rows,
          isMore: state.pageNumber * state.pageSize < res.total,
        });
      })
      .finally(() => {
        setPageLoading(false);
        setState({
          loading: false,
        });
      });
  });

  const handleLoadMore = useCallback(() => {
    if (!state.isMore) return;

    if (currentBusinessType?.typeCode === undefined || currentColumnType?.typeCode === undefined) {
      Toast.fail(t('业务类型或专栏类型未选择'));
      setState({
        loading: false,
        list: [],
      });
      setPageLoading(false);
      return;
    }

    const currentPageNumber = state.pageNumber + 1;
    setState({
      pageNumber: currentPageNumber,
      loading: true,
    });
    getPageList({
      pageNumber: currentPageNumber,
      pageSize: state.pageSize,
      businessTypeCode: currentBusinessType?.typeCode,
      columnTypeCode: currentColumnType?.typeCode,
    })
      .then((res) => {
        setState({
          loading: false,
          isMore: currentPageNumber * state.pageSize < res.total,
          list: [...state.list, ...res.rows],
        });
      })
      .finally(() => {
        setState({
          loading: false,
        });
      });
  }, [
    currentBusinessType?.typeCode,
    currentColumnType?.typeCode,
    setState,
    state.isMore,
    state.list,
    state.pageNumber,
    state.pageSize,
  ]);

  const handleScroll = debounce(
    usePersistFn((e: any) => {
      if (
        // 调整阈值
        e.target.scrollHeight - e.target.scrollTop - e.target.offsetHeight - 50 <= 0 &&
        state.isMore &&
        !state.loading
      ) {
        Toast.loading(t('加载更多数据'));
        handleLoadMore();
      }
    }),
    50,
  );

  useEffect(() => {
    const currentDom = containerRef.current;
    currentDom?.addEventListener('scroll', handleScroll);
    return () => {
      // 组件销毁时清除绑定事件
      currentDom?.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  /**
   * 获取业务类型
   */
  const handleGetBusinessType = () => {
    return new Promise((resolve, reject) => {
      getBusinessTypeList()
        .then((res) => {
          setBusinessTypeList(res);
          if (res?.length === 0) {
            Toast.fail(t('业务类型为空'));
            return reject('error');
          }
          setCurrentBusinessType(res?.[0]);
          handleGetColumnistType(res?.[0]?.typeCode)
            .then(() => {
              resolve('success');
            })
            .catch(reject);
        })
        .catch(reject);
    });
  };

  /**
   * 获取专栏类型
   */
  const handleGetColumnistType = (code: string) => {
    return new Promise((resolve, reject) => {
      getColumnTypeList({ code })
        .then((res) => {
          setColumnTypeList(res);
          if (res?.length === 0) {
            Toast.fail(t('专栏类型为空'));
            return reject('error');
          }
          setCurrentColumnType(res?.[0]);
          resolve('success');
        })
        .catch(reject);
    });
  };

  /**
   * 切换业务类型
   */
  const handleChangeBusinessType = async (businessType: IBusinessTypeItem) => {
    setState({
      pageNumber: 1,
    });
    setCurrentBusinessType(businessType);
    handleGetColumnistType(businessType?.typeCode)
      .then(() => {
        handleGetList();
      })
      .catch(() => {
        setState({
          list: [],
        });
      });
  };

  /**
   * 切换专栏类型
   */
  const handleChangeColumnistType = (columnType: IColumnTypeItem) => {
    setState({
      pageNumber: 1,
    });
    setCurrentColumnType(columnType);
    requestAnimationFrame(handleGetList);
  };

  /**
   * 点击文章
   */
  const handleClickArticle = (article: IArticleItem) => {
    pageVisit({ encryptId: article.id }).then(() => {
      window.location.href = article.informationUrl;
    });
  };

  return {
    containerRef,
    data: {
      pageLoading,
      businessTypeList,
      currentBusinessType,
      columnTypeList,
      currentColumnType,
    },
    state,
    handleChangeBusinessType,
    handleChangeColumnistType,
    handleClickArticle,
  };
};

export default useColumnistList;
