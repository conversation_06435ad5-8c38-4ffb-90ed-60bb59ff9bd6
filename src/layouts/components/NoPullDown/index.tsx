import React, { useRef } from 'react';
import { useNoPullDown } from '@/_/hooks';
import styles from './index.less';

/**
 * @description NoPullDown
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const NoPullDown: React.FC<{ children: React.ReactNode }> = (props) => {
  const { children } = props;
  const containerRef = useRef<HTMLDivElement>(null);
  useNoPullDown({
    containerRef,
  });

  return (
    <div className={styles.scroll} ref={containerRef}>
      {children}
    </div>
  );
};

export default NoPullDown;
