import React from 'react';
import { matchPath } from 'react-router-dom';
import { userStore } from '@/_/lego-stores/userStore';
import routes from '@@/routes/.routes.json';
import { ApmPageWrapper } from '@shein/apm-helper';

/**
 * @description findMatchPath
 * @param {unknown} allPath desc
 * @param {unknown} currentPath desc
 * @returns {unknown} desc
 */
const findMatchPath = (allPath: string[], currentPath: string) => {
  for (let i = 0; i < allPath.length; i++) {
    const path = allPath[i];
    if (matchPath(path, currentPath)) {
      return path;
    }
  }
  return '';
};

const allPagePath = routes.list.map((item) => item.path).filter((path) => path !== '/*');

/**
 * @description ApmComp
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const ApmComp: React.FC<{ path: string }> = (props) => {
  userStore.useMount();
  const { info } = userStore.useState();
  const { path } = props;

  if (!path || !info) {
    return null;
  }
  const matchPath = findMatchPath(allPagePath, path);

  const apmPageName = matchPath !== '/' ? matchPath.replace(/^\//, '') : 'home';

  const Comp = ApmPageWrapper(() => null, apmPageName);
  return <Comp />;
};

export default React.memo(ApmComp);
