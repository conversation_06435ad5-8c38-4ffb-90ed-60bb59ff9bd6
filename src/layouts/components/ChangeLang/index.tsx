/* eslint-disable @shein-bbl/bbl/translate-i18n-byT */
import React, { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useObjectState } from '@/_/hooks';
import langs from '@/langs';
import { useMount } from '@shein-lego/use';
import isNil from 'lodash/isNil';
import { ActionSheet } from 'shineout-mobile';
import styles from './index.less';
import noBblPages from './no-bbl-pages';

const ChangeLang: React.FC = () => {
  const switchPosRef = useRef({
    hasMoved: false, // exclude click event
    x: 0, // right
    y: 0, // bottom
    startX: 0,
    startY: 0,
    endX: 0,
    endY: 0,
  });
  const [btnPos, setBtnPos] = useObjectState({
    x: 0,
    y: 0,
  });
  const btnRef = useRef(null);
  const location = useLocation();

  /**
   * Get an safe [x, y] position for switch button
   */
  const _getSwitchButtonSafeAreaXY = (x: number, y: number) => {
    const docWidth = Math.max(document.documentElement.offsetWidth, window.innerWidth);
    const docHeight = Math.max(document.documentElement.offsetHeight, window.innerHeight);
    // check edge
    const btnSwitch = btnRef.current;
    if (x + btnSwitch.offsetWidth > docWidth) {
      // eslint-disable-next-line no-param-reassign
      x = docWidth - btnSwitch.offsetWidth;
    }
    if (y + btnSwitch.offsetHeight > docHeight) {
      // eslint-disable-next-line no-param-reassign
      y = docHeight - btnSwitch.offsetHeight;
    }
    if (x < 0) {
      // eslint-disable-next-line no-param-reassign
      x = 0;
    }
    if (y < 20) {
      // eslint-disable-next-line no-param-reassign
      y = 20;
    } // safe area for iOS Home indicator
    return [x, y];
  };

  const setSwitchPosition = (switchX: number, switchY: number) => {
    // eslint-disable-next-line no-param-reassign
    [switchX, switchY] = _getSwitchButtonSafeAreaXY(switchX, switchY);
    switchPosRef.current.x = switchX;
    switchPosRef.current.y = switchY;
    setBtnPos({
      x: switchX,
      y: switchY,
    });
    localStorage.setItem('lang_switch_x', switchX + '');
    localStorage.setItem('lang_switch_y', switchY + '');
  };

  useMount(() => {
    if (btnRef.current) {
      const initSwitchX = localStorage.getItem('lang_switch_x');
      const initSwitchY = localStorage.getItem('lang_switch_y');
      setSwitchPosition(
        !isNil(initSwitchX) && !Number.isNaN(+initSwitchX) ? +initSwitchX : 16,
        !isNil(initSwitchY) && !Number.isNaN(initSwitchY) ? +initSwitchY : window.innerHeight * 0.3,
      );
    }
  });

  /**
   * @description onTouchStart
   * @param {unknown} e desc
   * @returns {unknown} desc
   */
  const onTouchStart = (e) => {
    switchPosRef.current.startX = e.touches[0].pageX;
    switchPosRef.current.startY = e.touches[0].pageY;
    switchPosRef.current.hasMoved = false;
  };
  /**
   * @description onTouchEnd
   * @returns {unknown} desc
   */
  const onTouchEnd = () => {
    if (!switchPosRef.current.hasMoved) {
      return;
    }
    switchPosRef.current.startX = 0;
    switchPosRef.current.startY = 0;
    switchPosRef.current.hasMoved = false;
    setSwitchPosition(switchPosRef.current.endX, switchPosRef.current.endY);
  };

  useEffect(() => {
    const onTouchMove = (e) => {
      if (e.touches.length <= 0) {
        return;
      }
      const offsetX = e.touches[0].pageX - switchPosRef.current.startX,
        offsetY = e.touches[0].pageY - switchPosRef.current.startY;
      let x = Math.floor(switchPosRef.current.x - offsetX),
        y = Math.floor(switchPosRef.current.y - offsetY);
      [x, y] = _getSwitchButtonSafeAreaXY(x, y);
      setBtnPos({
        x,
        y,
      });
      switchPosRef.current.endX = x;
      switchPosRef.current.endY = y;
      switchPosRef.current.hasMoved = true;
      // preventDefault需要原生订阅事件才会生效
      e.preventDefault();
    };
    const btnDom = btnRef.current;
    btnDom?.addEventListener('touchmove', onTouchMove);
    return () => {
      btnDom?.removeEventListener('touchmove', onTouchMove);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (noBblPages.includes(location.pathname)) {
    return null;
  }

  return (
    <ActionSheet
      data={langs}
      renderItem="label"
      keygen="id"
      onSelect={(lang) => {
        localStorage.setItem('lang', lang.id);
        window.location.reload();
      }}
      cancel="取消/Cancel"
    >
      <div
        className={styles.langChanger}
        onTouchStart={onTouchStart}
        onTouchEnd={onTouchEnd}
        ref={btnRef}
        style={{
          right: btnPos.x,
          bottom: btnPos.y,
        }}
      >
        文/En
      </div>
    </ActionSheet>
  );
};

export default ChangeLang;
