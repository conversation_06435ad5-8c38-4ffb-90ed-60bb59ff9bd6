import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Icon from '@/_/components/Icon';
import { userStore } from '@/_/lego-stores/userStore';
import { t } from '@shein-bbl/react';
import { Tabbar } from 'shineout-mobile';
import styles from '../styles/footer.less';

const { Item } = Tabbar;

/**
 * @description Footer
 * @returns {unknown} desc
 */
const Footer: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [active, setActive] = useState(location.pathname);
  const { workbenchMenu } = userStore.useState();

  useEffect(() => {
    setActive(location.pathname);
  }, [location.pathname]);

  /**
   * @description handleChange
   * @param {unknown} name desc
   * @returns {unknown} desc
   */
  const handleChange = (name: string) => {
    navigate(name);
  };

  return (
    <footer className={styles.footer}>
      <Tabbar active={active} onChange={handleChange}>
        <Item
          icon={<Icon name="m-home-unselected" />}
          activeIcon={<Icon name="m-home-selected-fill" />}
          name="/"
        >
          {t('主页')}
        </Item>
        {(workbenchMenu || []).length > 0 ? (
          <Item
            icon={<Icon name="pc-workbench" />}
            activeIcon={<Icon name="pc-workbench-fill" />}
            name="/workbench"
          >
            {t('工作台')}
          </Item>
        ) : (
          <></>
        )}
        <Item
          icon={<Icon name="odec-personnel" />}
          activeIcon={<Icon name="odec-personnel-fill" />}
          name="/my"
        >
          {t('我的')}
        </Item>
      </Tabbar>
    </footer>
  );
};

export default Footer;
