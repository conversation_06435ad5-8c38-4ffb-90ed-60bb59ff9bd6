import React from 'react';
import { HashRouter as Router } from 'react-router-dom';
import { getCode, isInSheinIm, queryStringParse, setCode } from '@/utils';
import Routes from './Routes';

export const NoEnterpriseWeChatAuthList = [
  '#/access-control/home',
  '#/access-control/appointment',
  '#/access-control/appointment?isFromPassCardRebook=1',
  '#/access-control/appointment-record',
  '#/access-control/pass-card-list',
  '#/access-control/login',
  '#/access-control/login?redirect=home',
  '#/access-control/login?redirect=appointment',
  '#/access-control/login?redirect=appointment-record',
  '#/access-control/login?redirect=pass-card-list',
];

/**
 * @description checkIsNoEnterpriseWeChatAuth
 * @param {unknown} path desc
 * @returns {unknown} desc
 */
export const checkIsNoEnterpriseWeChatAuth = (path?: string): boolean => {
  if (NoEnterpriseWeChatAuthList.includes(path || window.location.hash)) {
    return true;
  }
  if ((path || window.location.hash).indexOf('#/access-control/pass-card-list/') > -1) {
    return true;
  }
  if (
    (path || window.location.hash).indexOf('#/access-control/login?redirect=pass-card-list/') > -1
  ) {
    return true;
  }
  if ((path || window.location.hash).indexOf('#/chrome-debug') > -1) {
    return true;
  }
  if ((path || window.location.hash).indexOf('#/canteen-device-scan-login') > -1) {
    return true;
  }
  return false;
};

// 企业微信id
const appIdMap = {
  dev: 'ww6e132f02586f7900',
  test: 'ww6e132f02586f7900',
  sit: 'ww5f025a4c596cbdd8',
  prod: 'wweb4abba124fabaad',
};

// 环境变量
const env = process.env.BUILD_TYPE;

const requestUrl = {
  dev: 'https://lpmpm-dev01.dotfashion.cn/',
  test: 'https://lpmpm-test01.dotfashion.cn/',
  sit: 'https://lpmpm-sit01.dotfashion.cn/',
  prod: 'https://lpmpm.dotfashion.cn/',
};

/**
 * @description getWXOauthUrl
 * @returns {unknown} desc
 */
export const getWXOauthUrl = () => {
  return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${
    appIdMap[env]
  }&redirect_uri=${encodeURIComponent(
    requestUrl[env] + window.location.hash,
  )}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`;
};

const injectConsole = async () => {
  if (env !== 'sit' && env !== 'prod') {
    try {
      const VConsole = await import('vconsole');
      new VConsole.default();
    } catch {
      // do nothing
    }
  }
};

export default /**
 * @description App
 * @returns {unknown} desc
 */
function App(): React.ReactElement {
  const { code } = queryStringParse(window.location.search);
  injectConsole();

  if (!checkIsNoEnterpriseWeChatAuth()) {
    if (code) {
      setCode(code as string);
    }

    if (!isInSheinIm()) {
      if (!code && !getCode()) {
        window.location.replace(getWXOauthUrl());
        return null;
      }
    }
  }

  return (
    <React.StrictMode>
      <Router>
        <Routes />
      </Router>
    </React.StrictMode>
  );
}
