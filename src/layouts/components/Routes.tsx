import React from 'react';
import { useLocation, useRoutes } from 'react-router-dom';
import ApmComp from '@/layouts/components/ApmComp';
import routes from '@@/routes/routes';

/**
 * @description Routes
 * @returns {unknown} desc
 */
const Routes: React.FC = () => {
  const location = useLocation();

  return (
    <>
      {useRoutes(routes)}
      <ApmComp path={location.pathname} />
    </>
  );
};

export default Routes;
