/** 用于企业微信端及班车项目之前已实现页面的布局 */
import React, { useEffect, useMemo } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { Page } from '@/_/components';
import { usePageTitle } from '@/_/hooks';
import { getUserInfo, USER_INFO_ERROR_CODE, userStore } from '@/_/lego-stores/userStore';
import ChangeLang from '@/layouts/components/ChangeLang';
import Footer from '@/layouts/components/Footer';
import OpenDebugButton from '@/pages/chrome-debug/components/OpenDebugButton';
import useChromeDebugBtn from '@/pages/chrome-debug/useChromeDebugBtn';
import { getCode, isInSheinIm, queryStringParse } from '@/utils';
import Watermark from '@shein-components/WatermarkSdk';
import { t } from '@shein-bbl/react';
import moment from 'moment';
import { checkIsNoEnterpriseWeChatAuth } from './App';

let watermarkInstance = null;

const PageLayout: React.FC = () => {
  userStore.useMount();
  const location = useLocation();
  usePageTitle(t('园区通'));

  const { info, homeMenu } = userStore.useState();

  const isShowFooter = useMemo(() => {
    const needShowPaths = ['/', '/my', '/workbench'];
    return needShowPaths.includes(location.pathname);
  }, [location.pathname]);

  const { code } = queryStringParse(window.location.search);

  const { isShowChromeDebugBtn, userInfo, ulpToken, openDefaultBrowser } = useChromeDebugBtn();

  useEffect(() => {
    if (!checkIsNoEnterpriseWeChatAuth()) {
      if (isInSheinIm()) {
        getUserInfo();
      } else {
        if (code) {
          getUserInfo(code as string);
        } else if (getCode()) {
          getUserInfo(getCode());
        }
      }
    }
  }, [code]);

  if (homeMenu?.length === 0 && !checkIsNoEnterpriseWeChatAuth()) {
    return null;
  }

  if (!checkIsNoEnterpriseWeChatAuth()) {
    if (!info || info.toString() === USER_INFO_ERROR_CODE) {
      return null;
    }
    if (!watermarkInstance) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      watermarkInstance = new Watermark({
        text: `${info.enName}(${info.workNum})\n${moment().format('YYYY-MM-DD')}`,
        density: 2,
        fontSize: 14,
      });
      watermarkInstance.addBodyWatermark();
      watermarkInstance.show();
    }
  }

  if (isInSheinIm()) {
    return (
      <Page contentClassName="flex flex-col items-center justify-center">
        <div className="text-[16px]">{t('欢迎使用园区通～')}</div>
      </Page>
    );
  }

  return (
    <div>
      {isShowChromeDebugBtn && (
        <OpenDebugButton
          openDefaultBrowser={openDefaultBrowser}
          ulpToken={ulpToken}
          userInfo={userInfo}
        />
      )}
      <ChangeLang />
      <Outlet />
      {isShowFooter && <Footer />}
    </div>
  );
};

export default PageLayout;
