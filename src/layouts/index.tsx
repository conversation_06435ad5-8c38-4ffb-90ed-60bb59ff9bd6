import React from 'react';
import { useLocation } from 'react-router-dom';
import NeedOAuthLoginLayout from './components/NeedOAuthLoginLayout';
import NoNeedOAuthLoginLayout from './components/NoNeedOAuthLoginLayout';
import { noNeedWeixinOauthLoginPages } from './config';

/**
 * @description isPathNoNeedOAuthLogin
 * @param {unknown} pathname desc
 * @returns {unknown} desc
 */
const isPathNoNeedOAuthLogin = (pathname: string) => {
  const noNeedWeixinOauthLoginPageRegs = noNeedWeixinOauthLoginPages.map(
    (regStr) => new RegExp(`^${regStr}`),
  );
  return noNeedWeixinOauthLoginPageRegs.some((reg) => reg.test(pathname));
};

/**
 * @description Main
 * @returns {unknown} desc
 */
const Main: React.FC = () => {
  const location = useLocation();

  if (isPathNoNeedOAuthLogin(location.pathname)) {
    return <NoNeedOAuthLoginLayout />;
  }
  return <NeedOAuthLoginLayout />;
};

export default Main;
