import React, { useEffect } from 'react';
import { render } from 'react-dom';
import {
  HashRouter as Router,
  Outlet,
  useLocation,
  useNavigate,
  useRoutes,
} from 'react-router-dom';
import routes from '@@/routes/routes';
import Icon from '@shein-components/Icon';
import 'shineout-mobile/index.css';
import '@/_/styles/global.less';
import './tailwind.css';
import { t } from '@shein-bbl/react';
import { setReactArgsFilter, setReactJoning } from '@shein-bbl/react';
import { Button } from 'shineout-mobile';
import useVconsole from './_/hooks/useVconsole';
import { checkIsLogin } from './pages/canteen-device/_/utils/token';

// bbl内部方法，防止调用t('something{}', <span>111</span>)时页面展示为[object Object]
setReactJoning();
setReactArgsFilter();

interface IProps {
  basename?: string;
}

/**
 * @description _404
 * @returns {unknown} desc
 */
const _404: React.FC = () => {
  const navigate = useNavigate();
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          padding: 30,
          height: 300,
          alignItems: 'center',
        }}
      >
        <Icon name="pc-404-blue-multic" fontSize={100} />
        <div
          style={{ textAlign: 'center', marginTop: 12 }}
          onClick={() => {
            navigate('/canteen-device/home');
          }}
        >
          <Button size="small" type="default">
            {t('返回首页')}
          </Button>
        </div>
      </div>
    </div>
  );
};

/**
 * @description CanteenDeviceLayout
 * @returns {unknown} desc
 */
const CanteenDeviceLayout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const isLogin = checkIsLogin();

  // // 检查登录，未登录直接跳转/login

  // useEffect(() => {
  //   if (!isLogin && location.pathname !== '/canteen-device/login') {
  //     navigate('/canteen/login');
  //   }
  //   if (isLogin && location.pathname === '/canteen-device/login') {
  //     navigate('/canteen/home');
  //   }
  // }, [isLogin, location.pathname, navigate]);

  return <Outlet />;
};

/**
 * @description Routes
 * @returns {unknown} desc
 */
function Routes(): ReturnType<typeof useRoutes> {
  const pathPrefix = '/canteen-device';
  const renderRoutes = routes[0].children
    .filter((item) => item.path.startsWith(pathPrefix))
    .map((item) => {
      return {
        ...item,
        errorElement: <_404 />,
      };
    });

  const renderRoutesWithLayout = [
    {
      element: <CanteenDeviceLayout />,
      children: renderRoutes,
      path: '/',
    },
    {
      element: <_404 />,
      path: '/*',
    },
  ];

  return useRoutes(renderRoutesWithLayout);
}

/**
 * @description App
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const App: React.FC<IProps> = (props) => {
  const { basename } = props;
  useVconsole();

  return (
    <Router basename={basename || '/'}>
      <Routes />
    </Router>
  );
};

render(<App />, document.getElementById('app'));
