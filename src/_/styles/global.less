* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

html {
  font-size: 10px;
}

body {
  font-family: -apple-system, PingFangSC-Regular, 'PingFang SC', sans-serif;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  background-color: darken(#f0f0f0, 8%);
}

#app {
  position: relative;
  max-width: 750px;
  min-height: 100vh;
  margin: 0 auto;
  background-color: #f2f2f2;
}

ul,
ol {
  list-style: none;
}

/* 兼容在pc端企业微信打开也能正常使用 */
@media screen and (min-width: 751px) {
  .sm-dropdown-menu-list {
    left: 50% !important;
    width: 750px !important;
    max-width: 750px !important;
    transform: translateX(-50%) !important;
  }

  .sm-popup-content {
    left: 50% !important;
    width: 750px !important;
    max-width: 750px !important;
    transform: translateX(-50%) !important;
  }

  [data-tool-element^='drawer'].sm-container {
    left: 50% !important;
    width: 750px !important;
    max-width: 750px !important;
    transform: translateX(-50%) !important;
  }
}
