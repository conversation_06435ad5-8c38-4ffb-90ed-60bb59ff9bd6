import { t } from '@shein-bbl/react';
export interface IUserInfo {
  workNum: string;
  staffId: number;
  staffName: string;
  avatar: string;
  deptName: string;
  position: string;
  enName: string;
}

export interface IMenuItem {
  path: string;
  annotate: string;
  icon: string;
  name: string;
  customProperties: string;
  children: IMenuItem[];
}

/**
 * 通用状态枚举
 * */
export enum ECommonStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1,
}

/**
 * 通用状态对应的名称
 * */
export const OCommonStatusMap = {
  [ECommonStatus.ENABLED]: t('启用'),
  [ECommonStatus.DISABLED]: t('禁用'),
};

/**
 * 企业微信sdk配置信息
 * */
export interface IWechatConfig {
  /** APPID */
  agentId?: string;
  /** 拿到的签名signature */
  signature?: string;
  /** 随机数nonceStr */
  noncestr?: string;
  /** 上面main方法中拿到的时间戳timestamp */
  timestamp?: string;
  /** jsapi_ticket */
  jsapiTicket?: string;
  /** 请求链路id */
  traceId?: string;
}

/**
 * 用户角色权限
 * */
export interface IUserPageAuthItem {
  url: string;
  name: string;
  buttonList: { name: string; code: string }[];
}

/**
 * 字典枚举
 * */
export interface IDictionaryItem {
  /** 数据字典编码 */
  dictionaryCode?: string;
  /** 字典名称 */
  dictionaryName?: string;
  /** 字典状态 */
  dictionaryStatus?: number;
}

/**
 * 在职/离职状态
 * */
export enum EStaffStatus {
  /** 在职 */
  IN_SERVICE = 1,
  /** 离职 */
  LEAVE_OFFICE = 0,
}

export enum ECommutingTypeEnum {
  /** 白天上班 */
  DAY_GO_TO_WORK = 0,
  /** 白天下班 */
  DAY_GO_OFF_WORK = 1,
  /** 夜晚上班 */
  NIGHT_GO_TO_WORK = 2,
  /** 夜晚下班 */
  NIGHT_GO_OFF_WORK = 3,
}

export enum ETaskStatusEnum {
  /* 待出发 */
  WAITING = 0,
  /* 验票中 */
  CHECKING = 1,
  /* 进行中 */
  IN_PROGRESS = 2,
  /* 已完成 */
  COMPLETED = 3,
  /* 缺勤 */
  BREACH_OF_CONTRACT = 4,
  /* 已取消 */
  CANCELED = 5,
}

export const TaskStatusEnumMap = {
  [ETaskStatusEnum.WAITING]: t('待出发'),
  [ETaskStatusEnum.CHECKING]: t('验票中'),
  [ETaskStatusEnum.IN_PROGRESS]: t('进行中'),
  [ETaskStatusEnum.COMPLETED]: t('已完成'),
  [ETaskStatusEnum.BREACH_OF_CONTRACT]: t('缺勤'),
  [ETaskStatusEnum.CANCELED]: t('已取消'),
};

export const TaskStatusEnumOptions = [
  {
    value: ETaskStatusEnum.WAITING,
    name: t('待出发'),
    color: '#F56C0A',
    lightColor: '#FFF3E2',
  },
  {
    value: ETaskStatusEnum.CHECKING,
    name: t('验票中'),
    color: '#00A85F',
  },
  {
    value: ETaskStatusEnum.IN_PROGRESS,
    name: t('进行中'),
    color: '#197AFA',
    lightColor: '#E9F5FE',
  },
  {
    value: ETaskStatusEnum.COMPLETED,
    name: t('已完成'),
    color: '#197AFA',
    lightColor: '#E9F5FE',
  },
  {
    value: ETaskStatusEnum.BREACH_OF_CONTRACT,
    name: t('缺勤'),
    color: '#EB4242',
    lightColor: '#F96156',
  },
  {
    value: ETaskStatusEnum.CANCELED,
    name: t('已取消'),
    color: '#999DA8',
    lightColor: '#F96156',
  },
];
