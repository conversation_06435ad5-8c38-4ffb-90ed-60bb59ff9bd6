import { CSSProperties } from 'react';
import classNames from 'classnames';
import styles from './index.less';

/**
 * @description AutoGapLabel
 * @param {unknown} param desc
 * @returns {unknown} desc
 */
const AutoGapLabel: React.FC<{ label: string; className?: string; styles?: CSSProperties }> = ({
  label,
  styles: parentStyles,
  className: parentClassName,
}) => {
  return (
    <div className={classNames(styles.label, parentClassName)}>
      {label?.split('')?.map((letter) => {
        return (
          <span key={letter} className={styles.text} style={parentStyles}>
            {letter}
          </span>
        );
      })}
    </div>
  );
};

export default AutoGapLabel;
