import React from 'react';
import { getDictionaryByCode } from '@/_/api';
import { ECommonStatus } from '@/_/interfaces';
import { t } from '@shein-bbl/react';
import { useRequest, useSetState, useUpdateEffect } from 'ahooks';
import { Cell, NPicker } from 'shineout-mobile';
import { IDictionaryItem } from '../../interfaces';

interface IDictionaryPickerProps {
  /** 字典编码 */
  dictionaryCode: string;
  title?: string;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  /** 查询，启用/禁用/全部，默认启用 */
  status?: ECommonStatus;
  errorMessage?: string;
}

/**
 * @description DictionaryPicker
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const DictionaryPicker: React.FC<IDictionaryPickerProps> = (props) => {
  const {
    title,
    value,
    onChange,
    dictionaryCode,
    disabled,
    status = ECommonStatus.ENABLED,
    errorMessage,
  } = props;
  const [state, setState] = useSetState({
    showPicker: false,
    innerValue: value,
    displayValue: '',
  });
  const { data, loading } = useRequest(getDictionaryByCode, {
    defaultParams: [
      {
        code: dictionaryCode,
        status,
      },
    ],
    onSuccess(data?: IDictionaryItem[]): void {
      const selectedItem = data?.find((item) => item.dictionaryCode === value);
      if (selectedItem) {
        setState({
          displayValue: selectedItem.dictionaryName,
        });
      }
    },
  });

  useUpdateEffect(() => {
    setState({
      innerValue: value,
      displayValue: data?.find((item) => item.dictionaryCode === value)?.dictionaryName,
    });
  }, [value]);

  return (
    <>
      <Cell
        label={null}
        isLink={!disabled}
        onClick={() => {
          if (disabled) {
            return;
          }
          setState({
            showPicker: true,
          });
        }}
        value={state.displayValue}
        placeholder={t('请选择')}
        valueAlign="right"
        errorMessage={errorMessage}
      />
      <NPicker
        drawer={{
          position: 'bottom',
          visible: state.showPicker,
        }}
        title={title}
        loading={loading}
        data={data || []}
        value={state.innerValue || data?.[0]?.dictionaryCode}
        renderItem="dictionaryName"
        format="dictionaryCode"
        disabled={disabled}
        keygen="dictionaryCode"
        onChange={(val) =>
          setState({
            innerValue: val,
          })
        }
        onCancel={() =>
          setState({
            showPicker: false,
          })
        }
        onOk={(val: string) => {
          onChange?.(val);
          setState({
            showPicker: false,
          });
        }}
      />
    </>
  );
};

export default DictionaryPicker;
