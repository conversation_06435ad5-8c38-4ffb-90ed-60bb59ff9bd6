业务组件的目录说明，用于方便快速知晓其大致功能，如果新增了业务组件，请及时维护此说明

```markdown
.
├── CityPicker // 根据省份获取城市的选择器
├── DictionaryPicker // 字典枚举选择器
├── Empty // 空页面显示组件
├── StatusHeader // 状态icon+文本的页面顶部部分，使用最多可以用工管理下面的页面
├── StatusTag // 设定了既定颜色组合的Tag
├── ProvincePicker // 省份选择器
├── RichText // 富文本（已处理XSS）
├── Page // 页面布局组件，包含区域内滚动和底部footer
├── PageContentLoading // 页面内容加载中组件
├── PageLoading // 页面加载中组件
└── index.ts // 所有组件的入口

```