import React, { useRef } from 'react';
import { useMount, useUnmount } from 'ahooks';
import classnames from 'classnames';
import styles from './index.less';

interface IPageProps {
  footer?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  contentClassName?: string;
  footerClassName?: string;
  title?: string;
  contentRef?: React.Ref<HTMLDivElement>;
}

/**
 * @description Page
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Page: React.FC<IPageProps> = (props) => {
  const {
    footer,
    className,
    style,
    children,
    contentClassName,
    footerClassName,
    title,
    contentRef,
  } = props;
  const preTitle = useRef<string>();

  useMount(() => {
    preTitle.current = document.title;
    if (title) {
      document.title = title;
    }
  });

  useUnmount(() => {
    if (preTitle.current) {
      document.title = preTitle.current;
    }
  });

  return (
    <div className={classnames(styles.page, className)} style={style}>
      <div ref={contentRef} className={classnames(styles.content, contentClassName)}>
        {children}
      </div>
      {!!footer && <div className={classnames(styles.footer, footerClassName)}>{footer}</div>}
    </div>
  );
};

export default Page;
