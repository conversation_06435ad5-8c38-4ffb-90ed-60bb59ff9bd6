.content {
  min-height: 0;
  padding: 12px;
  overflow: auto;
  background-color: #f4f5f8;
  flex: 1;
}

.footer {
  padding: 8px 16px;
  background-color: #fff;
}

@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
  .footer {
    padding-bottom: calc(8px + constant(safe-area-inset-bottom)); // 兼容 IOS < 11.2
    padding-bottom: calc(8px + env(safe-area-inset-bottom)); // 兼容 IOS < 11.2
  }
}

.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
}
