import React from 'react';
import Icon from '@/_/components/Icon';
import classNames from 'classnames';
import styles from './index.less';

export interface IStatusHeaderProps {
  type?: 'primary' | 'warning' | 'danger' | 'success';
  statusDesc?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  /** 完全使用自定义的配置 */
  custom?: {
    bgColor: string;
    iconColor: string;
    icon: string;
  };
}

const headerConfig: Record<
  IStatusHeaderProps['type'],
  {
    bgColor: string;
    icon: string;
    iconColor: string;
  }
> = {
  primary: {
    bgColor: '#E9F5FE',
    icon: 'pc-info-circle-fill',
    iconColor: '#197AFA',
  },
  warning: {
    bgColor: '#FFF3E2',
    icon: 'pc-attention-circle-fill',
    iconColor: '#F56C0A',
  },
  danger: {
    bgColor: '#FCE6E6',
    icon: 'pc-attention-circle-fill',
    iconColor: '#EB4242',
  },
  success: {
    bgColor: '#E4FCED',
    icon: 'pc-check-circle-fill',
    iconColor: '#00A85F',
  },
};

/**
 * @description StatusHeader
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const StatusHeader: React.FC<IStatusHeaderProps> = (props) => {
  const { type, statusDesc, className, style, custom } = props;

  const config = headerConfig[type];

  return (
    <header
      className={classNames(styles.header, className)}
      style={{
        ...style,
        backgroundColor: custom ? custom?.bgColor : config.bgColor,
      }}
    >
      <Icon
        name={custom ? custom?.icon : config.icon}
        fontSize={16}
        color={custom ? custom?.iconColor : config.iconColor}
      />
      <span className="format-pre">{statusDesc}</span>
    </header>
  );
};

StatusHeader.defaultProps = {
  type: 'primary',
};

export default StatusHeader;
