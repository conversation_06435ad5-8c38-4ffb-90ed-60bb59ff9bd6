import React from 'react';
import { Control, Controller, FieldError, RegisterOptions } from 'react-hook-form';

export interface IFieldProps {
  /** 字段名，支持path */
  name: string;
  /** 内部属性，由Form实现注入 */
  control?: Control;
  /** 字段的校验设置 */
  rules?: RegisterOptions;
  defaultValue?: unknown;
  shouldUnregister?: boolean;
  disabled?: boolean;
  children?:
    | JSX.Element
    | ((options?: {
        onChange: (value: any) => void;
        onBlur: () => void;
        value: any;
        disabled: boolean;
        name: string;
        error: FieldError;
        errorMessage: string;
      }) => any);
}

/**
 * @description Field
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Field = (props: IFieldProps): JSX.Element => {
  const { control, children, ...resetProps } = props;
  if (!control) {
    if (typeof children === 'function') {
      return children?.();
    }
    return children;
  }

  return (
    <Controller
      control={control}
      {...resetProps}
      render={({ field, formState, fieldState }) => {
        const { onChange, onBlur, value, disabled, name } = field;
        const { error } = fieldState;
        const { errors } = formState;
        if (React.isValidElement(children) && typeof children === 'object' && children !== null) {
          const childrenProps = children?.props as any;
          /**
           * @description handleChange
           * @param {unknown} e desc
           * @returns {unknown} desc
           */
          const handleChange = (e) => {
            if (typeof e === 'object' && e !== null && 'target' in e) {
              const _value = e.target?.value;
              childrenProps?.onChange?.(e);
              onChange?.(_value);
            } else {
              childrenProps?.onChange?.(e);
              onChange?.(e);
            }
          };
          /**
           * @description handleBlur
           * @param {unknown} e desc
           * @returns {unknown} desc
           */
          const handleBlur = (e) => {
            childrenProps?.onBlur?.(e);
            onBlur?.();
          };
          return React.cloneElement(children, {
            ...((children.props as any) || {}),
            onChange: handleChange,
            onBlur: handleBlur,
            value,
            disabled,
            name,
            error: error ? !errors[name]?.message : false,
            errorMessage: errors[name]?.message || '',
          });
        } else if (typeof children === 'function') {
          return children?.({
            onChange,
            onBlur,
            value,
            disabled,
            name,
            error,
            errorMessage: (errors[name]?.message as string) || '',
          });
        }
        return children;
      }}
    />
  );
};

Field.displayName = 'FormField';
export default Field;
