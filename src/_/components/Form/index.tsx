import React, { PropsWithChildren } from 'react';
import { DefaultValues, FieldErrors, FieldValues, useForm, UseFormReturn } from 'react-hook-form';
import Field from './Field';

export interface FormProps<FieldValues> {
  defaultValues?: DefaultValues<FieldValues>;
  onSubmit?: (data: FieldValues) => void;
  values?: FieldValues;
  style?: React.CSSProperties;
  className?: string;
  children?: React.ReactNode;
  formRef?: React.MutableRefObject<UseFormReturn<FieldValues, any, undefined>>;
  onError?: (errors: FieldErrors<FieldValues>) => void;
}

const Index = <T extends FieldValues>(props: PropsWithChildren<FormProps<T>>) => {
  const { defaultValues, onSubmit, children, values, style, className, formRef, onError } = props;
  const methods = useForm<T>({
    defaultValues,
    values,
    mode: 'onChange',
  });
  const { handleSubmit } = methods;
  if (formRef) {
    formRef.current = methods;
  }

  // 工具方法，实现递归渲染children，注入control
  const renderChildren = (children: React.ReactNode) => {
    return React.Children.map(children, (child) => {
      if (
        React.isValidElement(child) &&
        typeof child === 'object' &&
        child !== null &&
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        child?.type?.displayName === 'FormField' &&
        child.props.name
      ) {
        return React.cloneElement(child, {
          ...child.props,
          control: methods.control,
          key: child.props.name,
        });
      }
      if (
        React.isValidElement(child) &&
        typeof child === 'object' &&
        child !== null &&
        (child as any).props?.children
      ) {
        return React.cloneElement(child, {
          ...((child as any).props || {}),
          children: renderChildren((child.props as any).children),
        });
      }
      return child;
    });
  };

  return (
    <form className={className} noValidate style={style} onSubmit={handleSubmit(onSubmit, onError)}>
      {renderChildren(children)}
    </form>
  );
};

Index.Field = Field;

export default Index;
