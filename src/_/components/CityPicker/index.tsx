import React, { useRef } from 'react';
import { getCityByProvince } from '@/_/api';
import { t } from '@shein-bbl/react';
import { useMount, useRequest, useSetState, useUpdateEffect } from 'ahooks';
import { Cell, NPicker, Toast } from 'shineout-mobile';

interface ICityPickerProps {
  provinceName: string;
  title?: string;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  errorMessage?: string;
  placeholder?: string;
}

const CityPicker: React.FC<ICityPickerProps> = (props) => {
  const { title, value, onChange, disabled, provinceName, errorMessage, placeholder } = props;
  const firstInitRef = useRef(false);
  const [state, setState] = useSetState({
    showPicker: false,
    innerValue: value,
    cities: [],
  });
  const { loading, run } = useRequest(getCityByProvince, {
    manual: true,
    onSuccess(data: { cityId: string; cityName: string }[]): void {
      setState({
        cities: data,
      });
      firstInitRef.current = true;
    },
  });

  useMount(() => {
    if (provinceName) {
      run({
        provinceName,
      });
    }
  });

  useUpdateEffect(() => {
    if (!provinceName) {
      setState({
        cities: [],
        innerValue: null,
      });
    } else {
      run({
        provinceName,
      });
    }
    if (firstInitRef.current) {
      onChange?.(null);
    }
  }, [provinceName]);

  useUpdateEffect(() => {
    setState({
      innerValue: value,
    });
  }, [value]);

  return (
    <>
      <Cell
        label={null}
        isLink={!disabled}
        onClick={() => {
          if (disabled) {
            return;
          }
          if (!provinceName) {
            Toast.info(t('请先选择省份'));
            return;
          }
          setState({
            showPicker: true,
          });
        }}
        value={value}
        placeholder={placeholder ?? t('请选择')}
        valueAlign="right"
        errorMessage={errorMessage}
      />
      <NPicker
        drawer={{
          position: 'bottom',
          visible: state.showPicker,
        }}
        title={title}
        loading={loading}
        data={state.cities || []}
        value={state.innerValue || state.cities?.[0]?.cityName}
        renderItem="cityName"
        format="cityName"
        disabled={disabled || !state.cities?.length}
        keygen="cityName"
        onChange={(val) =>
          setState({
            innerValue: val,
          })
        }
        onCancel={() =>
          setState({
            showPicker: false,
          })
        }
        onOk={(val: string) => {
          // 此时城市数据为空，直接点击确定val会是一个空数组
          if (Array.isArray(val)) {
            return;
          }
          onChange?.(val);
          setState({
            showPicker: false,
          });
        }}
      />
    </>
  );
};

export default CityPicker;
