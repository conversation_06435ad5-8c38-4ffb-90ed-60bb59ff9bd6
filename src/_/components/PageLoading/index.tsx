import React from 'react';
import Page from '../Page';
import PageContentLoading from '../PageContentLoading';

interface IPageLoadingProps {
  title?: string;
}

/**
 * @description PageLoading
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const PageLoading: React.FC<IPageLoadingProps> = (props) => {
  const { title } = props;

  return (
    <Page title={title}>
      <PageContentLoading />
    </Page>
  );
};

export default PageLoading;
