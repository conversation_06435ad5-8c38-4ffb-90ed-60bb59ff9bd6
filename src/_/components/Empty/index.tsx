import React from 'react';
import Icon from '@/_/components/Icon';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';

interface IMainProps {
  /** icon name */
  icon?: string;
  /** 无数据描述 */
  desc?: string;
  className?: string;
}

/**
 * @description Main
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Main: React.FC<IMainProps> = (props) => {
  const { icon = 'pc-no data-multic', desc = t('暂无数据'), className } = props;

  return (
    <div className={classNames('flex h-full flex-col justify-center items-center', className)}>
      <Icon name={icon} fontSize={64} color="#999da8" />
      <div style={{ color: '#999da8', marginTop: 6 }}>{desc}</div>
    </div>
  );
};

export default Main;
