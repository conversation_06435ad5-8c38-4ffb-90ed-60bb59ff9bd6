import React from 'react';
import XssText from '@shein-components/XssText';
import classNames from 'classnames';
import styles from './index.less';

interface IRichTextTextProps {
  /**
   * 富文本字符串
   * */
  template: string;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * @description RichText
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const RichText: React.FC<IRichTextTextProps> = (props) => {
  const { template, className, style } = props;

  return (
    <div className={classNames(styles.richEditorContent, 'w-e-text', className)} style={style}>
      <XssText template={template} />
    </div>
  );
};

export default RichText;
