.richEditorContent {
  box-sizing: border-box;
  word-break: break-all;
  white-space: pre-line;

  ol li {
    list-style: decimal;
  }

  ul li {
    list-style: inside;
  }

  img:hover {
    box-shadow: none;
  }

  ul {
    padding-left: 10px;
  }

  p {
    word-break: break-all;
  }

  img {
    max-width: 100%;
  }

  ol {
    padding-left: 20px;
  }

  table {
    max-width: 100%;
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
  }

  table td {
    height: 30px;
    min-height: 30px;
    padding: 3px 5px;
    background-color: #fff;
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
  }

  table th {
    border-bottom: 2px solid #ccc;
    border-right: 1px solid #ccc;
    text-align: center;
    background-color: #f1f1f1;
  }
}
