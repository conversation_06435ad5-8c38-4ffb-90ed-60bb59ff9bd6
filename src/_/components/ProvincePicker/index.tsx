import React from 'react';
import { getProvince } from '@/_/api';
import { t } from '@shein-bbl/react';
import { useRequest, useSetState, useUpdateEffect } from 'ahooks';
import { Cell, NPicker } from 'shineout-mobile';

interface IProvincePickerProps {
  title?: string;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  errorMessage?: string;
  placeholder?: string;
}

/**
 * @description ProvincePicker
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const ProvincePicker: React.FC<IProvincePickerProps> = (props) => {
  const { title, value, onChange, disabled, errorMessage, placeholder } = props;
  const { data, loading } = useRequest(getProvince);
  const [state, setState] = useSetState({
    showPicker: false,
    innerValue: value,
  });

  useUpdateEffect(() => {
    setState({
      innerValue: value,
    });
  }, [value]);

  return (
    <>
      <Cell
        label={null}
        isLink={!disabled}
        onClick={() => {
          if (disabled) {
            return;
          }
          setState({
            showPicker: true,
          });
        }}
        value={value}
        placeholder={placeholder ?? t('请选择')}
        valueAlign="right"
        errorMessage={errorMessage}
      />
      <NPicker
        drawer={{
          position: 'bottom',
          visible: state.showPicker,
        }}
        title={title}
        loading={loading}
        data={data || []}
        value={state.innerValue || data?.[0]?.provinceName}
        renderItem="provinceName"
        format="provinceName"
        disabled={disabled}
        keygen="provinceName"
        onChange={(val) =>
          setState({
            innerValue: val,
          })
        }
        onCancel={() =>
          setState({
            showPicker: false,
          })
        }
        onOk={(val: string) => {
          onChange?.(val);
          setState({
            showPicker: false,
          });
        }}
      />
    </>
  );
};

export default ProvincePicker;
