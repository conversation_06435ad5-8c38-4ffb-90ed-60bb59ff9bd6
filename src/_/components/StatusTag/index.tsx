import React from 'react';
import { Tag, TagProps } from 'shineout-mobile';

interface IStatusTagProps {
  type?: 'default' | 'primary' | 'success' | 'danger' | 'warning';
  shape?: TagProps['shape'];
  custom?: {
    color: string;
    textColor: string;
  };
}

const tagConfig: Record<
  IStatusTagProps['type'],
  {
    color: string;
    textColor: string;
  }
> = {
  default: {
    color: '#F4F5F8',
    textColor: '#141737',
  },
  primary: {
    color: '#E9F5FE',
    textColor: '#197AFA',
  },
  success: {
    color: '#E4FCED',
    textColor: '#00A85F',
  },
  danger: {
    color: '#FCE6E6',
    textColor: '#EB4242',
  },
  warning: {
    color: '#FFF3E2',
    textColor: '#F56C0A',
  },
};

/**
 * @description StatusTag
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const StatusTag: React.FC<IStatusTagProps> = (props) => {
  const { type, shape, custom, children } = props;

  return (
    <Tag
      color={custom ? custom.color : tagConfig[type].color}
      textColor={custom ? custom?.textColor : tagConfig[type].textColor}
      shape={shape}
    >
      {children}
    </Tag>
  );
};

export default StatusTag;
