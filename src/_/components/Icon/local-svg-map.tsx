const LocalSvgMap: Record<string, React.ReactNode> = {
  'm-home-selected-fill': (
    <svg viewBox="0 0 60 60" width="24px" height="24px">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g transform="translate(-111.000000, -199.000000)">
          <g transform="translate(111.000000, 199.000000)">
            <path d="M33.6057666,13.7111027 L48.6057666,24.9892982 C50.1132104,26.1227146 51,27.8989616 51,29.7849678 L51,45 C51,48.3137085 48.3137085,51 45,51 L15,51 C11.6862915,51 9,48.3137085 9,45 L9,29.7849678 C9,27.8989616 9.88678961,26.1227146 11.3942334,24.9892982 L26.3942334,13.7111027 C28.5299126,12.1053289 31.4700874,12.1053289 33.6057666,13.7111027 Z M37.5,38 L22.5,38 C21.6715729,38 21,38.6715729 21,39.5 C21,40.3284271 21.6715729,41 22.5,41 L22.5,41 L37.5,41 C38.3284271,41 39,40.3284271 39,39.5 C39,38.6715729 38.3284271,38 37.5,38 L37.5,38 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  ),
  'pc-workbench': (
    <svg viewBox="0 0 24 24" width="24px" height="24px">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <path
          d="M21,3 C22.1045695,3 23,3.83573513 23,4.86666667 L23,15.1333333 C23,16.1642649 22.1045695,17 21,17 L13,17 L13,19.0645161 L18,19.0645161 C18.5522847,19.0645161 19,19.497789 19,20.0322581 C19,20.5285508 18.6139598,20.9375876 18.1166211,20.9934893 L18,21 L6,21 C5.44771525,21 5,20.5667272 5,20.0322581 C5,19.5359653 5.38604019,19.1269286 5.88337887,19.0710268 L6,19.0645161 L11,19.0645161 L11,17 L3,17 C1.8954305,17 1,16.1642649 1,15.1333333 L1,4.86666667 C1,3.83573513 1.8954305,3 3,3 L21,3 Z M20,4.86666667 L4,4.86666667 C3.44771525,4.86666667 3,5.31438192 3,5.86666667 L3,14.1333333 C3,14.6856181 3.44771525,15.1333333 4,15.1333333 L20,15.1333333 C20.5522847,15.1333333 21,14.6856181 21,14.1333333 L21,5.86666667 C21,5.31438192 20.5522847,4.86666667 20,4.86666667 Z M8,9.87096774 C8.51283584,9.87096774 8.93550716,10.244555 8.99327227,10.7258505 L9,10.8387097 L9,12.7741935 C9,13.3086627 8.55228475,13.7419355 8,13.7419355 C7.48716416,13.7419355 7.06449284,13.3683482 7.00672773,12.8870527 L7,12.7741935 L7,10.8387097 C7,10.3042406 7.44771525,9.87096774 8,9.87096774 Z M12,7.93548387 C12.5128358,7.93548387 12.9355072,8.30907115 12.9932723,8.79036665 L13,8.90322581 L13,12.7741935 C13,13.3086627 12.5522847,13.7419355 12,13.7419355 C11.4871642,13.7419355 11.0644928,13.3683482 11.0067277,12.8870527 L11,12.7741935 L11,8.90322581 C11,8.36875669 11.4477153,7.93548387 12,7.93548387 Z M16,6 C16.5128358,6 16.9355072,6.37358728 16.9932723,6.85488278 L17,6.96774194 L17,12.7741935 C17,13.3086627 16.5522847,13.7419355 16,13.7419355 C15.4871642,13.7419355 15.0644928,13.3683482 15.0067277,12.8870527 L15,12.7741935 L15,6.96774194 C15,6.43327282 15.4477153,6 16,6 Z"
          fillRule="nonzero"
        ></path>
      </g>
    </svg>
  ),
  'odec-personnel': (
    <svg viewBox="0 0 24 24" width="24px" height="24px">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <path d="M14.0094537,13.001596 L14.555687,13.0076478 L15.0432779,13.019651 L15.4783403,13.0388506 C15.546813,13.0427195 15.6133517,13.04694 15.6780838,13.0515383 L15.6780838,13.0515383 L16.0458167,13.0838673 C16.8000159,13.1646324 17.2919549,13.3179925 17.8014673,13.6009384 C18.7407725,14.122598 19.4984557,14.9484379 19.9699621,15.9570483 L19.9699621,15.9570483 L20.0590421,16.1567816 C20.0730017,16.1897971 20.0865267,16.222743 20.0996303,16.2557199 L20.0996303,16.2557199 L20.1733004,16.454759 C20.1847712,16.4882627 20.1958471,16.5219987 20.2065412,16.5560676 L20.2065412,16.5560676 L20.2662317,16.765282 L20.3173969,16.9873187 C20.3252493,17.0256617 20.332773,17.0647402 20.3399812,17.104655 L20.3399812,17.104655 L20.3795504,17.3549828 L20.4121834,17.6302102 L20.4385159,17.9351679 L20.4591835,18.2746869 L20.4748218,18.6535981 L20.4902399,19.3063929 L20.4991274,20.3597472 L20.5,21.6296783 C20.5,22.181963 20.0522847,22.6296783 19.5,22.6296783 L19.5,22.6296783 L4.5,22.6296783 C3.94761611,22.6296783 3.5,22.1818083 3.5,21.6294244 L3.5,21.6294244 L3.50032886,20.3377345 L3.50317252,19.7356652 L3.51576199,18.9484288 L3.53111806,18.4922362 L3.55317563,18.0840584 L3.58290413,17.7179891 C3.58855198,17.6601585 3.59455985,17.6038362 3.60094793,17.5488994 L3.60094793,17.5488994 L3.64400054,17.2349186 C3.6679697,17.0852561 3.6957243,16.9458471 3.72780967,16.8133695 L3.72780967,16.8133695 L3.79791634,16.5566715 C3.86141982,16.3488174 3.93796304,16.1540644 4.03007058,15.9570319 C4.50155845,14.9484348 5.25922166,14.1225957 6.19855417,13.6009209 C6.70805476,13.3179849 7.19998921,13.1646291 7.95418624,13.0838659 L7.95418624,13.0838659 L8.32191832,13.0515375 C8.38665035,13.0469394 8.45318897,13.0427189 8.52166154,13.0388501 L8.52166154,13.0388501 L8.95672337,13.0196508 L9.44431381,13.0076477 L9.99054656,13.001596 L14.0094537,13.001596 Z M13.9717164,15.0014216 L10.0301256,15.0014216 C9.94556115,15.0019108 9.86362115,15.0025552 9.78419707,15.0033671 L9.78419707,15.0033671 L9.13437968,15.0155196 L8.7679627,15.0296077 L8.44890332,15.0491598 L8.17199174,15.0747547 C8.0432046,15.0891592 7.92826922,15.1060466 7.82425508,15.1257425 L7.82425508,15.1257425 L7.62992016,15.1689751 C7.44841029,15.2161451 7.3027707,15.2754164 7.16955732,15.3493925 C6.60357759,15.6637193 6.13738136,16.1718647 5.84187775,16.8039999 L5.84187775,16.8039999 L5.80795293,16.8800167 L5.74662415,17.0402976 L5.69355033,17.2158897 C5.6853633,17.2467187 5.67749825,17.2784014 5.66994419,17.3110457 L5.66994419,17.3110457 L5.6282638,17.5193136 L5.59351971,17.7558379 C5.58827775,17.797901 5.58330283,17.8413572 5.57858395,17.8863144 L5.57858395,17.8863144 L5.55325531,18.1749323 L5.53354432,18.5047517 L5.51892351,18.8809507 L5.50886541,19.3087073 L5.50284257,19.7931997 L5.5,20.629 L18.499,20.629 L18.494154,19.6509065 L18.4832307,19.0134987 L18.4650339,18.4856043 L18.4481704,18.1867268 L18.4270373,17.9248387 L18.4012195,17.6952667 L18.3703024,17.4933377 L18.3338709,17.3143782 L18.2915104,17.1537151 C18.2839332,17.128203 18.2760916,17.1032585 18.2679771,17.0787843 L18.2679771,17.0787843 L18.2159451,16.9368033 L18.1581498,16.8040054 C17.8626443,16.1718825 17.3964275,15.6637276 16.8304598,15.3494076 C16.6972406,15.2754274 16.5515969,15.2161531 16.3701271,15.1689809 L16.3701271,15.1689809 L16.1758486,15.1257465 C16.1411896,15.1191809 16.1053188,15.1129275 16.0681277,15.106974 L16.0681277,15.106974 L15.8282725,15.0747568 C15.7853674,15.0699552 15.7409254,15.0654294 15.6948381,15.0611675 L15.6948381,15.0611675 L15.3977055,15.0386659 L15.0558874,15.0219179 L14.6641836,15.0103451 L13.9717164,15.0014216 Z M12,1.32718838 C14.8568672,1.32718838 17.1728116,3.64313513 17.1728116,6.5 C17.1728116,9.356869 14.856869,11.6728116 12,11.6728116 C9.143131,11.6728116 6.82718838,9.356869 6.82718838,6.5 C6.82718838,3.64313513 9.14313284,1.32718838 12,1.32718838 Z M12,3.32718838 C10.2477028,3.32718838 8.82718838,4.74770419 8.82718838,6.5 C8.82718838,8.2522995 10.2477005,9.67281162 12,9.67281162 C13.7522995,9.67281162 15.1728116,8.2522995 15.1728116,6.5 C15.1728116,4.74770419 13.7522972,3.32718838 12,3.32718838 Z"></path>
      </g>
    </svg>
  ),
  'pc-bookmark': (
    <svg viewBox="0 0 24 24" width="24px" height="24px">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <path
          d="M16,1 C16.9506639,1 17.908738,1.91115702 17.9938715,2.86373028 L18,3 L18,4 L19,4 C19.9506639,4 20.908738,4.91115702 20.9938715,5.86373028 L21,6 L21,22 C21,22.7372395 20.2294141,23.2210206 19.5654286,22.9006374 L19.5654286,22.9006374 L13.5,19.974 L7.4345714,22.9006374 C6.80553254,23.2041583 6.08081902,22.7859362 6.00627587,22.1142196 L6.00627587,22.1142196 L6,22 L6,19.145 L4.4345714,19.9006374 C3.84253482,20.1863041 3.16574838,19.8326414 3.02602791,19.2300311 L3.00627587,19.1142196 L3,19 L3,3 C3,2.07249687 3.97084525,1.09336748 4.87186958,1.00627205 L5,1 L16,1 Z M15.5,3 L5.5,3 C5.25454011,3 5.05039163,3.17687516 5.00805567,3.41012437 L5,3.5 L5,16.6106026 C5,16.6858424 5.01698041,16.7601121 5.04967574,16.8278767 C5.15633993,17.0489498 5.40435467,17.155955 5.63282724,17.0927268 L5.71727417,17.0609268 L6,16.924 L6,6 C6,5.08196597 6.95458209,4.09422831 7.86965188,4.00633074 L8,4 L16,4 L16,3.5 C16,3.25454011 15.8231248,3.05039163 15.5898756,3.00805567 L15.5,3 Z"
          fillRule="nonzero"
        ></path>
      </g>
    </svg>
  ),
  'arr-right': (
    <svg viewBox="0 0 1024 1024" width="24px" height="24px">
      <path d="M796.032 512l-392.234667 392.192-90.453333-90.453333L614.954667 512l-301.653334-301.696 90.453334-90.496L796.032 512z"></path>
    </svg>
  ),
  'odec-plus': (
    <svg viewBox="0 0 24 24" width="24px" height="24px">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <path d="M12.0316607,3.99999998 C12.5444961,4.00067304 12.9666611,4.3872669 13.0237743,4.88468087 L13.0303491,5.00131071 L13.022,10.9999991 L19,11 C19.5522847,11 20,11.4477153 20,12 C20,12.5128358 19.6139598,12.9355072 19.1166211,12.9932723 L19,13 L13.02,12.9999991 L13.0120009,19.0013107 C13.0112753,19.553595 12.5629736,20.000723 12.0106893,20 C11.4978539,19.999327 11.0756889,19.6127331 11.0185757,19.1153191 L11.0120009,18.9986893 L11.02,12.9999991 L5,13 C4.44771525,13 4,12.5522847 4,12 C4,11.4871642 4.38604019,11.0644928 4.88337887,11.0067277 L5,11 L11.022,10.9999991 L11.0303509,4.99868929 C11.0310747,4.44640501 11.4793764,3.99927697 12.0316607,3.99999998 Z"></path>
      </g>
    </svg>
  ),
  'm-wallet-fill': (
    <svg viewBox="0 0 24 24" width="24px" height="24px">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g>
          <path d="M15.0525589,2.73205081 L16.361,4.99971686 L7.125,4.99971686 L12.3205081,2 C13.2770933,1.44771525 14.5002741,1.77546556 15.0525589,2.73205081 Z M21,6 C22.1045695,6 23,6.8954305 23,8 L23,9 L13,9 C10.8578046,9 9.10892112,10.6839685 9.00489531,12.8003597 L9,13 L9,15 C9,17.209139 10.790861,19 13,19 L23,19 L23,20 C23,21.1045695 22.1045695,22 21,22 L3,22 C1.8954305,22 1,21.1045695 1,20 L1,8 C1,6.8954305 1.8954305,6 3,6 L21,6 Z M23,11 L23,17 L13,17 C11.8954305,17 11,16.1045695 11,15 L11,13 C11,11.8954305 11.8954305,11 13,11 L23,11 Z M14,12 C13.4477153,12 13,12.4477153 13,13 C13,13.5522847 13.4477153,14 14,14 C14.5522847,14 15,13.5522847 15,13 C15,12.4477153 14.5522847,12 14,12 Z"></path>
        </g>
      </g>
    </svg>
  ),
  'm-talk-fill': (
    <svg viewBox="0 0 24 24" width="24px" height="24px">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g>
          <path d="M12,1 C17.5222222,1 22,5.30209999 22,10.6096 C22,14.2594 19.8822222,17.4351 16.7622222,19.062 C15.2111111,19.8518 12.5977778,21.1641 8.92555555,23 L8.92555555,19.755 C4.90555555,18.5109 2,14.8864 2,10.6107 C2,5.30100001 6.47777779,1 12,1 Z M6.84444445,11.2223 C6.41068676,11.6518498 6.41068676,12.3481502 6.84444445,12.7777 C9.69179164,15.5965315 14.3082084,15.5965315 17.1555555,12.7777 C17.576635,12.3460831 17.5706126,11.6600073 17.1420198,11.2357004 C16.713427,10.8113936 16.0204211,10.8054314 15.5844445,11.2223 C14.6338272,12.1635154 13.3444503,12.6922935 12,12.6922935 C10.6555497,12.6922935 9.36617277,12.1635154 8.41555555,11.2223 C7.98166682,10.7928799 7.27833318,10.7928799 6.84444445,11.2223 L6.84444445,11.2223 Z"></path>
        </g>
      </g>
    </svg>
  ),
  'm-shop-fill': (
    <svg viewBox="0 0 24 24" width="24px" height="24px">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <rect fillRule="nonzero" opacity="0" x="0" y="0" width="24" height="24"></rect>
        <g transform="translate(1.000000, 1.000000)" fillRule="nonzero">
          <path d="M18.1320808,0 C19.5177201,0 20.7108233,0.978067527 20.9825249,2.33683702 L21.9413291,7.13072253 C22.3832802,9.34037143 20.2517213,11.1909266 18.1259762,10.4429721 L16.9368458,10.0245672 C16.254685,9.78455118 15.5075056,9.80741907 14.8413329,10.0887135 L12.8844808,10.9148646 C11.6796529,11.4235783 10.320273,11.4235783 9.1154451,10.9148646 L6.46843806,9.7972448 C6.24636756,9.70344768 5.99732071,9.69588965 5.76993052,9.77583037 L3.87397872,10.4429721 C1.74828212,11.1909266 -0.383269051,9.34037143 0.0586675115,7.13072253 L1.01743977,2.33683702 C1.28919948,0.978067527 2.48224458,0 3.8679323,0 Z M15.8449039,3.87591403 L6.15511886,3.87591403 C5.61997141,3.87591403 5.18614035,4.30974509 5.18614035,4.84489254 C5.18614035,5.38003999 5.61996172,5.81387105 6.15511886,5.81387105 L15.8449039,5.81387105 C16.3800708,5.81387105 16.8138824,5.38003999 16.8138824,4.84489254 C16.8138824,4.30974509 16.3800708,3.87591403 15.8449039,3.87591403 Z"></path>
          <path d="M1,12.5759425 L1,19.9766642 C1,21.0941526 1.8506585,22 2.9,22 L19.1,22 C20.14937,22 21,21.0941526 21,19.9766642 L21,12.5759425 C20.037935,12.9382208 17.946385,12.975349 16.855785,12.5666352 L15.689945,12.129797 C15.46698,12.0463344 15.22283,12.0542254 15.005185,12.1521549 L13.08666,13.0147029 C11.4329,13.7582788 9.567005,13.7582788 7.913302,13.0147029 L5.6564725,12 L4.144196,12.5666352 C3.053577,12.975349 1.962027,12.9382208 1,12.5759425 Z"></path>
        </g>
      </g>
    </svg>
  ),
  feedback: (
    <svg viewBox="0 0 1024 1024" width="24px" height="24px">
      <path d="M744.727273 0a139.636364 139.636364 0 0 1 139.636363 139.636364v168.773818l116.410182 67.258182a46.545455 46.545455 0 0 1 23.272727 42.589091L1024 418.909091v465.454545a139.636364 139.636364 0 0 1-139.636364 139.636364H139.636364a139.636364 139.636364 0 0 1-139.636364-139.636364V418.909091c0-12.148364 4.654545-23.272727 12.288-31.511273 2.513455-3.258182 5.538909-6.190545 8.936727-8.750545l4.561455-2.978909L139.636364 309.853091V139.636364a139.636364 139.636364 0 0 1 139.636363-139.636364h465.454546zM93.090909 544.535273V884.363636a46.545455 46.545455 0 0 0 46.545455 46.545455h744.727272a46.545455 46.545455 0 0 0 46.545455-46.545455v-338.338909l-388.096 224.349091h-0.046546l-4.840727 2.466909a46.126545 46.126545 0 0 1-24.669091 3.351273 46.173091 46.173091 0 0 1-24.622545-3.351273l-4.840727-2.420363L93.090909 544.488727zM744.727273 93.090909H279.272727a46.545455 46.545455 0 0 0-46.545454 46.545455v372.363636c0 2.001455-0.139636 3.956364-0.372364 5.911273l280.901818 162.304 278.574546-161.000728A46.917818 46.917818 0 0 1 791.272727 512V139.636364a46.545455 46.545455 0 0 0-46.545454-46.545455z m139.636363 322.746182v49.896727l43.194182-24.948363L884.363636 415.837091zM505.297455 372.363636a46.545455 46.545455 0 0 1 0 93.090909H323.025455a46.545455 46.545455 0 0 1 0-93.090909h182.318545zM139.636364 417.28l-40.634182 23.505455 40.634182 23.505454v-47.010909zM662.714182 186.181818a46.545455 46.545455 0 1 1 0 93.090909H322.978909a46.545455 46.545455 0 1 1 0-93.090909h339.735273z"></path>
    </svg>
  ),
  'm-release application-fill': (
    <svg viewBox="0 0 24 24" width="24px" height="24px">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g transform="translate(-238.000000, -1032.000000)" fillRule="nonzero">
          <g transform="translate(238.000000, 1032.000000)">
            <path d="M10.9454,1.41463027 C11.5468,0.86178991 12.4532,0.86178991 13.0546,1.41463027 C14.7103,2.93652858 16.8298,3.75843681 19,3.76950147 C20.0344,3.77477529 21,4.56219691 21,5.63187094 L21,10.994501 C21,15.8983198 18.2072,20.3422827 13.8737,22.3339226 L12.8123,22.8216986 C12.2951,23.0594338 11.7049,23.0594338 11.1877,22.8216986 L10.1263,22.3339226 C5.7928,20.3422827 3,15.8983198 3,10.994501 L3,5.63187094 C3,4.56219691 3.96559,3.77477529 5,3.76950147 C7.17023,3.75843681 9.28968,2.93652858 10.9454,1.41463027 Z M12.0839615,7.84955555 C11.7095094,8.21969432 11.7060127,8.8233045 12.0761515,9.19775662 C12.0787397,9.20037502 12.0813431,9.20297841 12.0839615,9.20556664 L12.6850366,9.7987685 L8.30304923,9.7990987 C7.75712631,9.7990987 7.31456834,10.2416567 7.31456834,10.7875796 C7.31456834,11.3335025 7.75712631,11.7760605 8.30304923,11.7760605 L12.6840366,11.7757685 L12.0839615,12.3695925 C11.7095094,12.7397313 11.7060127,13.3433415 12.0761515,13.7177936 C12.4640559,14.1013197 13.0756803,14.1013197 13.4557747,13.7256036 L15.0562234,12.1435907 L16.4280366,10.7875796 L15.0562234,9.43156849 L13.4557747,7.84955555 C13.0756803,7.47383948 12.4640559,7.47383948 12.0839615,7.84955555 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  ),
  'pc-self-support-fill': (
    <svg viewBox="0 0 24 24">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.468 8.00004H22C22.5523 8.00004 23 8.44775 23 9.00004V22C23 22.5523 22.5523 23 22 23H2C1.44772 23 1 22.5523 1 22V9.00004C1 8.44775 1.44772 8.00004 2 8.00004H6.53128L11.9944 1.43799L17.468 8.00004ZM14.8636 8.00004H9.13368L11.9959 4.56209L14.8636 8.00004ZM5 12C4.44772 12 4 12.4478 4 13C4 13.5523 4.44772 14 5 14H17C17.5523 14 18 13.5523 18 13C18 12.4478 17.5523 12 17 12H5ZM4 17C4 16.4478 4.44772 16 5 16H11C11.5523 16 12 16.4478 12 17C12 17.5523 11.5523 18 11 18H5C4.44772 18 4 17.5523 4 17Z"
      ></path>
    </svg>
  ),
  'pc-arrow-down-1': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <path
          d="M5.38710056,11.2097046 C5.77939176,10.9046797 6.34662282,10.9324093 6.70710678,11.2928932 L6.70710678,11.2928932 L11.2928407,15.8779458 C11.683458,16.2682949 12.316542,16.2682949 12.7071593,15.8779458 L12.7071593,15.8779458 L17.2928932,11.2928932 L17.3871006,11.2097046 C17.7793918,10.9046797 18.3466228,10.9324093 18.7071068,11.2928932 C19.0976311,11.6834175 19.0976311,12.3165825 18.7071068,12.7071068 L18.7071068,12.7071068 L12.7071068,18.7071068 L12.6128994,18.7902954 C12.2206082,19.0953203 11.6533772,19.0675907 11.2928932,18.7071068 L11.2928932,18.7071068 L5.29289322,12.7071068 L5.20970461,12.6128994 C4.90467972,12.2206082 4.93240926,11.6533772 5.29289322,11.2928932 L5.29289322,11.2928932 Z M5.38710056,5.2097046 C5.77939176,4.9046797 6.34662282,4.9324093 6.70710678,5.2928932 L6.70710678,5.2928932 L11.2928407,9.87794575 C11.683458,10.2682949 12.316542,10.2682949 12.7071593,9.87794575 L12.7071593,9.87794575 L17.2928932,5.2928932 L17.3871006,5.2097046 C17.7793918,4.9046797 18.3466228,4.9324093 18.7071068,5.2928932 C19.0976311,5.6834175 19.0976311,6.3165825 18.7071068,6.7071068 L18.7071068,6.7071068 L12.7071068,12.7071068 L12.6128994,12.7902954 C12.2206082,13.0953203 11.6533772,13.0675907 11.2928932,12.7071068 L11.2928932,12.7071068 L5.29289322,6.7071068 L5.20970461,6.6128994 C4.90467972,6.2206082 4.93240926,5.6533772 5.29289322,5.2928932 L5.29289322,5.2928932 Z"
          fillRule="nonzero"
        ></path>
      </g>
    </svg>
  ),
  'm-home-unselected': (
    <svg viewBox="0 0 60 60">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g transform="translate(-111.000000, -28.000000)">
          <g transform="translate(111.000000, 28.000000)">
            <path
              d="M33.6057666,13.7111027 L48.6057666,24.9892982 C50.1132104,26.1227146 51,27.8989616 51,29.7849678 L51,45 C51,48.3137085 48.3137085,51 45,51 L15,51 C11.6862915,51 9,48.3137085 9,45 L9,29.7849678 C9,27.8989616 9.88678961,26.1227146 11.3942334,24.9892982 L26.3942334,13.7111027 C28.5299126,12.1053289 31.4700874,12.1053289 33.6057666,13.7111027 Z M31.8028833,16.1089375 C30.7912458,15.3483078 29.4185881,15.3082747 28.3689484,15.9888381 L28.1971167,16.1089375 L13.1971167,27.387133 C12.4972321,27.913362 12.0649254,28.7167298 12.0067382,29.5838329 L12,29.7849678 L12,45 C12,46.5976809 13.24892,47.9036609 14.8237272,47.9949073 L15,48 L45,48 C46.5976809,48 47.9036609,46.75108 47.9949073,45.1762728 L48,45 L48,29.7849678 C48,28.9093221 47.6176851,28.080996 46.9595967,27.513393 L46.8028833,27.387133 L31.8028833,16.1089375 Z"
              fillRule="nonzero"
            ></path>
            <rect x="21" y="38" width="18" height="3" rx="1.5"></rect>
          </g>
        </g>
      </g>
    </svg>
  ),
  'pc-workbench-fill': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <path
          d="M21,3 C22.1045695,3 23,3.83573513 23,4.86666667 L23,15.1333333 C23,16.1642649 22.1045695,17 21,17 L13,17 L13,19.0645161 L18,19.0645161 C18.5522847,19.0645161 19,19.497789 19,20.0322581 C19,20.5285508 18.6139598,20.9375876 18.1166211,20.9934893 L18,21 L6,21 C5.44771525,21 5,20.5667272 5,20.0322581 C5,19.5359653 5.38604019,19.1269286 5.88337887,19.0710268 L6,19.0645161 L11,19.0645161 L11,17 L3,17 C1.8954305,17 1,16.1642649 1,15.1333333 L1,4.86666667 C1,3.83573513 1.8954305,3 3,3 L21,3 Z M8,9.87096774 C7.48716416,9.87096774 7.06449284,10.244555 7.00672773,10.7258505 L7,10.8387097 L7,12.7741935 L7.00672773,12.8870527 C7.06449284,13.3683482 7.48716416,13.7419355 8,13.7419355 C8.51283584,13.7419355 8.93550716,13.3683482 8.99327227,12.8870527 L9,12.7741935 L9,10.8387097 L8.99327227,10.7258505 C8.93550716,10.244555 8.51283584,9.87096774 8,9.87096774 Z M12,7.93548387 C11.4871642,7.93548387 11.0644928,8.30907115 11.0067277,8.79036665 L11,8.90322581 L11,12.7741935 L11.0067277,12.8870527 C11.0644928,13.3683482 11.4871642,13.7419355 12,13.7419355 C12.5128358,13.7419355 12.9355072,13.3683482 12.9932723,12.8870527 L13,12.7741935 L13,8.90322581 L12.9932723,8.79036665 C12.9355072,8.30907115 12.5128358,7.93548387 12,7.93548387 Z M16,6 C15.4871642,6 15.0644928,6.37358728 15.0067277,6.85488278 L15,6.96774194 L15,12.7741935 L15.0067277,12.8870527 C15.0644928,13.3683482 15.4871642,13.7419355 16,13.7419355 C16.5128358,13.7419355 16.9355072,13.3683482 16.9932723,12.8870527 L17,12.7741935 L17,6.96774194 L16.9932723,6.85488278 C16.9355072,6.37358728 16.5128358,6 16,6 Z"
          fillRule="nonzero"
        ></path>
      </g>
    </svg>
  ),
  // 'odec-personnel': (
  //   <svg viewBox="0 0 24 24">
  //     <g stroke="none" strokeWidth="1" fillRule="evenodd">
  //       <path d="M14.0094537,13.001596 L14.555687,13.0076478 L15.0432779,13.019651 L15.4783403,13.0388506 C15.546813,13.0427195 15.6133517,13.04694 15.6780838,13.0515383 L15.6780838,13.0515383 L16.0458167,13.0838673 C16.8000159,13.1646324 17.2919549,13.3179925 17.8014673,13.6009384 C18.7407725,14.122598 19.4984557,14.9484379 19.9699621,15.9570483 L19.9699621,15.9570483 L20.0590421,16.1567816 C20.0730017,16.1897971 20.0865267,16.222743 20.0996303,16.2557199 L20.0996303,16.2557199 L20.1733004,16.454759 C20.1847712,16.4882627 20.1958471,16.5219987 20.2065412,16.5560676 L20.2065412,16.5560676 L20.2662317,16.765282 L20.3173969,16.9873187 C20.3252493,17.0256617 20.332773,17.0647402 20.3399812,17.104655 L20.3399812,17.104655 L20.3795504,17.3549828 L20.4121834,17.6302102 L20.4385159,17.9351679 L20.4591835,18.2746869 L20.4748218,18.6535981 L20.4902399,19.3063929 L20.4991274,20.3597472 L20.5,21.6296783 C20.5,22.181963 20.0522847,22.6296783 19.5,22.6296783 L19.5,22.6296783 L4.5,22.6296783 C3.94761611,22.6296783 3.5,22.1818083 3.5,21.6294244 L3.5,21.6294244 L3.50032886,20.3377345 L3.50317252,19.7356652 L3.51576199,18.9484288 L3.53111806,18.4922362 L3.55317563,18.0840584 L3.58290413,17.7179891 C3.58855198,17.6601585 3.59455985,17.6038362 3.60094793,17.5488994 L3.60094793,17.5488994 L3.64400054,17.2349186 C3.6679697,17.0852561 3.6957243,16.9458471 3.72780967,16.8133695 L3.72780967,16.8133695 L3.79791634,16.5566715 C3.86141982,16.3488174 3.93796304,16.1540644 4.03007058,15.9570319 C4.50155845,14.9484348 5.25922166,14.1225957 6.19855417,13.6009209 C6.70805476,13.3179849 7.19998921,13.1646291 7.95418624,13.0838659 L7.95418624,13.0838659 L8.32191832,13.0515375 C8.38665035,13.0469394 8.45318897,13.0427189 8.52166154,13.0388501 L8.52166154,13.0388501 L8.95672337,13.0196508 L9.44431381,13.0076477 L9.99054656,13.001596 L14.0094537,13.001596 Z M13.9717164,15.0014216 L10.0301256,15.0014216 C9.94556115,15.0019108 9.86362115,15.0025552 9.78419707,15.0033671 L9.78419707,15.0033671 L9.13437968,15.0155196 L8.7679627,15.0296077 L8.44890332,15.0491598 L8.17199174,15.0747547 C8.0432046,15.0891592 7.92826922,15.1060466 7.82425508,15.1257425 L7.82425508,15.1257425 L7.62992016,15.1689751 C7.44841029,15.2161451 7.3027707,15.2754164 7.16955732,15.3493925 C6.60357759,15.6637193 6.13738136,16.1718647 5.84187775,16.8039999 L5.84187775,16.8039999 L5.80795293,16.8800167 L5.74662415,17.0402976 L5.69355033,17.2158897 C5.6853633,17.2467187 5.67749825,17.2784014 5.66994419,17.3110457 L5.66994419,17.3110457 L5.6282638,17.5193136 L5.59351971,17.7558379 C5.58827775,17.797901 5.58330283,17.8413572 5.57858395,17.8863144 L5.57858395,17.8863144 L5.55325531,18.1749323 L5.53354432,18.5047517 L5.51892351,18.8809507 L5.50886541,19.3087073 L5.50284257,19.7931997 L5.5,20.629 L18.499,20.629 L18.494154,19.6509065 L18.4832307,19.0134987 L18.4650339,18.4856043 L18.4481704,18.1867268 L18.4270373,17.9248387 L18.4012195,17.6952667 L18.3703024,17.4933377 L18.3338709,17.3143782 L18.2915104,17.1537151 C18.2839332,17.128203 18.2760916,17.1032585 18.2679771,17.0787843 L18.2679771,17.0787843 L18.2159451,16.9368033 L18.1581498,16.8040054 C17.8626443,16.1718825 17.3964275,15.6637276 16.8304598,15.3494076 C16.6972406,15.2754274 16.5515969,15.2161531 16.3701271,15.1689809 L16.3701271,15.1689809 L16.1758486,15.1257465 C16.1411896,15.1191809 16.1053188,15.1129275 16.0681277,15.106974 L16.0681277,15.106974 L15.8282725,15.0747568 C15.7853674,15.0699552 15.7409254,15.0654294 15.6948381,15.0611675 L15.6948381,15.0611675 L15.3977055,15.0386659 L15.0558874,15.0219179 L14.6641836,15.0103451 L13.9717164,15.0014216 Z M12,1.32718838 C14.8568672,1.32718838 17.1728116,3.64313513 17.1728116,6.5 C17.1728116,9.356869 14.856869,11.6728116 12,11.6728116 C9.143131,11.6728116 6.82718838,9.356869 6.82718838,6.5 C6.82718838,3.64313513 9.14313284,1.32718838 12,1.32718838 Z M12,3.32718838 C10.2477028,3.32718838 8.82718838,4.74770419 8.82718838,6.5 C8.82718838,8.2522995 10.2477005,9.67281162 12,9.67281162 C13.7522995,9.67281162 15.1728116,8.2522995 15.1728116,6.5 C15.1728116,4.74770419 13.7522972,3.32718838 12,3.32718838 Z"></path>
  //     </g>
  //   </svg>
  // ),
  'm-customer-fill': (
    <svg viewBox="0 0 44 44">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <path
          d="M22.5,5 C29.4660233,5 35.2883141,10.3001678 35.9414997,17.2354999 C37.770144,17.8538601 39.000698,19.569635 39,21.5 L39,24.5 C39,26.4994999 37.6949997,28.1944999 35.8919997,28.7809999 C35.2139998,32.7769999 31.3934998,35.8534998 26.6489998,36.4099999 C26.1277638,37.3888883 25.1090126,38.0003698 24,38 L22.5,38 C20.8431456,38 19.5,36.6568541 19.5,35 C19.5,33.3431456 20.8431456,32 22.5,32 L24,32 C25.0679999,32 26.0069998,32.5594999 26.5379998,33.3994999 C29.7344998,32.9509999 32.2109998,31.0354999 32.8424998,28.6834999 C31.1262697,28.0044504 29.9992132,26.3456849 30,24.5 L30,21.5 C30,19.5679999 31.2179998,17.9194999 32.9279998,17.2819999 C32.3025862,11.9963802 27.8217416,8.01446733 22.4992499,8.01446733 C17.1767581,8.01446733 12.6959136,11.9963802 12.0704999,17.2819999 C13.832179,17.9380783 15.0003958,19.6201192 15,21.5 L15,24.5 C15,26.9852813 12.9852813,29 10.5,29 C8.01471861,29 6,26.9852813 6,24.5 L6,21.5 C6,19.5199999 7.2795,17.8369999 9.05849996,17.2354999 C9.71168559,10.3001678 15.5339764,5 22.5,5 Z"
          fillRule="nonzero"
        ></path>
      </g>
    </svg>
  ),
  'pc-wait-fill': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g transform="translate(-634.000000, -1107.000000)" fillRule="nonzero">
          <g transform="translate(634.000000, 1107.000000)">
            <path d="M6.79300001,1.759 C6.79355184,3.01521916 7.81178086,4.03344817 9.06800002,4.03399999 L15.138,4.03399999 C16.3946097,4.03400012 17.4134479,3.01560955 17.414,1.759 L19.256,1.759 C20.556,1.759 21.206,2.467 21.206,3.883 L21.2067418,14.0113156 C20.1792181,13.1868071 18.8744907,12.6935538 17.4545579,12.6935538 C14.1409247,12.6935538 11.4546943,15.3797842 11.4546943,18.6934173 C11.4546943,20.3838723 12.1537981,21.9110428 13.2786565,23.0015799 L4.95,23 C3.64999999,23 3,22.292 3,20.876 L3,3.883 C3,2.467 3.64999999,1.759 4.95,1.759 L6.79300001,1.759 Z M17.4545579,14.9091157 C19.5632335,14.9091157 21.2726528,16.6185351 21.2726528,18.7272107 C21.2726528,20.8358863 19.5632335,22.5453057 17.4545579,22.5453057 C15.3458822,22.5453057 13.6364629,20.8358863 13.6364629,18.7272107 C13.6364629,16.6185351 15.3458822,14.9091157 17.4545579,14.9091157 Z M16.9091157,16 C16.306637,16 15.8182314,16.4884055 15.8182314,17.0908843 L15.8182314,19.2726528 C15.8182314,19.8751316 16.306637,20.3635371 16.9091157,20.3635371 L19.0908843,20.3635371 C19.693363,20.3635371 20.1817686,19.8751316 20.1817686,19.2726528 C20.1817686,18.6701741 19.693363,18.1817686 19.0908843,18.1817686 L17.9992314,18.181 L18,17.0908843 C18,16.4884055 17.5115945,16 16.9091157,16 Z M14.793,9.345 L6.31,9.345 C5.91346017,9.34850388 5.58645309,9.65668159 5.559466,10.0523176 C5.53247892,10.4479535 5.81460899,10.7976816 6.20700002,10.855 L6.31,10.862 L14.793,10.862 C15.2011955,10.8471761 15.5244545,10.5119646 15.5244545,10.1035 C15.5244545,9.69503543 15.2011955,9.35982394 14.793,9.345 Z M17.896,6.31 L6.31,6.31 C5.91346023,6.31350397 5.58645323,6.62168166 5.55946615,7.01731756 C5.53247907,7.41295347 5.81460905,7.76268155 6.20700002,7.81999999 L6.31,7.828 L17.896,7.828 C18.3151842,7.828 18.655,7.48818413 18.655,7.069 C18.655,6.64981588 18.3151842,6.31 17.896,6.31 Z M15.138,1 C15.5461955,1.01482394 15.8694545,1.35003543 15.8694545,1.7585 C15.8694545,2.16696456 15.5461955,2.50217605 15.138,2.51699999 L9.06800002,2.51699999 C8.65980454,2.50217605 8.33654554,2.16696456 8.33654554,1.7585 C8.33654554,1.35003543 8.65980454,1.01482394 9.06800002,1 L15.138,1 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  ),
  'm-myprocess-fill': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g>
          <path d="M20,1 C21.1045695,1 22,1.8954305 22,3 L22.0013939,16.1689317 C21.3612062,14.8832984 20.0337332,14 18.4999742,14 C16.3412626,14 14.5909091,15.7499785 14.5909091,17.9089643 L14.5959954,18.1101305 C14.6095043,18.3766463 14.6497035,18.6361203 14.7139734,18.8859325 L14.747,19 L14.628775,19.1077055 C13.6379119,20.0579718 13,21.4046242 13,22.6818182 L13,22.796623 L13.0131369,22.9106737 L13.0215403,22.9836299 L13.023,23 L4,23 C2.8954305,23 2,22.1045695 2,21 L2,3 C2,1.8954305 2.8954305,1 4,1 L20,1 Z M17.0455312,19.9123396 C17.4349902,20.2505804 17.9436575,20.4545455 18.49997,20.4545455 C19.056305,20.4545455 19.5649868,20.2505783 19.9553298,19.9133418 C20.8160049,20.1837135 21.5158403,20.9990578 21.8264806,21.8184954 C21.5157778,22.5078016 20.8273639,22.9899256 20.0252355,22.9998441 L15.3181818,23 C15.1675587,23 15.0413804,22.8953393 15.0084034,22.7547744 L15,22.6818182 C15,21.6585044 15.8733719,20.2812838 17.0455312,19.9123396 Z M18.4999742,16 C19.5543758,16 20.4090909,16.8547342 20.4090909,17.9089643 C20.4090909,18.963404 19.5543844,19.8181818 18.4999742,19.8181818 C17.445607,19.8181818 16.5909091,18.9634127 16.5909091,17.9089643 C16.5909091,16.8544898 17.4458903,16 18.4999742,16 Z M10,14 L6,14 C5.44771525,14 5,14.4477153 5,15 C5,15.5522847 5.44771525,16 6,16 L10,16 C10.5522847,16 11,15.5522847 11,15 C11,14.4477153 10.5522847,14 10,14 Z M18,9 L6,9 C5.44771525,9 5,9.44771525 5,10 C5,10.5522847 5.44771525,11 6,11 L18,11 C18.5522847,11 19,10.5522847 19,10 C19,9.44771525 18.5522847,9 18,9 Z M18,4 L6,4 C5.44771525,4 5,4.44771525 5,5 C5,5.55228475 5.44771525,6 6,6 L18,6 C18.5522847,6 19,5.55228475 19,5 C19,4.44771525 18.5522847,4 18,4 Z"></path>
        </g>
      </g>
    </svg>
  ),
  'pc-complete-fill': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g transform="translate(-674.000000, -1107.000000)" fillRule="nonzero">
          <g transform="translate(674.000000, 1107.000000)">
            <path d="M6.79300001,1.759 C6.79355184,3.01521916 7.81178086,4.03344817 9.06800002,4.03399999 L15.138,4.03399999 C16.3946097,4.03400012 17.4134479,3.01560955 17.414,1.759 L19.256,1.759 C20.556,1.759 21.206,2.467 21.206,3.883 L21.2067418,14.0113156 C20.1792181,13.1868071 18.8744907,12.6935538 17.4545579,12.6935538 C14.1409247,12.6935538 11.4546943,15.3797842 11.4546943,18.6934173 C11.4546943,20.3838723 12.1537981,21.9110428 13.2786565,23.0015799 L4.95,23 C3.64999999,23 3,22.292 3,20.876 L3,3.883 C3,2.467 3.64999999,1.759 4.95,1.759 L6.79300001,1.759 Z M17.4545579,14.9091157 C19.5632335,14.9091157 21.2726528,16.6185351 21.2726528,18.7272107 C21.2726528,20.8358863 19.5632335,22.5453057 17.4545579,22.5453057 C15.3458822,22.5453057 13.6364629,20.8358863 13.6364629,18.7272107 C13.6364629,16.6185351 15.3458822,14.9091157 17.4545579,14.9091157 Z M18.3520306,17.1199929 L17.2540215,19.0218005 L16.6200857,18.6557974 C16.2699726,18.4536596 15.8222847,18.5736172 15.6201468,18.9237303 C15.4180089,19.2738434 15.5379665,19.7215313 15.8880796,19.9236692 L17.1559513,20.6556752 L17.232344,20.694094 C17.568767,20.8395021 17.9681907,20.7128474 18.1558902,20.3877424 L19.6199023,17.851999 C19.8220402,17.5018859 19.7020826,17.054198 19.3519695,16.8520601 C19.0018564,16.6499222 18.5541685,16.7698798 18.3520306,17.1199929 Z M14.793,9.345 L6.31,9.345 C5.91346017,9.34850388 5.58645309,9.65668159 5.559466,10.0523176 C5.53247892,10.4479535 5.81460899,10.7976816 6.20700002,10.855 L6.31,10.862 L14.793,10.862 C15.2011955,10.8471761 15.5244545,10.5119646 15.5244545,10.1035 C15.5244545,9.69503543 15.2011955,9.35982394 14.793,9.345 Z M17.896,6.31 L6.31,6.31 C5.91346023,6.31350397 5.58645323,6.62168166 5.55946615,7.01731756 C5.53247907,7.41295347 5.81460905,7.76268155 6.20700002,7.81999999 L6.31,7.828 L17.896,7.828 C18.3151842,7.828 18.655,7.48818413 18.655,7.069 C18.655,6.64981588 18.3151842,6.31 17.896,6.31 Z M15.138,1 C15.5461955,1.01482394 15.8694545,1.35003543 15.8694545,1.7585 C15.8694545,2.16696456 15.5461955,2.50217605 15.138,2.51699999 L9.06800002,2.51699999 C8.65980454,2.50217605 8.33654554,2.16696456 8.33654554,1.7585 C8.33654554,1.35003543 8.65980454,1.01482394 9.06800002,1 L15.138,1 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  ),
  'm-todo-fill': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g>
          <path d="M17,2 C17.5522847,2 18,2.44771525 18,3 L18,5 L21,5 C22.1045695,5 23,5.8954305 23,7 L23,19 C23,20.1045695 22.1045695,21 21,21 L3,21 C1.8954305,21 1,20.1045695 1,19 L1,7 C1,5.8954305 1.8954305,5 3,5 L6,5 L6,3 C6,2.44771525 6.44771525,2 7,2 C7.55228475,2 8,2.44771525 8,3 L8,5 L16,5 L16,3 C16,2.44771525 16.4477153,2 17,2 Z M19,15 L5,15 C4.44771525,15 4,15.4477153 4,16 C4,16.5522847 4.44771525,17 5,17 L19,17 C19.5522847,17 20,16.5522847 20,16 C20,15.4477153 19.5522847,15 19,15 Z M11,9 L5,9 C4.44771525,9 4,9.44771525 4,10 C4,10.5522847 4.44771525,11 5,11 L11,11 C11.5522847,11 12,10.5522847 12,10 C12,9.44771525 11.5522847,9 11,9 Z"></path>
        </g>
        <g>
          <path d="M17,2 C17.5522847,2 18,2.44771525 18,3 L18,5 L21,5 C22.1045695,5 23,5.8954305 23,7 L23,19 C23,20.1045695 22.1045695,21 21,21 L3,21 C1.8954305,21 1,20.1045695 1,19 L1,7 C1,5.8954305 1.8954305,5 3,5 L6,5 L6,3 C6,2.44771525 6.44771525,2 7,2 C7.55228475,2 8,2.44771525 8,3 L8,5 L16,5 L16,3 C16,2.44771525 16.4477153,2 17,2 Z M19,15 L5,15 C4.44771525,15 4,15.4477153 4,16 C4,16.5522847 4.44771525,17 5,17 L19,17 C19.5522847,17 20,16.5522847 20,16 C20,15.4477153 19.5522847,15 19,15 Z M11,9 L5,9 C4.44771525,9 4,9.44771525 4,10 C4,10.5522847 4.44771525,11 5,11 L11,11 C11.5522847,11 12,10.5522847 12,10 C12,9.44771525 11.5522847,9 11,9 Z"></path>
        </g>
      </g>
    </svg>
  ),
  'pc-add-fill': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g transform="translate(-713.000000, -1107.000000)" fillRule="nonzero">
          <g transform="translate(713.000000, 1107.000000)">
            <path d="M6.79300001,1.759 C6.79355184,3.01521916 7.81178086,4.03344817 9.06800002,4.03399999 L15.138,4.03399999 C16.3946097,4.03400012 17.4134479,3.01560955 17.414,1.759 L19.256,1.759 C20.556,1.759 21.206,2.467 21.206,3.883 L21.2067418,14.0113156 C20.1792181,13.1868071 18.8744907,12.6935538 17.4545579,12.6935538 C14.1409247,12.6935538 11.4546943,15.3797842 11.4546943,18.6934173 C11.4546943,20.3838723 12.1537981,21.9110428 13.2786565,23.0015799 L4.95,23 C3.64999999,23 3,22.292 3,20.876 L3,3.883 C3,2.467 3.64999999,1.759 4.95,1.759 L6.79300001,1.759 Z M17.4545579,14.9091157 C19.5632335,14.9091157 21.2726528,16.6185351 21.2726528,18.7272107 C21.2726528,20.8358863 19.5632335,22.5453057 17.4545579,22.5453057 C15.3458822,22.5453057 13.6364629,20.8358863 13.6364629,18.7272107 C13.6364629,16.6185351 15.3458822,14.9091157 17.4545579,14.9091157 Z M17.4859149,16.2061713 C17.1791129,16.2061713 16.9303875,16.4548586 16.9303405,16.7616607 L16.9303405,18.1505968 L15.5414043,18.1505968 C15.2346023,18.1506438 14.9859149,18.3993692 14.9859149,18.7061713 C14.9859149,19.0129734 15.2346023,19.2616988 15.5414043,19.2617458 L16.9303405,19.2617458 L16.9303405,20.6506819 C16.9303875,20.957484 17.1791129,21.2061713 17.4859149,21.2061713 C17.792717,21.2061713 18.0414424,20.957484 18.0414894,20.6506819 L18.0414894,19.2617458 L19.4304255,19.2617458 C19.7372276,19.2616988 19.9859149,19.0129734 19.9859149,18.7061713 C19.9859149,18.3993692 19.7372276,18.1506438 19.4304255,18.1505968 L18.0414894,18.1505968 L18.0414894,16.7616607 C18.0414424,16.4548586 17.792717,16.2061713 17.4859149,16.2061713 Z M14.793,9.345 L6.31,9.345 C5.91346017,9.34850388 5.58645309,9.65668159 5.559466,10.0523176 C5.53247892,10.4479535 5.81460899,10.7976816 6.20700002,10.855 L6.31,10.862 L14.793,10.862 C15.2011955,10.8471761 15.5244545,10.5119646 15.5244545,10.1035 C15.5244545,9.69503543 15.2011955,9.35982394 14.793,9.345 Z M17.896,6.31 L6.31,6.31 C5.91346023,6.31350397 5.58645323,6.62168166 5.55946615,7.01731756 C5.53247907,7.41295347 5.81460905,7.76268155 6.20700002,7.81999999 L6.31,7.828 L17.896,7.828 C18.3151842,7.828 18.655,7.48818413 18.655,7.069 C18.655,6.64981588 18.3151842,6.31 17.896,6.31 Z M15.138,1 C15.5461955,1.01482394 15.8694545,1.35003543 15.8694545,1.7585 C15.8694545,2.16696456 15.5461955,2.50217605 15.138,2.51699999 L9.06800002,2.51699999 C8.65980454,2.50217605 8.33654554,2.16696456 8.33654554,1.7585 C8.33654554,1.35003543 8.65980454,1.01482394 9.06800002,1 L15.138,1 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  ),
  'm-todoprocess-fill': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g>
          <path d="M20,1 C21.1045695,1 22,1.8954305 22,3 L22.0005473,15.2576141 C21.0495273,14.4719987 19.8298843,14 18.5,14 C15.4624339,14 13,16.4624339 13,19.5 C13,20.8298843 13.4719987,22.0495273 14.2576141,23.0005473 L4,23 C2.8954305,23 2,22.1045695 2,21 L2,3 C2,1.8954305 2.8954305,1 4,1 L20,1 Z M18.5,16 C20.4329966,16 22,17.5670034 22,19.5 C22,21.4329966 20.4329966,23 18.5,23 C16.5670034,23 15,21.4329966 15,19.5 C15,17.5670034 16.5670034,16 18.5,16 Z M18,17 C17.4477153,17 17,17.4477153 17,18 L17,20 C17,20.5522847 17.4477153,21 18,21 L20,21 C20.5522847,21 21,20.5522847 21,20 C21,19.4477153 20.5522847,19 20,19 L19,19 L19,18 C19,17.4477153 18.5522847,17 18,17 Z M10,14 L6,14 C5.44771525,14 5,14.4477153 5,15 C5,15.5522847 5.44771525,16 6,16 L10,16 C10.5522847,16 11,15.5522847 11,15 C11,14.4477153 10.5522847,14 10,14 Z M18,9 L6,9 C5.44771525,9 5,9.44771525 5,10 C5,10.5522847 5.44771525,11 6,11 L18,11 C18.5522847,11 19,10.5522847 19,10 C19,9.44771525 18.5522847,9 18,9 Z M18,4 L6,4 C5.44771525,4 5,4.44771525 5,5 C5,5.55228475 5.44771525,6 6,6 L18,6 C18.5522847,6 19,5.55228475 19,5 C19,4.44771525 18.5522847,4 18,4 Z"></path>
        </g>
      </g>
    </svg>
  ),
  'm-todocomplete-fill': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g>
          <path d="M17,2 C17.5522847,2 18,2.44771525 18,3 L18,5 L21,5 C22.1045695,5 23,5.8954305 23,7 L23,20 C23,21.1045695 22.1045695,22 21,22 L3,22 C1.8954305,22 1,21.1045695 1,20 L1,7 C1,5.8954305 1.8954305,5 3,5 L6,5 L6,3 C6,2.44771525 6.44771525,2 7,2 C7.55228475,2 8,2.44771525 8,3 L8,5 L16,5 L16,3 C16,2.44771525 16.4477153,2 17,2 Z M17.3033009,9.73223305 C16.9127766,9.34170876 16.2796116,9.34170876 15.8890873,9.73223305 L10.9388059,14.6813398 L8.1109127,11.8535534 C7.72038841,11.4630291 7.08722343,11.4630291 6.69669914,11.8535534 C6.30617485,12.2440777 6.30617485,12.8772427 6.69669914,13.267767 L10.232233,16.8033009 C10.412475,16.9835428 10.6444038,17.0805962 10.8803084,17.094461 L10.9983712,17.094461 L11.1157713,17.0805962 C11.3101497,17.0459343 11.496245,16.9535025 11.6464466,16.8033009 L17.3033009,11.1464466 C17.6938252,10.7559223 17.6938252,10.1227573 17.3033009,9.73223305 Z"></path>
        </g>
      </g>
    </svg>
  ),
  'm-processed-fill': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g>
          <path d="M20,1 C21.1045695,1 22,1.8954305 22,3 L22.0005473,15.2576141 C21.0495273,14.4719987 19.8298843,14 18.5,14 C15.4624339,14 13,16.4624339 13,19.5 C13,20.8298843 13.4719987,22.0495273 14.2576141,23.0005473 L4,23 C2.8954305,23 2,22.1045695 2,21 L2,3 C2,1.8954305 2.8954305,1 4,1 L20,1 Z M18.5,16 C20.4329966,16 22,17.5670034 22,19.5 C22,21.4329966 20.4329966,23 18.5,23 C16.5670034,23 15,21.4329966 15,19.5 C15,17.5670034 16.5670034,16 18.5,16 Z M19.5229975,17.9029694 L18.1575875,19.3657262 L17.4276471,18.6836785 C17.0237316,18.3070212 16.3909524,18.3291183 16.0142951,18.7330338 C15.6376378,19.1369493 15.6597349,19.7697286 16.0636504,20.1463859 L17.5263578,21.5103826 C17.9302733,21.8870399 18.5630526,21.8649428 18.9397099,21.4610273 C18.943619,21.4568352 18.9474852,21.4526185 18.9513085,21.4483777 L20.9857049,19.2669662 C21.3623622,18.8630507 21.3402651,18.2302714 20.9363496,17.8536141 C20.5324341,17.4769568 19.8996548,17.499054 19.5229975,17.9029694 Z M10,14 L6,14 C5.44771525,14 5,14.4477153 5,15 C5,15.5522847 5.44771525,16 6,16 L10,16 C10.5522847,16 11,15.5522847 11,15 C11,14.4477153 10.5522847,14 10,14 Z M18,9 L6,9 C5.44771525,9 5,9.44771525 5,10 C5,10.5522847 5.44771525,11 6,11 L18,11 C18.5522847,11 19,10.5522847 19,10 C19,9.44771525 18.5522847,9 18,9 Z M18,4 L6,4 C5.44771525,4 5,4.44771525 5,5 C5,5.55228475 5.44771525,6 6,6 L18,6 C18.5522847,6 19,5.55228475 19,5 C19,4.44771525 18.5522847,4 18,4 Z"></path>
        </g>
        <g>
          <path d="M20,1 C21.1045695,1 22,1.8954305 22,3 L22.0005473,15.2576141 C21.0495273,14.4719987 19.8298843,14 18.5,14 C15.4624339,14 13,16.4624339 13,19.5 C13,20.8298843 13.4719987,22.0495273 14.2576141,23.0005473 L4,23 C2.8954305,23 2,22.1045695 2,21 L2,3 C2,1.8954305 2.8954305,1 4,1 L20,1 Z M18.5,16 C20.4329966,16 22,17.5670034 22,19.5 C22,21.4329966 20.4329966,23 18.5,23 C16.5670034,23 15,21.4329966 15,19.5 C15,17.5670034 16.5670034,16 18.5,16 Z M19.5229975,17.9029694 L18.1575875,19.3657262 L17.4276471,18.6836785 C17.0237316,18.3070212 16.3909524,18.3291183 16.0142951,18.7330338 C15.6376378,19.1369493 15.6597349,19.7697286 16.0636504,20.1463859 L17.5263578,21.5103826 C17.9302733,21.8870399 18.5630526,21.8649428 18.9397099,21.4610273 C18.943619,21.4568352 18.9474852,21.4526185 18.9513085,21.4483777 L20.9857049,19.2669662 C21.3623622,18.8630507 21.3402651,18.2302714 20.9363496,17.8536141 C20.5324341,17.4769568 19.8996548,17.499054 19.5229975,17.9029694 Z M10,14 L6,14 C5.44771525,14 5,14.4477153 5,15 C5,15.5522847 5.44771525,16 6,16 L10,16 C10.5522847,16 11,15.5522847 11,15 C11,14.4477153 10.5522847,14 10,14 Z M18,9 L6,9 C5.44771525,9 5,9.44771525 5,10 C5,10.5522847 5.44771525,11 6,11 L18,11 C18.5522847,11 19,10.5522847 19,10 C19,9.44771525 18.5522847,9 18,9 Z M18,4 L6,4 C5.44771525,4 5,4.44771525 5,5 C5,5.55228475 5.44771525,6 6,6 L18,6 C18.5522847,6 19,5.55228475 19,5 C19,4.44771525 18.5522847,4 18,4 Z"></path>
        </g>
      </g>
    </svg>
  ),
  // 'm-home-unselected': (
  //   <svg viewBox="0 0 60 60">
  //     <g stroke="none" strokeWidth="1" fillRule="evenodd">
  //       <g transform="translate(-111.000000, -28.000000)">
  //         <g transform="translate(111.000000, 28.000000)">
  //           <path
  //             d="M33.6057666,13.7111027 L48.6057666,24.9892982 C50.1132104,26.1227146 51,27.8989616 51,29.7849678 L51,45 C51,48.3137085 48.3137085,51 45,51 L15,51 C11.6862915,51 9,48.3137085 9,45 L9,29.7849678 C9,27.8989616 9.88678961,26.1227146 11.3942334,24.9892982 L26.3942334,13.7111027 C28.5299126,12.1053289 31.4700874,12.1053289 33.6057666,13.7111027 Z M31.8028833,16.1089375 C30.7912458,15.3483078 29.4185881,15.3082747 28.3689484,15.9888381 L28.1971167,16.1089375 L13.1971167,27.387133 C12.4972321,27.913362 12.0649254,28.7167298 12.0067382,29.5838329 L12,29.7849678 L12,45 C12,46.5976809 13.24892,47.9036609 14.8237272,47.9949073 L15,48 L45,48 C46.5976809,48 47.9036609,46.75108 47.9949073,45.1762728 L48,45 L48,29.7849678 C48,28.9093221 47.6176851,28.080996 46.9595967,27.513393 L46.8028833,27.387133 L31.8028833,16.1089375 Z"
  //             fillRule="nonzero"
  //           ></path>
  //           <rect x="21" y="38" width="18" height="3" rx="1.5"></rect>
  //         </g>
  //       </g>
  //     </g>
  //   </svg>
  // ),
  'odec-personnel-fill': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <path d="M14.0094537,13.001596 L14.555687,13.0076478 L15.0432779,13.019651 L15.4783403,13.0388506 L15.5796148,13.0449209 L15.5796148,13.0449209 L15.6780838,13.0515383 L16.0458167,13.0838673 C16.8000159,13.1646324 17.2919549,13.3179925 17.8014673,13.6009384 C18.6624971,14.0791264 19.3709139,14.8129103 19.846184,15.7087736 L19.9699621,15.9570483 L20.0590421,16.1567816 L20.0796572,16.2062652 L20.0796572,16.2062652 L20.0996303,16.2557199 L20.1733004,16.454759 L20.1902121,16.5052013 L20.1902121,16.5052013 L20.2065412,16.5560676 L20.2662317,16.765282 L20.3173969,16.9873187 L20.3289307,17.0453974 L20.3289307,17.0453974 L20.3399812,17.104655 L20.3795504,17.3549828 L20.4121834,17.6302102 L20.4385159,17.9351679 L20.4591835,18.2746869 L20.4748218,18.6535981 L20.4902399,19.3063929 L20.4991274,20.3597472 L20.5,21.6296783 C20.5,22.1425141 20.1139598,22.5651854 19.6166211,22.6229505 L19.5,22.6296783 L4.5,22.6296783 C3.9870721,22.6296783 3.56447969,22.2435046 3.50672632,21.7460675 L3.5,21.6294244 L3.50032886,20.3377345 L3.50317252,19.7356652 L3.51576199,18.9484288 L3.53111806,18.4922362 L3.55317563,18.0840584 L3.58290413,17.7179891 L3.59164845,17.6323591 L3.59164845,17.6323591 L3.60094793,17.5488994 L3.64400054,17.2349186 L3.68286153,17.0176997 L3.68286153,17.0176997 L3.72780967,16.8133695 L3.79791634,16.5566715 C3.86141982,16.3488174 3.93796304,16.1540644 4.03007058,15.9570319 C4.50155845,14.9484348 5.25922166,14.1225957 6.19855417,13.6009209 C6.65144358,13.3494222 7.09045358,13.2003079 7.71211681,13.1135537 L7.95418624,13.0838659 L8.32191832,13.0515375 L8.42038723,13.0449203 L8.42038723,13.0449203 L8.52166154,13.0388501 L8.95672337,13.0196508 L9.44431381,13.0076477 L9.99054656,13.001596 L14.0094537,13.001596 Z M12,1.32718838 C14.8568672,1.32718838 17.1728116,3.64313513 17.1728116,6.5 C17.1728116,9.356869 14.856869,11.6728116 12,11.6728116 C9.143131,11.6728116 6.82718838,9.356869 6.82718838,6.5 C6.82718838,3.64313513 9.14313284,1.32718838 12,1.32718838 Z"></path>
      </g>
    </svg>
  ),
  'm-release approval-fill': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g transform="translate(-294.000000, -1032.000000)" fillRule="nonzero">
          <g transform="translate(294.000000, 1032.000000)">
            <path d="M20.3320557,1 C21.4570018,1 22.3689508,1.8954305 22.3689508,3 L22.371229,16.0650169 C21.7174855,14.7456955 20.3571498,13.8386631 18.7849653,13.8386631 C16.5758263,13.8386631 14.7849653,15.6295241 14.7849653,17.8386631 C14.7849653,18.0343179 14.7990127,18.2266917 14.826154,18.414831 L14.2688541,18.4146341 C13.7165693,18.4146341 13.2688541,18.8623494 13.2688541,19.4146341 L13.2691729,22.9784208 C13.2544201,22.9801668 13.2396493,22.9819032 13.2248606,22.9836299 L13.2263472,23 L4.03689508,23 C2.91194899,23 2,22.1045695 2,21 L2,3 C2,1.8954305 2.91194899,1 4.03689508,1 L20.3320557,1 Z M21.813743,22.2195122 C22.0228364,22.2195122 22.1923402,22.3942304 22.1923402,22.6097561 C22.1923402,22.8098871 22.0461864,22.9748321 21.8578954,22.9973746 L21.813743,23 L15.7561877,23 C15.5470942,23 15.3775904,22.8252818 15.3775904,22.6097561 C15.3775904,22.4096251 15.5237442,22.2446801 15.7120352,22.2221376 L15.7561877,22.2195122 L21.813743,22.2195122 Z M18.845503,15 C20.0379583,15 20.8930703,15.7139284 20.8930703,17.1176195 C20.8930703,17.7901726 20.7010702,18.2677348 20.3418758,18.7048878 L20.1057747,18.9750725 L20.0113498,19.087198 L19.9956239,19.1082238 C19.9912832,19.1146676 19.9876566,19.1202428 19.9846321,19.1250443 L19.9706433,19.1499282 L19.9699746,19.300878 L21.6244633,19.3009951 C21.919639,19.3009951 22.1622394,19.5331612 22.1897593,19.8299889 L22.1923591,19.886361 L22.1923591,21.4390244 C22.1923591,21.6545501 22.0228554,21.8292683 21.8137619,21.8292683 L15.7561418,21.8292683 C15.5469631,21.8292683 15.3774239,21.6544123 15.3775444,21.4387983 L15.3784182,19.8857247 C15.3789288,19.5629025 15.6328193,19.3013462 15.9464233,19.3009951 L17.6336512,19.300878 L17.6344084,19.1834146 L17.6142043,19.1568713 L17.5794831,19.1140425 L17.2989111,18.7920494 L17.2555217,18.7391066 C16.9015367,18.2900293 16.7068263,17.7894506 16.7068263,17.1176195 C16.7068263,15.717794 17.6282682,15 18.845503,15 Z M9.76939318,12.2817871 C9.39494106,12.6519258 9.39144439,13.255536 9.76158316,13.6299881 L9.76939318,13.6377981 L10.3704682,14.231 L5.98848089,14.2313302 C5.44255797,14.2313302 5,14.6738882 5,15.2198111 C5,15.765734 5.44255797,16.208292 5.98848089,16.208292 L10.3694682,16.208 L9.76939318,16.801824 C9.39494106,17.1719628 9.39144439,17.775573 9.76158316,18.1500251 C10.1494876,18.5335512 10.7611119,18.5335512 11.1412063,18.1578351 L12.741655,16.5758222 L14.1134682,15.2198111 L12.741655,13.8638 L11.1412063,12.2817871 C10.7611119,11.906071 10.1494876,11.906071 9.76939318,12.2817871 Z M14.3410569,8 L5.76962833,8 C5.37513922,8 5.05534261,8.44771525 5.05534261,9 C5.05534261,9.55228475 5.37513922,10 5.76962833,10 L14.3410569,10 C14.735546,10 15.0553426,9.55228475 15.0553426,9 C15.0553426,8.44771525 14.735546,8 14.3410569,8 Z M18.2951606,4 L6.07379015,4 C5.51131711,4 5.05534261,4.44771525 5.05534261,5 C5.05534261,5.55228475 5.51131711,6 6.07379015,6 L18.2951606,6 C18.8576336,6 19.3136081,5.55228475 19.3136081,5 C19.3136081,4.44771525 18.8576336,4 18.2951606,4 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  ),
  'm-scan passed-fill': (
    <svg viewBox="0 0 24 24">
      <defs>
        <linearGradient x1="5.11915404%" y1="0%" x2="100%" y2="100%">
          <stop stopColor="#197AFA" offset="0%"></stop>
          <stop stopColor="#197AFA" offset="100%"></stop>
        </linearGradient>
      </defs>
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g transform="translate(-348.000000, -1032.000000)">
          <g transform="translate(348.000000, 1032.000000)">
            <path d="M22,14.5 C22.5522847,14.5 23,14.9477153 23,15.5 L23,20 C23,21.6568542 21.6568542,23 20,23 L15.5,23 C14.9477153,23 14.5,22.5522847 14.5,22 C14.5,21.4477153 14.9477153,21 15.5,21 L20,21 C20.5522847,21 21,20.5522847 21,20 L21,15.5 C21,14.9477153 21.4477153,14.5 22,14.5 Z M2,14.5 C2.55228475,14.5 3,14.9477153 3,15.5 L3,20 C3,20.5522847 3.44771525,21 4,21 L8.5,21 C9.05228475,21 9.5,21.4477153 9.5,22 C9.5,22.5522847 9.05228475,23 8.5,23 L4,23 C2.34314575,23 1,21.6568542 1,20 L1,15.5 C1,14.9477153 1.44771525,14.5 2,14.5 Z M15.279896,5 C15.9182066,5 16.5142846,5.31901126 16.8683556,5.85011781 L19.0359801,9.10155442 C19.1056693,9.2060883 19.1428571,9.32891137 19.1428571,9.45454545 L19.1428571,17.0909091 C19.1428571,18.1452709 18.288128,19 17.2337662,19 L7.05194805,19 C5.99758626,19 5.14285714,18.1452709 5.14285714,17.0909091 L5.14285714,9.45454545 C5.14285714,9.32891137 5.18004498,9.2060883 5.24973423,9.10155442 L7.41735864,5.85011781 C7.77142967,5.31901126 8.36750766,5 9.00581829,5 L15.279896,5 Z M12.3286386,11.1402587 C11.9541865,11.5103975 11.9506898,12.1140076 12.3208286,12.4884598 L12.3286386,12.4962698 L12.9297136,13.0894716 L8.54772628,13.0898018 C8.00180336,13.0898018 7.55924539,13.5323598 7.55924539,14.0782827 C7.55924539,14.6242057 8.00180336,15.0667636 8.54772628,15.0667636 L12.9287136,15.0664716 L12.3286386,15.6602957 C11.9541865,16.0304344 11.9506898,16.6340446 12.3208286,17.0084968 C12.708733,17.3920228 13.3203573,17.3920228 13.7004517,17.0163068 L15.3009004,15.4342938 L16.6727136,14.0782827 L15.3009004,12.7222716 L13.7004517,11.1402587 C13.3203573,10.7645426 12.708733,10.7645426 12.3286386,11.1402587 Z M20,1 C21.6568542,1 23,2.34314575 23,4 L23,8.5 C23,9.05228475 22.5522847,9.5 22,9.5 C21.4477153,9.5 21,9.05228475 21,8.5 L21,4 C21,3.44771525 20.5522847,3 20,3 L15.5,3 C14.9477153,3 14.5,2.55228475 14.5,2 C14.5,1.44771525 14.9477153,1 15.5,1 L20,1 Z M8.5,1 C9.05228475,1 9.5,1.44771525 9.5,2 C9.5,2.55228475 9.05228475,3 8.5,3 L4,3 C3.44771525,3 3,3.44771525 3,4 L3,8.5 C3,9.05228475 2.55228475,9.5 2,9.5 C1.44771525,9.5 1,9.05228475 1,8.5 L1,4 C1,2.34314575 2.34314575,1 4,1 L8.5,1 Z M15.0900927,7.01127451 L9.30431457,7.01127451 C9.11515711,7.01127451 8.9385149,7.10581054 8.83358922,7.26319906 L7.53158692,9.33320979 L16.8526654,9.33320979 L15.560818,7.26319906 C15.4558923,7.10581054 15.2792501,7.01127451 15.0900927,7.01127451 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  ),
  'm-face-fill': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g transform="translate(-119.000000, -1079.000000)" fillRule="nonzero">
          <g transform="translate(119.000000, 1079.000000)">
            <path d="M11.9934825,2.05048759 C15.1360424,2.15035848 16.8152701,3.82893527 17.0311655,7.08621795 C17.0311655,9.71100137 16.3043133,11.6543142 14.8506087,12.9161564 C14.631305,13.1059711 14.5055111,13.3817843 14.5056518,13.6718251 L14.5056518,14.5369924 C14.5047791,15.3018783 14.9402341,16.0002769 15.6273124,16.3363941 C16.2758737,16.6531499 17.1478969,16.9655413 18.2433819,17.2735682 C20.2814,18.0210414 21.3626764,19.0395646 21.4872111,20.3291377 L21.4872111,22.001082 C21.4872111,22.5533668 21.0394959,23.001082 20.4872111,23.001082 L3.50550371,23.001082 C2.953219,23.001283 2.50534078,22.5537308 2.50514196,22.0014461 C2.50632109,21.4495905 2.50449579,20.8921544 2.49966607,20.3291377 C2.65411617,18.8614812 4.00006025,18.0408563 6.3394094,17.2735682 C7.18730473,17.0594975 7.97824337,16.7193019 8.71222532,16.2529814 C9.28664481,15.8852266 9.6351023,15.2506719 9.63604205,14.5686168 L9.63604205,13.6526763 C9.63492384,13.3509326 9.49762123,13.0658244 9.26129248,12.8782016 C7.72847348,11.6459779 6.95063956,9.80105841 6.92779072,7.34344316 C7.1656577,3.91596717 8.85422163,2.15164865 11.9934825,2.05048759 Z M2.73444666,10.5962489 C3.18293754,10.5962489 3.4819373,10.8870977 3.4819373,11.323388 L3.4819373,13.171655 C3.4819373,13.3898001 3.63142842,13.5352246 3.85567386,13.5352246 L5.7556263,13.5352246 C6.20411718,13.5352246 6.50309942,13.8260905 6.50309942,14.2623808 C6.50309942,14.6986711 6.20411718,14.98952 5.7556263,14.98952 L3.85567386,14.98952 C2.80920098,14.98952 1.98697354,14.18966 1.98697354,13.171655 L1.98697354,11.323388 C1.98697354,10.8870977 2.28595578,10.5962489 2.73444666,10.5962489 Z M21.1692343,10.5815291 C21.6177252,10.5815291 21.9167074,10.872378 21.9167074,11.3086683 L21.9167074,13.1569352 C21.9167074,14.1749403 21.09448,14.9748002 20.0480071,14.9748002 L18.1480547,14.9748002 C17.6995638,14.9748002 17.4005816,14.6839514 17.4005816,14.247661 C17.4005816,13.8113707 17.6995638,13.5205048 18.1480547,13.5205048 L20.0480071,13.5205048 C20.2722526,13.5205048 20.4217437,13.3750804 20.4217437,13.1569352 L20.4217437,11.3086683 C20.4217437,10.872378 20.7207434,10.5815291 21.1692343,10.5815291 Z M20.1182732,1.00671304 C21.1647461,1.00671304 21.9869735,1.80657295 21.9869735,2.82457802 L21.9869735,4.67284496 C21.9869735,5.10913529 21.6879913,5.39998414 21.2395004,5.39998414 C20.7910095,5.39998414 20.4920098,5.10913529 20.4920098,4.67284496 L20.4920098,2.82457802 C20.4920098,2.60643286 20.3425187,2.46100844 20.1182732,2.46100844 L18.2183208,2.46100844 C17.7698299,2.46100844 17.4708477,2.17014254 17.4708477,1.73385221 C17.4708477,1.29756189 17.7698299,1.00671304 18.2183208,1.00671304 L20.1182732,1.00671304 Z M5.76864428,1.00108225 C6.21713516,1.00108225 6.5161174,1.29193111 6.5161174,1.72822143 C6.5161174,2.16451175 6.21713516,2.45537765 5.76864428,2.45537765 L3.86869184,2.45537765 C3.6444464,2.45537765 3.49495528,2.60080208 3.49495528,2.81894724 L3.49495528,4.66721418 C3.49495528,5.1035045 3.19595552,5.39435336 2.74746464,5.39435336 C2.29897376,5.39435336 1.99999152,5.1035045 1.99999152,4.66721418 L1.99999152,2.81894724 C1.99999152,1.80094217 2.82221896,1.00108225 3.86869184,1.00108225 L5.76864428,1.00108225 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  ),
  'order management': (
    <svg viewBox="0 0 32 38">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <g transform="translate(-593.000000, -819.000000)">
          <g transform="translate(24.000000, 732.000000)">
            <g transform="translate(537.000000, 84.000000)">
              <g transform="translate(26.000000, 0.000000)">
                <path d="M25,3 C27.4188915,3 29.4366008,4.71766536 29.8999437,6.99981203 L34,7 C36.209139,7 38,8.790861 38,11 L38,37 C38,39.209139 36.209139,41 34,41 L10,41 C7.790861,41 6,39.209139 6,37 L6,11 C6,8.790861 7.790861,7 10,7 L14.1000563,6.99981203 C14.5633992,4.71766536 16.5811085,3 19,3 L25,3 Z M14.4206286,10.0104609 L11,10.0102047 C9.9456382,10.0102047 9.08183488,10.8260824 9.00548574,11.8609423 L9,12.0102047 L9,36.0102047 C9,37.0645664 9.81587779,37.9283698 10.8507377,38.0047189 L11,38.0102047 L33,38.0102047 C34.0543618,38.0102047 34.9181651,37.1943269 34.9945143,36.159467 L35,36.0102047 L35,12.0102047 C35,10.9056352 34.1045695,10.0102047 33,10.0102047 L29.5793714,10.0104609 C28.8054356,11.7707766 27.0462681,13 25,13 L19,13 C16.9537319,13 15.1945644,11.7707766 14.4206286,10.0104609 Z M20.5,30 C21.3284271,30 22,30.6715729 22,31.5 C22,32.3284271 21.3284271,33 20.5,33 L15.5,33 C14.6715729,33 14,32.3284271 14,31.5 C14,30.6715729 14.6715729,30 15.5,30 L20.5,30 Z M28.5,24 C29.3284271,24 30,24.6715729 30,25.5 C30,26.3284271 29.3284271,27 28.5,27 L15.5,27 C14.6715729,27 14,26.3284271 14,25.5 C14,24.6715729 14.6715729,24 15.5,24 L28.5,24 Z M28.5,18 C29.3284271,18 30,18.6715729 30,19.5 C30,20.3284271 29.3284271,21 28.5,21 L15.5,21 C14.6715729,21 14,20.3284271 14,19.5 C14,18.6715729 14.6715729,18 15.5,18 L28.5,18 Z M25,6 L19,6 C17.8954305,6 17,6.8954305 17,8 C17,9.0543618 17.8158778,9.91816512 18.8507377,9.99451426 L19,10 L25,10 C26.1045695,10 27,9.1045695 27,8 C27,6.8954305 26.1045695,6 25,6 Z"></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  'pc-location': (
    <svg viewBox="0 0 24 24">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <path
          d="M11.5,1 C16.1944347,1 20,4.80556525 20,9.5 C20,12.4855681 18.6222013,15.5046392 16.3187942,18.4505757 C15.5012827,19.4961299 14.612199,20.4710024 13.701357,21.3589402 C13.626319,21.4320912 13.5525283,21.5033198 13.4800751,21.5725974 L13.4800751,21.5725974 L13.0621269,21.9646242 L12.6806081,22.3084617 L12.339849,22.6027435 L12.1317324,22.7751865 C11.7665446,23.0727936 11.2431698,23.0751607 10.875305,22.7808688 L10.875305,22.7808688 L10.6577109,22.6007454 L10.3185027,22.3077336 L9.93768106,21.9644669 L9.51991783,21.5725916 L9.29864299,21.3589402 C8.38780103,20.4710024 7.4987173,19.4961299 6.68120581,18.4505757 C4.37779865,15.5046392 3,12.4855681 3,9.5 C3,4.80556525 6.80556525,1 11.5,1 Z M11.5,3 C7.91013475,3 5,5.91013475 5,9.5 C5,11.9561506 6.20278728,14.5917319 8.25676294,17.2186586 C9.01510106,18.1885332 9.84464526,19.0981211 10.6947359,19.9268352 L10.6947359,19.9268352 L11.0974589,20.3121061 C11.2262146,20.4328997 11.3492053,20.5459499 11.4655923,20.6509165 L11.4655923,20.6509165 L11.5,20.681 L11.9024935,20.312145 L12.3052641,19.9268352 C13.1553547,19.0981211 13.9848989,18.1885332 14.7432371,17.2186586 C16.7972127,14.5917319 18,11.9561506 18,9.5 C18,5.91013475 15.0898653,3 11.5,3 Z M11.5,5.5 C13.7091347,5.5 15.5,7.29086525 15.5,9.5 C15.5,11.7091347 13.7091347,13.5 11.5,13.5 C9.29086525,13.5 7.5,11.7091347 7.5,9.5 C7.5,7.29086525 9.29086525,5.5 11.5,5.5 Z M11.5,7.5 C10.3954347,7.5 9.5,8.39543475 9.5,9.5 C9.5,10.6045653 10.3954347,11.5 11.5,11.5 C12.6045653,11.5 13.5,10.6045653 13.5,9.5 C13.5,8.39543475 12.6045653,7.5 11.5,7.5 Z"
          fillRule="nonzero"
        ></path>
      </g>
    </svg>
  ),
  cartview: (
    <svg viewBox="0 0 1024 1024">
      <path d="M213.333333 170.666667a106.666667 106.666667 0 0 1 106.666667 106.666666V853.333333h-213.333333V277.333333A106.666667 106.666667 0 0 1 213.333333 170.666667z m298.666667 256a106.666667 106.666667 0 0 1 106.666667 106.666666V853.333333h-213.333334v-320A106.666667 106.666667 0 0 1 512 426.666667z m298.666667 0a106.666667 106.666667 0 0 1 106.666666 106.666666V853.333333h-213.333333v-320A106.666667 106.666667 0 0 1 810.666667 426.666667z"></path>
    </svg>
  ),
  'arrow-right': (
    <svg viewBox="0 0 44 44">
      <g stroke="none" strokeWidth="1" fillRule="evenodd">
        <path
          d="M21.763456,12 L21.9072346,12.0068647 C22.2418886,12.0388999 22.5678346,12.1830583 22.8241161,12.4393398 L39.0875721,28.7027958 C39.6733585,29.2885822 39.6733585,30.2383297 39.0875721,30.8241161 C38.5017857,31.4099026 37.5520382,31.4099026 36.9662518,30.8241161 L21.763,15.621 L6.56066017,30.8241161 C5.97487373,31.4099026 5.02512627,31.4099026 4.43933983,30.8241161 C3.85355339,30.2383297 3.85355339,29.2885822 4.43933983,28.7027958 L20.7027958,12.4393398 C20.995689,12.1464466 21.3795725,12 21.763456,12 Z"
          transform="translate(21.763456, 21.631728) rotate(-270.000000) translate(-21.763456, -21.631728) "
        ></path>
      </g>
    </svg>
  ),
  'rating-sad': (
    <svg viewBox="0 0 1024 1024">
      <path d="M512 42.666667c259.2 0 469.333333 210.133333 469.333333 469.333333s-210.133333 469.333333-469.333333 469.333333S42.666667 771.2 42.666667 512 252.8 42.666667 512 42.666667z m0 85.333333a384 384 0 1 0 0 768 384 384 0 0 0 0-768z m0 426.666667c104.064 0 180.394667 64.938667 211.157333 157.184a42.666667 42.666667 0 1 1-80.981333 26.965333C622.293333 679.125333 576.298667 640 512 640c-64.298667 0-110.293333 39.125333-130.176 98.816a42.666667 42.666667 0 1 1-80.981333-26.965333C331.605333 619.605333 407.936 554.666667 512 554.666667zM362.666667 341.333333c35.2 0 64 28.8 64 64S397.866667 469.333333 362.666667 469.333333 298.666667 440.533333 298.666667 405.333333 327.466667 341.333333 362.666667 341.333333z m298.666666 0c35.2 0 64 28.8 64 64s-28.8 64-64 64-64-28.8-64-64c0-36.010667 28.8-64 64-64z"></path>
    </svg>
  ),
  'm-in-house-operation-multic': (
    <svg viewBox="0 0 32 32" width="24px" height="24px">
      <path
        d="M9.5 23H22.5V25C22.5 25.5523 22.0523 26 21.5 26H10.5C9.94772 26 9.5 25.5523 9.5 25V23Z"
        fill="white"
        fillOpacity="0.5"
      ></path>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7 7C5.89543 7 5 7.89543 5 9V21C5 22.1046 5.89543 23 7 23H25C26.1046 23 27 22.1046 27 21V9C27 7.89543 26.1046 7 25 7H7ZM20.25 11C19.6977 11 19.25 11.4477 19.25 12V19C19.25 19.5523 19.6977 20 20.25 20H20.75C21.3023 20 21.75 19.5523 21.75 19V12C21.75 11.4477 21.3023 11 20.75 11H20.25ZM14.75 14C14.75 13.4477 15.1977 13 15.75 13H16.25C16.8023 13 17.25 13.4477 17.25 14V19C17.25 19.5523 16.8023 20 16.25 20H15.75C15.1977 20 14.75 19.5523 14.75 19V14ZM11.25 15C10.6977 15 10.25 15.4477 10.25 16V19C10.25 19.5523 10.6977 20 11.25 20H11.75C12.3023 20 12.75 19.5523 12.75 19V16C12.75 15.4477 12.3023 15 11.75 15H11.25Z"
        fill="white"
      ></path>
    </svg>
  ),
};

export default LocalSvgMap;
