import SheinIcon, { IconProps } from '@shein-components/Icon';
import classNames from 'classnames';
import localSvgMap from './local-svg-map';

/**
 * @description Icon
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Icon: React.FC<IconProps> = (props) => {
  const { name, ...restProps } = props;

  if (name in localSvgMap) {
    const { className, style, fontSize, color, onClick } = restProps;
    return (
      <i
        className={classNames(
          'so-icon shein-components_icon_sop_icon shein-components_icon_icon so-icon-default',
          className,
        )}
        style={{
          ...(style || {}),
          fontSize,
          color,
        }}
        onClick={onClick}
      >
        {localSvgMap[name]}
      </i>
    );
  }

  return <SheinIcon name={name} {...restProps} />;
};

export default Icon;
