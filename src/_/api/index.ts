import { API_PREFIX, get, post } from '@/utils';
import axios from 'axios';
import {
  ECommonStatus,
  IDictionaryItem,
  IMenuItem,
  IUserInfo,
  IUserPageAuthItem,
  IWechatConfig,
} from '../interfaces';
/**
 * 用户信息
 * */
export const getUserInfo = (code: string) => {
  return axios.get<Partial<{ code: string; data: IUserInfo; msg: string }>>(
    `${API_PREFIX}/auth/createAuthToken?code=${code}`,
  );
};

/**
 * 获取菜单
 * */
export const getMenu = () => {
  return get<IMenuItem[]>('/auth/getUserPageAuthList');
};

/**
 * 微信sdk配置信息
 * */
export const getWechatConfig = (data: { url: string }) => {
  return post<IWechatConfig>('/wechat/msg/jssdk', data);
};

/**
 * 用户角色权限
 * */
export const getUserPageAuth = () => {
  return get<IUserPageAuthItem[]>('/auth/getUserPageButtonAuthList');
};

/**
 * 获取字典枚举
 * */
export const getDictionaryByCode = (data: { code: string; status?: ECommonStatus }) => {
  return get<IDictionaryItem[]>('/emp/h5/supplier/selectChildrenDict', data);
};

/**
 * 查询省
 * */
export const getProvince = () => {
  return get<{ provinceId: string; provinceName: string }[]>('/emp/h5/supplier/getProvinces');
};

/**
 * 查询市
 * */
export const getCityByProvince = (data: { provinceName: string }) => {
  return get<{ cityId: string; cityName: string }[]>('/emp/h5/supplier/getCities', data);
};

/**
 * im登录
 * */
export const imLogin = () => {
  return get<IUserInfo>('/auth/imLogin');
};
