import React, { useEffect, useRef } from 'react';

interface IUseNoPullDownProps {
  containerRef: React.MutableRefObject<HTMLElement>;
}

/**
 * @description isScrollable
 * @param {unknown} element desc
 * @returns {unknown} desc
 */
const isScrollable = (element: HTMLElement) => {
  return element.scrollHeight > element.clientHeight || element.scrollWidth > element.clientWidth;
};

/**
 * @description findCanScrollEl
 * @param {unknown} target desc
 * @returns {unknown} desc
 */
const findCanScrollEl = (target) => {
  let parentNode = target.parentNode;
  if (isScrollable(target)) {
    return target;
  }
  while (parentNode) {
    if (isScrollable(parentNode as HTMLElement)) {
      return parentNode;
    }
    parentNode = parentNode.parentNode;
  }
  return null;
};

/**
 * @description useNoPullDown
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const useNoPullDown = (props: IUseNoPullDownProps) => {
  const { containerRef } = props;
  const startYRef = useRef(0);
  const currentScrollElement = useRef<HTMLElement>(null);

  useEffect(() => {
    const container = containerRef.current;

    /**
     * @description handleTouchStart
     * @param {unknown} event desc
     * @returns {unknown} desc
     */
    const handleTouchStart = (event: TouchEvent) => {
      startYRef.current = event.touches[0].clientY;
      currentScrollElement.current = findCanScrollEl(event.target);
    };

    /**
     * @description handleTouchEnd
     * @returns {unknown} desc
     */
    const handleTouchEnd = () => {
      startYRef.current = 0;
      currentScrollElement.current = null;
    };

    /**
     * @description handleTouchMove
     * @param {unknown} event desc
     * @returns {unknown} desc
     */
    const handleTouchMove = (event: TouchEvent) => {
      if (event.touches.length > 1) {
        event.preventDefault();
        return;
      }
      const touch = event.touches[0];
      const element = currentScrollElement.current || container;
      const isYScrollable = element.scrollHeight > element.clientHeight;
      const isXScrollable = element.scrollWidth > element.clientWidth;
      const isAtTop = element.scrollTop === 0;
      const isAtBottom = element.scrollTop + element.clientHeight === element.scrollHeight;

      if (!isYScrollable && isAtTop && !isXScrollable) {
        event.preventDefault();
      } else if (isYScrollable && touch.clientY > startYRef.current && isAtTop && !isXScrollable) {
        if (event.cancelable) {
          event.preventDefault();
        }
      } else if (
        isYScrollable &&
        isAtBottom &&
        touch.clientY < startYRef.current &&
        !isXScrollable
      ) {
        if (event.cancelable) {
          event.preventDefault();
        }
      }
    };

    container.addEventListener('touchstart', handleTouchStart, false);
    container.addEventListener('touchmove', handleTouchMove, {
      passive: false,
    });
    container.addEventListener('touchend', handleTouchEnd, false);

    return () => {
      container.removeEventListener('touchstart', handleTouchStart, false);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd, false);
    };
  }, [containerRef]);
};

export default useNoPullDown;
