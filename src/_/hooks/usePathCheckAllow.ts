import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { post } from '@/utils';
import { t } from '@shein-bbl/react';
import { Toast } from 'shineout-mobile';

/**
 * 校验是否允许访问页面
 * https://soapi.sheincorp.cn/application/3694/routes/171389/doc
 * 状态码，默认0表示成功，失败时为异常码
 * @param path
 * @returns
 */
const checkBusinessOnlineService = (path) => {
  return post<number>(
    '/businessOnlineConfiguration/checkBusinessOnline',
    {
      allowPageCode: path,
    },
    {
      showErrorMsg: false,
    },
  );
};

export const usePathCheckAllow = (path?: string) => {
  const location = useLocation();
  const [isAllowed, setIsAllowed] = useState<boolean>(false);

  useEffect(() => {
    let params;
    if (path === undefined) {
      params = location.hash.replace('#', '');
    } else {
      params = path;
    }

    checkBusinessOnlineService(params)
      .then((res) => {
        console.log('checkBusinessOnlineService.res', res);
        if (res === 0) {
          console.log(t('允许访问'));
          setIsAllowed(true);
        } else {
          Toast.fail(t('不允许访问'));
          setIsAllowed(false);
        }
      })
      .catch((err) => {
        console.log('checkBusinessOnlineService.err', err);
        Toast.fail(err.msg);
        setIsAllowed(false);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return { isAllowed };
};
