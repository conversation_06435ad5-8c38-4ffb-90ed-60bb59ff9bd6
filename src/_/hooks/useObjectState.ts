import { useReducer } from 'react';

const useObjectState = <State extends Record<string, any> = any>(initialState: State) => {
  const stateReducer = (state, newState) => {
    let pendingState;
    if (typeof newState === 'function') {
      // 严格模式下，开发环境此函数会被触发两次，具体原因可查看 https://legacy.reactjs.org/docs/strict-mode.html#detecting-unexpected-side-effects
      pendingState = newState?.(state);
    } else {
      pendingState = newState;
    }
    return {
      ...state,
      ...(pendingState || {}),
    };
  };
  const [state, setState] = useReducer(stateReducer, initialState || {});

  return [
    state as State,
    setState as (newState: Partial<State> | ((prevState: State) => Partial<State>)) => void,
  ] as const;
};

export default useObjectState;
