import { useEffect, useRef } from 'react';
import { get } from '@/utils';
import { useRequest } from 'ahooks';
import { Toast } from 'shineout-mobile';

type IProps<T, P> = {
  request: (_payload?: P) => Promise<T>;
  pollingInterval?: number;
  prefix?: string;
};

/*
 * 通用轮序接口
 */
const pollingRequest = <T>(taskId?: string, prefix?: string) => {
  return get<{ code?: string; data?: T; status?: string; msg?: string }>(
    `/${prefix}/common/request`,
    {
      id: taskId,
    },
  );
};

const usePollingRequest = <T, P>(props: IProps<T, P>) => {
  const { request, pollingInterval = 3000, prefix = 'Home' } = props;

  const intervalRef = useRef<NodeJS.Timeout>();

  const firstRequest = useRequest((_payload?: P) => request(_payload), {
    manual: true,
  });

  const polling = useRequest((_taskId?: string) => pollingRequest<T>(_taskId, prefix), {
    manual: true,
  });

  const startPolling = async (_payload?: P) => {
    const taskId = await firstRequest.runAsync(_payload);

    return new Promise<T | undefined>((resolve, reject) => {
      intervalRef.current = setInterval(async () => {
        try {
          const response = await polling.runAsync(taskId as string);

          if (response?.status === '0' && response?.code === '0') {
            // 成功，停止轮询
            resolve(response?.data);
            clearInterval(intervalRef.current);
          } else if (response?.status === '200') {
            // 继续轮训
          } else {
            // 网络错误，停止轮训
            reject();
            clearInterval(intervalRef.current);
            Toast.fail(response?.msg);
          }
        } catch (_e) {
          reject();
          clearInterval(intervalRef.current);
        }
      }, pollingInterval);
    });
  };

  /**
   * @description stopPolling
   * @returns {unknown} desc
   */
  const stopPolling = () => {
    clearInterval(intervalRef.current);
  };

  useEffect(() => {
    return () => {
      clearInterval(intervalRef.current);
    };
  }, []);

  return {
    startPolling,
    stopPolling,
  };
};

export default usePollingRequest;
