import { useEffect } from 'react';
import { debounce, getRootElementFontSize } from '@/utils';

/**
 * @param designWidth 设计稿尺寸，通常为375
 * */
const useRem = (designWidth = 375) => {
  useEffect(() => {
    let rootEl = window.document.documentElement;
    const oldRootElFontSize = rootEl.style.fontSize;
    /**
     * @description setFontSize
     * @returns {unknown} desc
     */
    const setFontSize = () => {
      rootEl.style.fontSize = getRootElementFontSize(designWidth) + 'px';
    };
    const resizeListener = debounce(setFontSize, 300);
    window.addEventListener('orientationchange', resizeListener, false);
    window.addEventListener('resize', resizeListener, false);
    setFontSize();
    return () => {
      window.removeEventListener('orientationchange', resizeListener, false);
      window.removeEventListener('resize', resizeListener, false);
      if (oldRootElFontSize) {
        rootEl.style.fontSize = oldRootElFontSize;
      } else {
        rootEl.removeAttribute('style');
      }
      rootEl = null;
    };
  }, [designWidth]);
};

export default useRem;
