import { useMount } from 'ahooks';

const useVconsole = () => {
  // 环境变量
  const env = process.env.BUILD_TYPE;

  // 判断是否在手机设备中打开
  const isMobile = /Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent);

  const injectConsole = async () => {
    if (env !== 'sit' && env !== 'prod' && isMobile) {
      try {
        const vConsole = await import('vconsole');
        new vConsole.default();
      } catch {
        // do nothing
      }
    }
  };

  useMount(() => {
    injectConsole();
  });
};

export default useVconsole;
