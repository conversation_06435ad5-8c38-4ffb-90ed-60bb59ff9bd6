import { useCallback, useMemo, useRef, useState } from 'react';

/**
 * @desc 用于提高用户交互。比如用户点击一个按钮做异步操作时，通常，我们都是直接
 * 设置一个loading来防止重复点击，但是当响应足够快的时候完全可以不用立即loading
 * 的形式来减少页面抖动。pending用于防止重复请求，loading用于展示加载状态
 * */
const useDelayLoading = (delayTime = 300) => {
  const [pending, setPending] = useState(false);
  const [loading, setLoading] = useState(false);
  const timerRef = useRef<NodeJS.Timeout>(null);

  const startLoading = useCallback(() => {
    setPending(true);
    timerRef.current = setTimeout(() => {
      setLoading(true);
    }, delayTime);
  }, [delayTime]);

  /**
   * @description stopLoading
   * @returns {unknown} desc
   */
  const stopLoading = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    setLoading(false);
    setPending(false);
  };

  return useMemo(() => {
    return [pending, loading, startLoading, stopLoading] as const;
  }, [loading, pending, startLoading]);
};

export default useDelayLoading;
