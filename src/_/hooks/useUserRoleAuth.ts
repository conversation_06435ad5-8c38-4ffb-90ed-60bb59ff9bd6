import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { getUserRoleAuth, userStore } from '@/_/lego-stores/userStore';
import { t } from '@shein-bbl/react';
import { useMount } from 'ahooks';

/**
 * @description useUserRoleAuth
 * @returns {unknown} desc
 */
const useUserRoleAuth = <ButtonKeys extends string = string>(): {
  loading: boolean;
  data: Record<ButtonKeys, boolean>;
} => {
  userStore.useMount();

  const { auth, authLoading } = userStore.useState();
  const location = useLocation();

  useMount(() => {
    getUserRoleAuth();
  });

  const pageAuth = useMemo(() => {
    if (!auth || authLoading) {
      return undefined;
    }
    return auth.find((item) => item.url === location.pathname);
  }, [auth, authLoading, location.pathname]);

  if (!pageAuth) {
    return { loading: authLoading, data: null } as {
      loading: boolean;
      data: Record<ButtonKeys, boolean>;
    };
  }
  const authCodeMap: Record<string, boolean> = {};
  pageAuth.buttonList.forEach((item) => {
    if (!item.code) {
      console.warn(`${item.name}${t(' 未配置权限code，请配置权限code，并根据code做逻辑判断')}`);
    }
    authCodeMap[item.code || item.name] = true;
  });

  return {
    loading: authLoading,
    data: authCodeMap as Record<ButtonKeys, boolean>,
  };
};

export default useUserRoleAuth;
