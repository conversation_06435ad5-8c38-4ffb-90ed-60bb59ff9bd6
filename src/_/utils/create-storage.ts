/**
 * @description 检测字符串是否为 JSON 对象
 * @param {string} str 字符串
 * @returns {boolean} 是否为 JSON 对象
 */
const isObject = (str: string): boolean => {
  try {
    const obj = JSON.parse(str);
    return typeof obj === 'object' && obj !== null;
  } catch (e) {
    return false;
  }
};

type StorageOption = 'session' | number;
type SetItemErrorCallback = (error: Error) => void;

/**
 * createStorage 用于创建一个 storage 对象
 * 默认 localStorage
 * 具有对象(/数组)存储和解析功能，setItem 自动转换为字符串，getItem 返回对象,
 * 具有过期时间功能 opt > 0，自动托管过期时间，过期自动删除，getItem 返回 null
 * @param {String} key 键
 * @param {String|Number} opt 默认 0, 'session':仅当前页面 | 0: 永久 | >0: 过期时间，单位：ms
 * @param {Function} onSetItemError 设置值时的错误回调
 * @returns
 */
const createStorage = (
  key: string,
  opt: StorageOption = 0,
  onSetItemError: SetItemErrorCallback = () => {
    /*  */
  },
) => {
  const typeStorage = opt === 'session' ? sessionStorage : localStorage;

  const storage = {
    // 在本地存储中设置存储值
    setItem: (value: string | string[]) => {
      let parsedValue = value;
      // 如果是对象字符串，尝试解析为对象，解析失败则抛出错误回调
      if (typeof value === 'string' && (value.indexOf('{') === 0 || value.indexOf('[') === 0)) {
        try {
          parsedValue = JSON.parse(value);
        } catch (error) {
          onSetItemError(error as Error);
          return null;
        }
      }

      // 如果传入的值是一个对象，先转成 JSON 字符串
      if (typeof value === 'object') {
        parsedValue = JSON.stringify(value);
      }
      // 在本地存储中设置键-值对
      typeStorage.setItem(key, parsedValue as string);

      // 如果设置了过期时间，则设置一个键来保存过期时间
      if (typeof opt === 'number' && opt > 0) {
        try {
          const expirationTime = Date.now() + opt;
          const expiration = JSON.parse(localStorage.getItem('expiration') || '{}');
          expiration[key] = expirationTime;
          localStorage.setItem('expiration', JSON.stringify(expiration));
        } catch (e) {
          console.log(e);
        }
      }
    },
    // 从本地存储中获取值
    getItem: () => {
      const value = typeStorage.getItem(key);

      // 如果值为 null 或 undefined，返回 null
      if (value === null || value === undefined) {
        return null;
      }

      // 如果值是 JSON 字符串，解析成对象
      if (isObject(value)) {
        // isObject 解析不过的话进不到这个 if 语句，所以不用 try catch
        return JSON.parse(value);
      }
      return value;
    },
    // 从本地存储中删除值
    removeItem: () => {
      typeStorage.removeItem(key);
    },
    // 检测存储值是否过期 (过期/无值返回 true )
    isExpired: () => {
      try {
        const expiration = JSON.parse(localStorage.getItem('expiration') || '{}');
        const expirationTime = expiration[key];
        // 若过期时间不存在, 返回 true
        if (!expirationTime) {
          return true;
        }
        // 若当前时间大于过期时间, 返回 true
        return Date.now() > parseInt(expirationTime, 10);
      } catch (error) {
        console.error('Local Storage Error: ', error);
        return true;
      }
    },
  };

  return storage;
};

export default createStorage;
