import { reduxStore } from '@/configuraStore';
import { getWXOauthUrl } from '@/layouts/components/App';
import {
  getSessionHasUserInfo,
  getSessionUserInfo,
  getUserInfoErr,
  isInSheinIm,
  setSessionHasUserInfo,
  setSessionUserInfo,
  setUserInfoErr,
} from '@/utils';
import { t } from '@shein-bbl/react';
import { namespace } from '@shein-lego/ak.macro';
import { conf } from 'sheinq';
import { Toast } from 'shineout-mobile';
import * as api from '../api';
import { IMenuItem, IUserInfo, IUserPageAuthItem } from '../interfaces';

interface IUserStore extends Record<string, any> {
  info: IUserInfo;
  homeMenu: IMenuItem[];
  workbenchMenu: IMenuItem[];
  personalMenu: IMenuItem[];
  auth: IUserPageAuthItem[];
  authLoading: boolean;
}

const getInitialUserInfo = () => {
  try {
    const info = JSON.parse(getSessionUserInfo());
    if (info) {
      conf({
        customAuthInfoSync: {
          name: info.staffName,
          enName: info.enName,
          emplid: info.workNum,
        },
        plugin: {
          error: {
            // 仅生产环境才需要发送到日志中心进行告警
            sendErrorToLogcenter: process.env.BUILD_TYPE === 'prod',
          },
        },
      });
    }
    return info;
  } catch {
    return null;
  }
};

export const userStore = reduxStore.createStoreSlice<IUserStore>({
  namespace,
  initialState: {
    homeMenu: [],
    workbenchMenu: [],
    personalMenu: [],
    info: getInitialUserInfo(),
    auth: null,
    authLoading: false,
  },
});

const __d = userStore.defineFire;

export const USER_INFO_ERROR_CODE = 'GET_INFO_ERROR';

export const getUserInfo = __d((__s, __g) => (code?: string) => {
  const { info } = __g();
  if (isInSheinIm() && !info) {
    api.imLogin().then((info) => {
      if (!info) {
        Toast.fail(t('未获取到用户信息'));
        return;
      }
      __s((s) => {
        s.info = info;
        setSessionUserInfo(JSON.stringify(info));
        conf({
          customAuthInfoSync: {
            name: info.staffName,
            enName: info.enName,
            emplid: info.workNum,
          },
          plugin: {
            error: {
              // 仅生产环境才需要发送到日志中心进行告警
              sendErrorToLogcenter: process.env.BUILD_TYPE === 'prod',
            },
          },
        });
        getMenu();
      });
    });
  } else if (!info && (!getSessionHasUserInfo() || Number(getSessionHasUserInfo()) < 3)) {
    api
      .getUserInfo(code)
      .then((result) => {
        if (+result.data?.code === 0) {
          __s((s) => {
            s.info = result.data?.data;
            setSessionUserInfo(JSON.stringify(result.data?.data));
            conf({
              customAuthInfoSync: {
                name: result.data?.data?.staffName,
                enName: result.data?.data?.enName,
                emplid: result.data?.data?.workNum,
              },
              plugin: {
                error: {
                  // 仅生产环境才需要发送到日志中心进行告警
                  sendErrorToLogcenter: process.env.BUILD_TYPE === 'prod',
                },
              },
            });
            getMenu();
          });
        } else if (+result.data?.code === 400107) {
          location.replace(getWXOauthUrl());
        } else if (+result.data?.code === -1) {
          setSessionUserInfo(JSON.stringify(USER_INFO_ERROR_CODE));
          Toast.fail(result.data?.msg);
          setUserInfoErr(JSON.stringify(result.data?.data));
        }
      })
      .catch((err) => {
        Toast.fail(err?.message);
      })
      .finally(() => {
        // 用于标示是否获取过用户信息，最多获取3次
        if (!getSessionHasUserInfo()) {
          setSessionHasUserInfo('1');
        } else {
          let hasUserInfo = Number(getSessionHasUserInfo());
          hasUserInfo++;
          setSessionHasUserInfo(String(hasUserInfo));
        }
      });
  } else if (info?.toString() === USER_INFO_ERROR_CODE) {
    try {
      const userInfoErr = JSON.parse(getUserInfoErr());
      Toast.fail(userInfoErr.msg);
    } catch {
      Toast.fail(t('获取用户信息异常'));
    }
  } else {
    getMenu();
  }
});

export const getMenu = __d((__s) => () => {
  api.getMenu().then((info) => {
    __s((s) => {
      // const allConfigMenu = (info || []).find((item) => item.path === '/')?.children || [];
      s.homeMenu = ((info || []).find((item) => item.path === '/')?.children || []).filter(
        (item) => item.name !== t('全部'),
      );
      s.workbenchMenu = (info || []).find((item) => item.path === '/workbench')?.children || [];
      s.personalMenu = (info || []).find((item) => item.path === '/my')?.children || [];
      // s.homeAllGroupMenu = allConfigMenu.filter((item) => item.name === t('全部'));
    });
  });
});

export const getUserRoleAuth = __d((__s, __g) => () => {
  const { auth } = __g();
  if (!auth) {
    __s((s) => {
      s.authLoading = true;
    });
    api
      .getUserPageAuth()
      .then((data) => {
        __s((s) => {
          s.auth = data;
          s.authLoading = false;
        });
      })
      .catch(() => {
        __s((s) => {
          s.authLoading = false;
        });
      });
  }
});
