import { reduxStore } from '@/configuraStore';
import { IDishListItem, IDishListItemWithAmount } from '@/pages/canteen/_/interfaces';
import { IStallListItem } from '@/pages/canteen/stall/interfaces';
import { namespace } from '@shein-lego/ak.macro';

export type ICurrentStall = IStallListItem & {
  /** 餐厅id */
  canteenId: number;
  /** 餐段类型code */
  mealTimePeriodTypeCode: number;
  /** 食堂名称 */
  canteenName?: string;
  /** 餐段类型名称 */
  mealTimePeriodTypeName?: string;
  /** yyyy-MM-dd */
  date: string;
};

export type ICartInfo = {
  /** 档口id */
  stallId: number;
  /** 菜品列表 */
  dishList: IDishListItemWithAmount[];
};

interface ICanteenStore extends Record<string, unknown> {
  currentStall?: ICurrentStall;
  /** 购物车信息 */
  cartInfo?: ICartInfo;
}

export const canteenStore = reduxStore.createStoreSlice<ICanteenStore>({
  namespace,
  initialState: {},
});

export const changeCurrentStall = canteenStore.defineFire(
  (setState) => (val: ICanteenStore['currentStall']) => {
    setState((state) => {
      state.currentStall = val;
      // 切换档口时，清空购物车内存数据
      state.cartInfo = undefined;
    });
  },
);

/** 购物车缓存 */
export const CANTENEN_CART_CACHE_KEY = 'canteen_cart';

export const changeCartInfo = canteenStore.defineFire(
  (setState) => (value: IDishListItemWithAmount) => {
    setState((state) => {
      const { cartInfo, currentStall } = state;
      if (!cartInfo) {
        state.cartInfo = {
          stallId: currentStall.id,
          dishList: [value].filter((item) => item.buyCount),
        };
      } else {
        let { dishList } = cartInfo;
        const dish = dishList?.find((item) => item.id === value.id);
        if (dish) {
          dish.buyCount = value.buyCount;
        } else {
          dishList = [...(dishList || []), value];
        }
        cartInfo.dishList = dishList.filter((item) => item.buyCount);
        state.cartInfo = cartInfo;
      }
      localStorage.setItem(CANTENEN_CART_CACHE_KEY, JSON.stringify(state.cartInfo));
    });
  },
);

/** 进入页面时，购物车默认使用缓存 */
export const initCartInfoCache = canteenStore.defineFire(
  (setState, getState) => (dishList: IDishListItem[]) => {
    setState((state) => {
      const { currentStall } = getState();
      const cartInfoCacheJSON = localStorage.getItem(CANTENEN_CART_CACHE_KEY);
      const cartInfoCache = cartInfoCacheJSON
        ? (JSON.parse(cartInfoCacheJSON) as ICanteenStore['cartInfo'])
        : undefined;
      // 如果缓存不存在，或者缓存中的档口id与当前档口id不一致，则不使用缓存
      if (!cartInfoCache || cartInfoCache.stallId !== currentStall.id) {
        state.cartInfo = undefined;
        return;
      }
      // 过滤掉菜品列中不存在的菜品，并更新菜品信息
      const cartDishList = (cartInfoCache?.dishList || [])
        .map((item) => {
          const dish = dishList.find((dish) => dish.id === item.id);
          if (dish) {
            return {
              ...dish,
              buyCount: item.buyCount,
            };
          }
          return null;
        })
        .filter(Boolean);

      const cartInfo = {
        stallId: currentStall.id,
        dishList: cartDishList,
      };
      state.cartInfo = cartInfo;
    });
  },
);

/** 清空购物车缓存 */
export const clearCartInfoCache = canteenStore.defineFire((setState) => () => {
  setState((state) => {
    state.cartInfo = undefined;
    localStorage.removeItem(CANTENEN_CART_CACHE_KEY);
  });
});
