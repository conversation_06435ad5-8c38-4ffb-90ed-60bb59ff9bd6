import { t } from '@shein-bbl/react';
/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-prototype-builtins */
/* eslint-disable prettier/prettier */
(function (global, factory) {
  if (typeof module === 'object' && typeof module.exports === 'object') {
    // CommonJS
    module.exports = global.document
      ? factory(global, true)
      : function (w) {
          if (!w.document) {
            throw new Error('Geetest requires a window with a document');
          }
          return factory(w);
        };
  } else {
    factory(global);
  }
})(typeof window !== 'undefined' ? window : this, function (window, noGlobal) {
  if (typeof window === 'undefined') {
    throw new Error('Geetest requires browser environment');
  }
  var document = window.document;
  var Math = window.Math;
  var head = document.getElementsByTagName('head')[0];

  /**
   * @description _Object
   * @param {unknown} obj desc
   * @returns {unknown} desc
   */
  function _Object(obj) {
    this._obj = obj;
  }

  _Object.prototype = {
    _each: function (process) {
      var _obj = this._obj;
      for (var k in _obj) {
        if (_obj.hasOwnProperty(k)) {
          process(k, _obj[k]);
        }
      }
      return this;
    },
  };
  /**
   * @description Config
   * @param {unknown} config desc
   * @returns {unknown} desc
   */
  function Config(config) {
    var self = this;
    new _Object(config)._each(function (key, value) {
      self[key] = value;
    });
  }

  Config.prototype = {
    protocol: 'http://',
    _extend: function (obj) {
      var self = this;
      new _Object(obj)._each(function (key, value) {
        self[key] = value;
      });
    },
  };
  /**
   * @description isNumber
   * @param {unknown} value desc
   * @returns {unknown} desc
   */
  var isNumber = function (value) {
    return typeof value === 'number';
  };
  /**
   * @description isString
   * @param {unknown} value desc
   * @returns {unknown} desc
   */
  var isString = function (value) {
    return typeof value === 'string';
  };
  /**
   * @description isBoolean
   * @param {unknown} value desc
   * @returns {unknown} desc
   */
  var isBoolean = function (value) {
    return typeof value === 'boolean';
  };
  /**
   * @description isObject
   * @param {unknown} value desc
   * @returns {unknown} desc
   */
  var isObject = function (value) {
    return typeof value === 'object' && value !== null;
  };
  /**
   * @description isFunction
   * @param {unknown} value desc
   * @returns {unknown} desc
   */
  var isFunction = function (value) {
    return typeof value === 'function';
  };
  var callbacks = {};
  var status = {};
  /**
   * @description loadScript
   * @param {unknown} url desc
   * @param {unknown} cb desc
   * @returns {unknown} desc
   */
  var loadScript = function (url, cb) {
    var script = document.createElement('script');
    script.charset = 'UTF-8';
    script.async = true;
    script.onerror = function () {
      cb(true);
    };
    var loaded = false;
    script.onload = script.onreadystatechange = function () {
      if (
        !loaded &&
        (!script.readyState || 'loaded' === script.readyState || 'complete' === script.readyState)
      ) {
        loaded = true;
        setTimeout(function () {
          cb(false);
        }, 0);
      }
    };
    script.src = url;
    head.appendChild(script);
  };
  var normalizeDomain = function (domain) {
    // return domain.replace(/^https?:\/\/|\/$/g, '');
    return domain.replace(/^(https?:\/\/|)(.+?)(\/?)$/, '$2$3');
  };
  /**
   * @description normalizePath
   * @param {unknown} path desc
   * @returns {unknown} desc
   */
  var normalizePath = function (path) {
    path = path.replace(/\/+/g, '/');
    if (path.indexOf('/') !== 0) {
      path = '/' + path;
    }
    return path;
  };
  /**
   * @description normalizeQuery
   * @param {unknown} query desc
   * @returns {unknown} desc
   */
  var normalizeQuery = function (query) {
    if (!query) {
      return '';
    }
    var q = '?';
    new _Object(query)._each(function (key, value) {
      if (isString(value) || isNumber(value) || isBoolean(value)) {
        q = q + encodeURIComponent(key) + '=' + encodeURIComponent(value) + '&';
      }
    });
    if (q === '?') {
      q = '';
    }
    return q.replace(/&$/, '');
  };
  /**
   * @description makeURL
   * @param {unknown} protocol desc
   * @param {unknown} domain desc
   * @param {unknown} path desc
   * @param {unknown} query desc
   * @returns {unknown} desc
   */
  var makeURL = function (protocol, domain, path, query) {
    domain = normalizeDomain(domain);
    console.log('domain', domain);

    var url = normalizePath(path) + normalizeQuery(query);
    if (domain) {
      url = protocol + domain + url;
    }

    return url;
  };
  /**
   * @description load
   * @param {unknown} protocol desc
   * @param {unknown} domains desc
   * @param {unknown} path desc
   * @param {unknown} query desc
   * @param {unknown} cb desc
   * @returns {unknown} desc
   */
  var load = function (protocol, domains, path, query, cb) {
    /**
     * @description tryRequest
     * @param {unknown} at desc
     * @returns {unknown} desc
     */
    var tryRequest = function (at) {
      var url = makeURL(protocol, domains[at], path, query);
      loadScript(url, function (err) {
        if (err) {
          if (at >= domains.length - 1) {
            cb(true);
          } else {
            tryRequest(at + 1);
          }
        } else {
          cb(false);
        }
      });
    };
    tryRequest(0);
  };
  /**
   * @description throwError
   * @param {unknown} errorType desc
   * @param {unknown} config desc
   * @returns {unknown} desc
   */
  var throwError = function (errorType, config) {
    var errors = {
      networkError: t('网络错误'),
    };
    if (typeof config.onError === 'function') {
      config.onError(errors[errorType]);
    } else {
      throw new Error(errors[errorType]);
    }
  };
  /**
   * @description detect
   * @returns {unknown} desc
   */
  var detect = function () {
    return !!window.Geetest;
  };
  if (detect()) {
    status.slide = 'loaded';
  }
  /**
   * @description initGeetest
   * @param {unknown} userConfig desc
   * @param {unknown} callback desc
   * @returns {unknown} desc
   */
  var initGeetest = function (userConfig, callback) {
    var config = new Config(userConfig);
    if (userConfig.https) {
      config.protocol = 'https://';
    } else if (!userConfig.protocol) {
      config.protocol = window.location.protocol + '//';
    }
    var type = config.type;
    /**
     * @description init
     * @returns {unknown} desc
     */
    var init = function () {
      callback(new window.Geetest(config));
    };
    callbacks[type] = callbacks[type] || [];
    var s = status[type] || 'init';
    if (s === 'init') {
      status[type] = 'loading';
      callbacks[type].push(init);
      load(config.protocol, config.static_servers, config[type], null, function (err) {
        if (err) {
          status[type] = 'fail';
          throwError('networkError', config);
        } else {
          status[type] = 'loaded';
          var cbs = callbacks[type];
          for (var i = 0, len = cbs.length; i < len; i = i + 1) {
            var cb = cbs[i];
            if (isFunction(cb)) {
              cb();
            }
          }
          callbacks[type] = [];
        }
      });
    } else if (s === 'loaded') {
      init();
    } else if (s === 'fail') {
      throwError('networkError', config);
    } else if (s === 'loading') {
      callbacks[type].push(init);
    }
  };
  window.initGeetest = initGeetest;
  return initGeetest;
});
