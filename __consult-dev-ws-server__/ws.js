/* eslint-disable @typescript-eslint/no-var-requires,@shein-bbl/bbl/translate-i18n-byT */
const Websocket = require('ws');
const moment = require('moment');
const httpServer = require('http').createServer();
const PORT = 8188;

const WebSocketServer = Websocket.Server;

const server = new WebSocketServer({
  server: httpServer,
});

const msgContain = (req, msg) => {
  return decodeURIComponent(req.data?.content || '').includes(msg);
};

const robotAvatar =
  'https://lt-lpmp-test.oss-cn-shenzhen.aliyuncs.com/lpmp-test/70141680773766768/robot_avatar.png?Expires=4894853767&OSSAccessKeyId=LTAI5tAe7TDu76LHtRcY8sPL&Signature=ykOQ4YwUHiQ%2BQ2N78hUpGUrrfgI%3D';
const arifiAvatar =
  'https://lt-lpmp-test.oss-cn-shenzhen.aliyuncs.com/lpmp-test/93911680773800849/artificial_avatar.png?Expires=4894853801&OSSAccessKeyId=LTAI5tAe7TDu76LHtRcY8sPL&Signature=v3Rew%2FaBVGw2nCtKUf2VxQms15M%3D';

const s = JSON.stringify;

server.on('connection', (socket) => {
  console.log('a user connected');
  socket.on('open', () => {
    console.log('建立连接');
  });
  socket.send(
    s({
      event: 'MESSAGE',
      data: {
        content: '您好，欢迎使用员工助手，请问有什么可以帮您',
        msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        contentType: 0,
        msgType: 'NORMAL_TEXT',
        avatar: robotAvatar,
      },
      errorMsg: null,
    }),
  );
  socket.send(
    s({
      event: 'MESSAGE',
      data: {
        content: JSON.stringify([
          { id: 1, title: '年终奖金什么时候发放' },
          { id: 2, title: '每日餐补什么时候发放' },
          { id: 3, title: '离职手续办理' },
          { id: 4, title: '那就是的宁静啊的' },
          { id: 5, title: '单身的那家的那就是的' },
          { id: 6, title: 'and看见暗淡扩大' },
          { id: 7, title: '你单肩但侃大山' },
          { id: 8, title: '但可单可打开你打' },
        ]),
        msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        contentType: 0,
        msgType: 'SYSTEM_COMMON_QUESTION',
        avatar: robotAvatar,
        history: true,
        questionList: [
          { id: 1, title: '年终奖金什么时候发放' },
          { id: 2, title: '每日餐补什么时候发放' },
          { id: 3, title: '离职手续办理' },
          { id: 4, title: '那就是的宁静啊的' },
          { id: 5, title: '单身的那家的那就是的' },
          { id: 6, title: 'and看见暗淡扩大' },
          { id: 7, title: '你单肩但侃大山' },
          { id: 8, title: '但可单可打开你打' },
        ],
      },
      errorMsg: null,
    }),
  );
  socket.on('close', () => {
    console.log('user disconnected');
  });
  socket.on('message', (msg) => {
    const req = JSON.parse(msg.toString());
    console.log('req', req);
    if (req.event === 'NEED_SERVICE') {
      socket.send(
        s({
          event: 'NEED_SERVICE',
          errorMsg: null,
          requestId: req.requestId,
        }),
      );
      socket.send(
        s({
          event: 'MESSAGE',
          data: {
            msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            content: '您好，人工客服 001号为您服务',
            msgType: 'SYSTEM_NOTICE',
            avatar: arifiAvatar,
          },
          errorMsg: null,
          requestId: req.requestId,
        }),
      );
      setTimeout(() => {
        socket.send(
          s({
            event: 'MESSAGE',
            data: {
              msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
              content: '您好，请问有什么可以帮您?',
              msgType: 'NORMAL_TEXT',
              avatar: arifiAvatar,
            },
            errorMsg: null,
            requestId: req.requestId,
          }),
        );
        socket.send(
          s({
            event: 'SERVICE_ACCESS',
            data: null,
            errorMsg: null,
          }),
        );
      }, 2000);
    }
    if (req.event !== 'MESSAGE') {
      return;
    }
    if (msgContain(req, '超时消息')) {
      return;
    } else if (req.data?.content?.includes('延迟消息')) {
      setTimeout(() => {
        socket.send(
          s({
            event: 'MESSAGE_RECEIVED',
            data: {
              msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            },
            errorMsg: null,
            requestId: req.requestId,
          }),
        );
      }, 2000);
    } else {
      socket.send(
        s({
          event: 'MESSAGE_RECEIVED',
          data: {
            msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
          },
          errorMsg: null,
          requestId: req.requestId,
        }),
      );
    }
    if (req.data.msgType === 'USER_COMMON_QUESTION') {
      socket.send(
        s({
          event: 'MESSAGE',
          data: {
            msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            content: '等着吧，着什么急，小心不给你发',
            msgType: 'SYSTEM_COMMON_QUESTION_RES',
            avatar: arifiAvatar,
          },
          errorMsg: null,
          requestId: req.requestId,
        }),
      );
    }
    if (req.data?.content.includes('无客服接待')) {
      socket.send(
        s({
          event: 'MESSAGE',
          data: {
            msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            content: '等着吧，着什么急，小心不给你发',
            msgType: 'NO_SERVICE',
            avatar: arifiAvatar,
            consultId: 10,
          },
          errorMsg: null,
        }),
      );
    }
    if (req.data?.content.includes('关键字空')) {
      socket.send(
        s({
          event: 'MESSAGE',
          data: {
            msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            content: null,
            msgType: 'KEY_WORD_RES',
            avatar: robotAvatar,
          },
          errorMsg: null,
        }),
      );
    }
    if (req.data?.content.includes('未评价')) {
      socket.send(
        s({
          event: 'MESSAGE',
          data: {
            msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            content: null,
            msgType: 'SYSTEM_EVALUATE',
            evaluate: false,
            consultId: '1234',
            avatar: arifiAvatar,
          },
          errorMsg: null,
        }),
      );
    }
    if (req.data?.content.includes('已评价')) {
      socket.send(
        s({
          event: 'MESSAGE',
          data: {
            msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            content: null,
            msgType: 'SYSTEM_EVALUATE',
            evaluate: true,
            consultId: '1234',
            avatar: arifiAvatar,
          },
          errorMsg: null,
        }),
      );
    }
    if (req.data?.content.includes('图片消息')) {
      socket.send(
        s({
          event: 'MESSAGE',
          data: {
            msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            content: 'http://pic.imeitou.com/uploads/allimg/210524/3-2105241R940.jpg',
            msgType: 'NORMAL_TEXT',
            contentType: 1,
            avatar: arifiAvatar,
          },
          errorMsg: null,
        }),
      );
    }
    if (msgContain(req, '即将结束')) {
      socket.send(
        s({
          event: 'SERVICE_COMING_END',
          data: {
            msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            msgType: 'NORMAL_TEXT',
            contentType: 1,
            avatar: robotAvatar,
          },
          errorMsg: null,
        }),
      );
    }
    if (msgContain(req, '关闭')) {
      socket.close();
    }
    if (msgContain(req, '多开')) {
      socket.send(
        s({
          event: 'SIGN_OUT',
          data: {
            msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
          },
          errorMsg: null,
        }),
      );
    }
    if (msgContain(req, '会话过期')) {
      socket.send(
        s({
          event: 'NO_USER',
          data: {
            msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
          },
          errorMsg: null,
        }),
      );
    }
    if (req.data?.content.includes('问题无匹配')) {
      socket.send(
        s({
          event: 'MESSAGE',
          data: {
            msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            content: null,
            msgType: 'SYSTEM_COMMON_QUESTION_RES',
            avatar: robotAvatar,
          },
          errorMsg: null,
        }),
      );
    }
    if (msgContain(req, '主动结束')) {
      socket.send(
        s({
          event: 'SERVICE_END',
          data: {
            msgTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            content: null,
            msgType: 'NORMAL_TEXT',
            avatar: robotAvatar,
          },
          errorMsg: null,
        }),
      );
      socket.close();
    }

    if (msgContain(req, '正在输入')) {
      socket.send(
        s({
          event: 'TYPING',
          data: {
            consultWorkNum: '1234',
          },
          errorMsg: null,
        }),
      );
    }

    if (msgContain(req, '撤回')) {
      socket.send(
        s({
          event: 'MESSAGE_WITHDRAW',
          requestId: req.requestId,
        }),
      );
    }
  });
});

server.on('open', () => {
  console.log(`websocket server running at http://localhost:${PORT}/`);
});

server.on('close', () => {
  console.log('ws close');
});

server.on('error', () => {
  console.log('ws error');
});

httpServer.listen(PORT, () => {
  console.log(`socket service start on ${PORT}`);
});
