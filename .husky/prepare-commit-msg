#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# branch=$(git symbolic-ref --short HEAD)
# {
#   ticket=$(echo $branch | grep -o 'YQCP-[0-9]\{4,\}')
# } || {
#     ticket=$(echo $branch | grep -o 'OFC-[0-9]\{4,\}')
# } || {
#     ticket=$(echo $branch | grep -o 'GZSCC-[0-9]\{4,\}')
# } ||
# {
#    ticket=$(echo $branch | grep -o 'GEARS-[0-9]\{4,\}')
# } || {
#   echo "警告：当前分支命名不规范，不会将需求单号添加到 commit 信息中。";
# }
# if [ -n "$ticket" ]; then
#     if [ -z "$1" ]; then
#         echo "Error: commit message file path not provided."
#         exit 1
#     fi
#     if [ ! -f "$1" ]; then
#         echo "Error: commit message file not found."
#         exit 1
#     fi
#     if [ ! -s "$1" ]; then
#         echo "Error: commit message file is empty."
#         exit 1
#     fi
#     # 获取 commit message 的前缀
#     prefix=$(head -n 1 "$1" | grep -o '^\w\+')
#     # 如果是支持的前缀，则添加 ticket 号
#     case $prefix in
#         build|chore|ci|docs|feat|fix|perf|refactor|revert|style|test)
#         sed -i.bak "1s/^$prefix:/$prefix($ticket):/" "$1"
#             ;;
#         *)
#             echo "警告：未知的 commit message 前缀 $prefix，不会将分支号添加到 commit 信息中。"
#             ;;
#     esac
# fi
