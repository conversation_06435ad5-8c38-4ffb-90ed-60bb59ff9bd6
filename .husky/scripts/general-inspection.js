#!/usr/bin/env node
const { spawnSync } = require('child_process');
const traverse = require('@babel/traverse').default;
const parser = require('@babel/parser');
const generator = require('@babel/generator').default;
const path = require('path');
const fs = require('fs');
const babelTypes = require('@babel/types');

// 读取.huskyrules.json内容
const huskyRulesPath = path.resolve(__dirname,'../.huskyrules.json');

// 用户设置的规则配置文件
const huskyRulesContent = fs.readFileSync(huskyRulesPath, {
	encoding: 'utf-8'
});
const {
	onFilterAndRenderItemMatch,
	judgeByCnVerify,
	storageVerify,
	initLoadingCheck,//是否需要检测initLoading
} = (JSON.parse(huskyRulesContent) || {}).rules || {}
const {language, whiteList} = (JSON.parse(huskyRulesContent) || {})
try {
	// 校验关键字
	const keys = ['sessionStorage', 'localStorage'];

	// 白名单方法路径
	// const whiteList = ['src/utils/storage.ts'];

	// 校验不通过列表
	const validationFailSet = new Set();

	// onFilter 与 renderItem属性一致性校验
	const inConsistencySet = new Set();

	// 存在中文判断
	const chineseJudgeSet = new Set();

	//存在view.jsx文件未添加initLoading属性
	const notInitLoadingSet = new Set();

	// 筛选出改动的.ts/.tsx文件
	const filesChanged = spawnSync(
			'git',
			[
				'diff',
				'--cached',
				'--name-only',
				'--diff-filter=ACM',
				'--',
				'*.js',
				'*.jsx',
				`*.${language}`,
				`*.${language}x`,
			],
			{ encoding: 'utf-8' }
	)
			.stdout
			.toString()
			.trim()
			.split('\n')
			.filter(Boolean)
			.filter((filePath) => !filePath.startsWith('.husky/'))
			.filter((filePath) => !whiteList.includes(filePath));

	// 检查的文件列表
	// console.log('📔📔📔fileLists', filesChanged);

	// 处理不同类型的renderItem值，最终返回字符串
	const handleRenderItemNode = (node) => {
		const type = node.value.type;
		// 根据节点类型进行相应处理
		switch (type) {
			case 'StringLiteral':
				// 普通字符串 - renderItem="dictNameZh"
				return node.value.value;
				break;
			case 'JSXExpressionContainer':
				// 查找 (w) => w.dictNameZh 变量的值
				const expression = node.value.expression;
				// 判断是否是箭头函数
				if (expression.type === 'ArrowFunctionExpression' && expression.body) {
					const expressionBody = expression.body;
					// 判断函数体是否是访问成员对象 如: w.name，且是标识符
					if (expressionBody.type === 'MemberExpression' && expressionBody.property.type === 'Identifier') {
						return expressionBody.property.name;
					}
				}
				break;
			default:
				return '';
		}
	};

	// 处理onFilter节点
	const handleFilterNode = (node) => {
		if (!node) {
			return;
		}
		const expression = node.value.expression;
		const expressionBody = node.value.expression.body;
		// 箭头函数 + 箭头函数 + 函数表达式
		if (expression.type === 'ArrowFunctionExpression' && expressionBody.type === 'ArrowFunctionExpression') {
			// 普通表达式
			if (expressionBody.body.type === 'CallExpression') {
				return expressionBody.body?.callee?.object?.callee?.object?.property.name;
			} else if (expressionBody.body.type === 'BinaryExpression') {
				// 包含二元操作符 例如 >
				return expressionBody.body?.left?.callee?.object?.callee?.object?.property?.name;
			}
		}
		return '';
	};


	// 是否通过校验
	for (const file of filesChanged) {
		// 读取每一个改动的文件
		const content = fs.readFileSync(file, 'utf-8')
				.toString();
		const ast = parser.parse(content, {
			sourceType: 'module',
			plugins: ['typescript', 'jsx'],
		});

		let hasInit = false;
		let hasInitLoading = false;
		let hasViewEle = false;
		let isDivEleNoInitLoading = false

		// 1. 判断onFilter与renderItem属性是否一致
		traverse(ast, {
			JSXOpeningElement(path) {
				// 找出select标签
				if (path.node.name.name === 'Select') {
					let onFilterName = '';
					let renderItemName = '';
					// 获取select标签的属性
					const attributes = path.node.attributes;
					for (const attribute of attributes) {
						if (
								attribute.type === 'JSXAttribute' &&
								attribute.name?.name === 'onFilter'
						) {
							onFilterName = attribute;
						} else if (
								attribute.type === 'JSXAttribute' &&
								attribute.name.name === 'renderItem'
						) {
							renderItemName = attribute;
						}
					}
					renderItemName = handleRenderItemNode(renderItemName);
					onFilterName = handleFilterNode(onFilterName);
					// 比较 onFilter 与 renderItem 属性是否一致
					if (onFilterName && renderItemName && renderItemName !== onFilterName) {
						inConsistencySet.add(file);
					}
				}
			},

			//判断class类型的view.jsx文件中是否添加了initLoading属性
			ClassMethod(path) {
				if (path.node.key.name === 'componentDidMount' || path.node.key.name === 'componentWillMount') {
					path.traverse({
						MemberExpression(path) {
							try {
								if (path.node.property.name === 'init') {
									hasInit = true;
								}
							} catch (error) {
								console.log('class组件有问题的文件', file);
							}
						},
					});
				}
				if (path.node.key.name === 'render') {
					path.traverse({
						JSXOpeningElement(path) {
							//如果View和initLoading成对出现
							if(path.node.name.name === 'View'){
								hasViewEle = true;
								try {
									if (
											path.node.attributes &&
											path.node.attributes.some(
													(attr) => attr?.name?.name === 'initLoading',
											)
									) {
										hasInitLoading = true;
									}
								} catch (error) {
									console.log('initLoading有问题的文件', file);
								}
							}
						},
					});
					// 判断return元素最外层是不是div
					const returnStatement = path.node.body.body.find((statement) => babelTypes.isReturnStatement(statement));
					if (returnStatement) {
						const returnedElement = returnStatement.argument;

						//如果是div且内部元素均没有找到initLoading属性
						if (
								babelTypes.isJSXElement(returnedElement) && returnedElement.openingElement.name.name === 'div' &&
								returnedElement.openingElement.attributes.every(
										(attr) => attr.name.name !== 'initLoading',
								)
						) {
							isDivEleNoInitLoading = true;
						}
						// 检测条件表达式中的div
						if (babelTypes.isConditionalExpression(returnedElement) && babelTypes.isJSXElement(returnedElement.consequent)) {
							// consequent部分
							const openingElementCon = returnedElement.consequent.openingElement;
							if (openingElementCon.name &&
									openingElementCon.name.name === 'div' &&
									openingElementCon.attributes.every(attr => (
											babelTypes.isJSXIdentifier(attr.name) && attr.name.name !== 'initLoading'
									))) {
								isDivEleNoInitLoading = true;
							}
							// alternate部分
							const openingElementAlt = returnedElement.alternate.openingElement;
							if (openingElementAlt.name &&
									openingElementAlt.name.name === 'div' &&
									openingElementAlt.attributes.every(attr => (
											babelTypes.isJSXIdentifier(attr.name) && attr.name.name !== 'initLoading'
									))) {
								isDivEleNoInitLoading = true;
							}
						}
					}
				}
			},

			//判断function类型的view.jsx文件中是否添加了initLoading属性
			FunctionDeclaration(componentPath) {
				componentPath.traverse({
					ExpressionStatement(path) {
						path.traverse({
							CallExpression(path) {
								if(path.node.callee && (path.node.callee.name === 'useEffect')){
									path.traverse({
										MemberExpression(path) {
											try {
												if (path.node.property.name === 'init') {
													hasInit = true;
												}
											} catch (error) {
												console.log('useEffect-问题路径', file);
											}
										},
									});
									componentPath.traverse({
										JSXOpeningElement(path) {
											if(path.node.name.name === 'View'){
												hasViewEle = true
												try {
													if (
															path.node.attributes &&
															path.node.attributes.some(
																	(attr) => attr?.name?.name === 'initLoading',
															)
													) {
														hasInitLoading = true;
													}
												} catch (error) {
													console.log('initLoading-问题路径', file);
												}
											}
										},
									});
									componentPath.traverse({
										ReturnStatement(path) {
											const returnedElement = path.node.argument;
											try {
												if (
														babelTypes.isJSXElement(returnedElement) && returnedElement.openingElement.name.name === 'div' &&
														returnedElement.openingElement.attributes.every(
																(attr) => attr?.name?.name !== 'initLoading',
														)
												) {
													isDivEleNoInitLoading = true;
												}
											} catch (error) {
												console.log('initLoading-问题路径', file);
											}
										},
									});
								}
							},
						});
					},
				});
			},
		});

		// 如果有init，有View但没有initLoading属性
		if (hasInit && (hasViewEle && !hasInitLoading )) {
			notInitLoadingSet.add(file)
		}else if (hasInit && !hasViewEle && isDivEleNoInitLoading  ) {// 如果有init，没有View ，return的最外层是div 且没有initLoading属性
			notInitLoadingSet.add(file)
		}else { //只要没有init方法，就不用检测是否有initLoading了
			console.log('页面暂不需要添加initLoading属性')
		}

		// 2.判断Storage使用是否规范
		// 移除注释后的代码
		let { code } = generator(ast, { comments: false });
		//  判断是否单独使用storage相关关键字
		if (keys.some((keyWord) => code.includes(keyWord))) {
			validationFailSet.add(file);
		}

		// 判断是否使用中文判断
		const matchArr = code.match(/!?===?\s*?(?:t\()?(["'])\s*[\u4e00-\u9fa5][^'"]*\2\)?|(?:t\()?(["'])\s*[\u4e00-\u9fa5][^'"]*\2\)?\s*!?===?/g
		);
		if (matchArr && matchArr.length !== 0) {
			chineseJudgeSet.add(file);
		}
	}

	// 将 Set 转换为 Array
	const validationFailList = Array.from(validationFailSet);
	const inConsistencyList = Array.from(inConsistencySet);
	const chineseJudgeList = Array.from(chineseJudgeSet);
	const notInitLoadingList = Array.from(notInitLoadingSet);

	if (validationFailList.length !== 0 && storageVerify) {
		console.error(
				'以下文件被检测出local/session使用不规范，请修改为公共方法：createStorage'
		);
		validationFailList.forEach((file) => {
			console.error(`🥹🥹Error: ${file}`);
		});
		process.exit(1);
	}

	if (inConsistencyList.length !== 0 && onFilterAndRenderItemMatch) {
		console.error(
				'以下文件被检测出Select组件onFilter与renderItem属性不一致，请二次确认!'
		);
		inConsistencyList.forEach((file) => {
			console.error(`🤨🤨Error: ${file}`);
		});
		process.exit(1);
	}

	if (chineseJudgeList.length !== 0 && judgeByCnVerify) {
		console.error(
				'以下文件被检测出使用中文判断，请修改！'
		);
		chineseJudgeList.forEach((file) => {
			console.error(`🥹🥹Error: ${file}`);
		});
		process.exit(1);
	}
	if (notInitLoadingList.length !== 0 && initLoadingCheck) {
		console.error('以下view.jsx文件被检测出未添加initLoading属性，请修改！');
		notInitLoadingList.forEach((file) => {
			console.error(`🥹🥹Error: ${file}`);
		});
		process.exit(1);
	}
} catch (e) {
	console.error(e);
	console.error('🧐🧐联系修改人员,检查报错general-inspection脚本报错');
}
