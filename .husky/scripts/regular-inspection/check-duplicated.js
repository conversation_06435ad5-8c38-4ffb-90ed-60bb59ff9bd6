const fs = require('fs');
const path = require('path');
const glob = require('glob');
const parser = require('@babel/parser');
const t = require('@babel/types');
const traverse = require('@babel/traverse').default;
const srcPath = path.resolve(__dirname, '../../../src');

try{
	/**
	 * -------获取legorc中的defineInProcessEnv------------
	 */
// 你的文件路径
	const filePath = path.join(__dirname, '../../../config/.legorc.ts');
	const fileContent = fs.readFileSync(filePath, 'utf8');
// 将 TypeScript 代码转换为 AST
	const legorcAst = parser.parse(fileContent, {
		sourceType: 'module',
		plugins: ['typescript'],
	});

	let defineInProcessEnv = {};

// 使用 traverse 遍历 AST，并找到 defineInProcessEnv 属性

	traverse(legorcAst, {
		CallExpression(path) {
			const node = path.node;
			if (
				t.isIdentifier(node.callee, { name: 'defineConfig' }) &&
				node.arguments.length === 1 &&
				t.isObjectExpression(node.arguments[0])
			) {
				const properties = node.arguments[0].properties;
				properties.forEach(prop => {
					if (
						t.isIdentifier(prop.key, { name: 'defineInProcessEnv' }) &&
						t.isObjectExpression(prop.value)
					) {
						const envProperties = prop.value.properties;
						envProperties.forEach(envProp => {
							if (envProp.value.arguments[0].value) {
								defineInProcessEnv[envProp.key.name] = envProp.value.arguments[0].value
							}
						});
					}
				});
			}
		},
	});

	/**
	 * -------处理项目逻辑------------
	 */


// 定义一个对象，用于存储所有已经出现过的 URL
	const urlMap = {};

// 定义一个对象，用于存储重复的 URL
	const duplicatedUrlArrSet = new Set();
	/**
	 * 累加quasis函数中的 value.raw
	 * @param {*} quasis
	 * @returns
	 */
	function sumRawValues(quasis) {
		return quasis.reduce((total, current) => total + current.value.raw, '');
	}
	// 读取.huskyrules.json内容
	const huskyRulesPath = path.resolve(__dirname,'../../.huskyrules.json');

// 用户设置的规则配置文件
	const huskyRulesContent = fs.readFileSync(huskyRulesPath, {
		encoding: 'utf-8'
	});

	const {language} = (JSON.parse(huskyRulesContent) || {})


// 遍历 src 目录下所有的 .ts 文件
	glob
		.sync(`${srcPath}/**/*.${language}`, {
			// 忽略文件
			ignore: [
				'**/.lego/**',
				'**/ak/**',
				'**/assets/**',
				'**/hooks/**',
				'**/styles/**',
				'**/utils/**',
				'**/apis/sheinq/**',
				'**/typings/**',
				'**/pages/example/**',
			],
		})
		.forEach((file) => {
			const content = fs.readFileSync(file, 'utf-8');
			// 将 TypeScript 代码转换为 AST
			const ast = parser.parse(content, {
				sourceType: 'module',
				plugins: ['typescript'],
			});
			// 遍历 AST，查找 const url  = 的声明
			traverse(ast, {
				VariableDeclaration(path) {
					if (path.node.kind === 'const') {
						path.node.declarations.forEach((decl) => {
							// 字符串的处理
							if (
								decl.id.name === 'url' &&
								decl.init &&
								decl.init.type === 'StringLiteral'
							) {
								// 如果找到了 const url 声明，判断是否已经出现过相同的值
								const checkUrl = decl.init.value;
								if (urlMap[checkUrl]) {
									duplicatedUrlArrSet.add(checkUrl)
									console.error(`ʕ•̫͡•ʔ*̫͡*ʔ-̫͡-ʔ ~~~ Duplicated URL found in file ${file}: ${checkUrl}`);
								} else {
									urlMap[checkUrl] = true;
								}
							}
							// 模板的处理
							if (
								decl.id.name === 'url' &&
								decl.init && decl.init.type === 'TemplateLiteral'
							) {
								// 遍历模板字符串
								const quasis = decl.init.quasis;
								const expressions = decl.init.expressions;
								let tplUrl = '';
								tplUrl = sumRawValues(quasis)
								// 模板字符串中包含表达式
								const expression = expressions[0];
								// expressions 为空数组的时候代表没有模版, 本身就是url,直接跳过解析
								if (expression) {
									if (expression.type === 'MemberExpression') {
										// 如果 url 的值是一个对象属性，则获取对象名和属性名
										const propName = expression.property.name;
										// 将对象名和属性名组合成一个字符串，作为 URL 的值
										tplUrl =
											defineInProcessEnv[`${propName}`] +
											sumRawValues(quasis)
									} else {
										// console.warn(
										//   `ʕ·͡ˑ·ཻʔ ~~~ Unsupported expression type: ${expression.type}`
										// );
									}
								}

								// 如果找到了 const url 声明，判断是否已经出现过相同的值
								if (urlMap[tplUrl]) {
									duplicatedUrlArrSet.add(tplUrl)
									console.error(`ʕ•̫͡•ʔ*̫͡*ʔ-̫͡-ʔ ~~~ Duplicated URL found in file ${file}: ${tplUrl}`);
								} else {
									urlMap[tplUrl] = true;
								}
							}
						});
					}
				},
			});
		});
	// 将 Set 转换为 Array
	const duplicatedUrlArr = Array.from(duplicatedUrlArrSet);
	// 判断有重复的 提示 禁止提交
	if(duplicatedUrlArr.length >0){
		console.error(`ि०॰०ॢी ~~~~ ${duplicatedUrlArr.length}处,提交的接口有重复声明,请检查完再提交`);
		process.exit(1);
	}
}catch(e){
	console.error(e);
	console.error("~(˶‾᷄ꈊ‾᷅˵)~~~~ 联系修改人员,检查报错check-duplicated脚本报错");
}
