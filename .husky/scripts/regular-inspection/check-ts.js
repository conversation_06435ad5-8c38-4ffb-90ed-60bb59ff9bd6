#!/usr/bin/env node
const { execSync } = require('child_process');
const path = require('path');

try {
  const isskipTsError = process.argv[2];
  const modifyFiles = execSync('git diff --cached --name-status | grep -E "src|config" | grep -E "\.ts$|\.tsx$" || true')
    .toString()
    .split('\n')
    .filter(line => line.length > 0)
    .map(line => line.split('\t')[1]);
  
  if (!modifyFiles.length) {
    console.log('未找到需要ts检测的文件改动');
    return;
  }

  const packagePathList = new Set();
  modifyFiles.forEach((file) => {
    const index = file.indexOf('/src/');
    if (index > -1) {
      const packagePath = file.substring(0, index);
      packagePathList.add(packagePath);
    } else {
      packagePathList.add('root');
    }
  })

  Array.from(packagePathList).forEach((packagePath) => {
    try {
      execSync("npx tsc --noEmit --project tsconfig.json > tsc.log", {
        cwd: packagePath === 'root' ? '.' : packagePath,
      })
    } catch (e) {
      console.error('\x1b[33m%s\x1b[0m', `检测失败，详情请查看${packagePath !== 'root' ? packagePath + '/' : '' }tsc.log文件 \n`);
      if(isskipTsError !== 'true') {
        process.exit(1);
      }
    }
  })

} catch(e) {
  console.error('\x1b[31m%s\x1b[0m', 'check-ts run Error:', e);
  process.exit(1);
}
