const fs = require('fs');
const path = require('path');
const glob = require('glob');
const parser = require('@babel/parser');
const { default: generator } = require('@babel/generator');
const srcPath = path.resolve(__dirname, '../../../src');
try {
  // 校验关键字
  const keys = ['sessionStorage', 'localStorage'];

  // 校验不通过列表
  const validationFailSet = new Set();

  // 读取.huskyrules.json内容
  const huskyRulesPath = path.resolve(__dirname,'../../.huskyrules.json');

// 用户设置的规则配置文件
  const huskyRulesContent = fs.readFileSync(huskyRulesPath, {
    encoding: 'utf-8'
  });

  const {language, whiteList} = (JSON.parse(huskyRulesContent) || {})



  // 遍历 src 目录下所有的 .ts、.tsx 文件
  glob
    .sync(`${srcPath}/**/*.${language}{,x}`, {
      // 忽略文件
      ignore: [
        '**/.lego/**',
        '**/assets/**',
        '**/hooks/**',
        '**/styles/**',
        '**/apis/sheinq/**',
        '**/typings/**',
        '**/pages/example/**',
        ...(whiteList.map(item => `${srcPath}${item}`)),
      ],
    })
    .forEach((file) => {
      const content = fs.readFileSync(file, 'utf-8');
      // 将 TypeScript 代码转换为 AST
      const ast = parser.parse(content, {
        sourceType: 'module',
        plugins: ['typescript', 'jsx'],
      });
      // 移除注释后的代码
      let { code } = generator(ast, { comments: false });
      //  判断是否单独使用
      if (keys.some((keyWord) => code.includes(keyWord))) {
        validationFailSet.add(file);
      }
    });
  // 判断有重复的 提示 禁止提交

  // 将 Set 转换为 Array
  const validationFailList = Array.from(validationFailSet);

  if (validationFailList.length !== 0) {
    console.error(
      '以下文件被检测出local/session使用不规范，请修改为公共方法：createStorage'
    );
    validationFailList.forEach((file) => {
      console.error(`🥹🥹Error: ${file}`);
    });
    process.exit(1);
  }
} catch (e) {
  console.error(e);
  console.error('🧐🧐联系修改人员,检查报错check-storage脚本报错');
}
