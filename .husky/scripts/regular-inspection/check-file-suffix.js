#!/usr/bin/env node
const { execSync } = require('child_process');
const path = require('path');

const whiteList = ['.ts', '.tsx', '.less', '.css', '.json', '.ejs', '.jpg', '.png', '.mp3', '.html', '.md', '.svg'];
try {
  const addedFiles = execSync('git diff --cached --name-status')
    .toString()
    .split('\n')
    .filter(line => line.startsWith('A'))
    .map(line => line.split('\t')[1]);

  const invalidFiles = addedFiles.filter(file => {
    const ext = path.extname(file);
    return /src\//.test(file) && !whiteList.includes(ext);
  });

  if (invalidFiles.length > 0) {
    console.error('\x1b[31m%s\x1b[0m', 'CheckFileSuffix Error: 请使用.ts / .tsx', invalidFiles.join(','));
    process.exit(1);
  }
  console.log('checkFileSuffix success');
} catch (error) {
  console.error('\x1b[31m%s\x1b[0m', 'CheckFileSuffix Error executing script:', error);
  process.exit(1);
}
