#!/usr/bin/env sh

## Load nvm
#export NVM_DIR="$HOME/.nvm"
#[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
# Load nvm
if [[ -n "$NVM_DIR" ]] && [[ -s "$NVM_DIR/nvm.sh" ]]; then
  echo "存在nvm"
else
  echo "不存在nvm"
  export NVM_DIR="$HOME/.nvm"
  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
fi

if ! command -v jq >/dev/null 2>&1; then
  echo "jq not found, please execute this command: brew install jq"
else
  echo "jq命令存在"
fi

. "$(dirname -- "$0")/_/husky.sh"

# get current branch name
BRANCH_NAME=$(git branch --show-current)

# The commit-msg hook takes one parameter,
# which again is the path to a temporary file that contains the commit message written by the developer.
COMMIT_MESSAGE=$(cat "$1")

# 读取配置项
configPath=$(dirname "$0")
json=$(cat "$configPath/.huskyrules.json")
addBranchName=$(echo $json | jq '.rules.addBranchName')
commitLintEnable=$(echo $json | jq '.rules.commitLintEnable')



if $commitLintEnable ; then
  # --- 提交规范校验
  #--no-install 参数表示强制npx使用项目中node_modules目录中的commitlint包
  npx --no-install commitlint -e $1
else
  echo "关闭commitlint提交规范校验"
fi



if $addBranchName ; then
  # 在commit-msg中拼接开发分支号
  # if commit message not null
  if [ -n "$COMMIT_MESSAGE" ]; then
    # if commit message not null, rewrite to the temporary file
    echo "[${BRANCH_NAME}] ${COMMIT_MESSAGE}" > "$1"
  else
    # if commit message is null, throw error
    echo "Error: Aborting commit due to empty commit message."
    exit 1;
  fi
else
  echo "关闭拼接分支号功能"
fi
