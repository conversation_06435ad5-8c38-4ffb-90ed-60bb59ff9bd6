# husky检测内容说明

## 一、自动触发：

### （1）提交前 pre-commit

1. 检查「修改的文件」是否遵循eslint规范 - .husky/pre-commit
2. 校验「提交信息」是否规范 - .husky/pre-commit,规范详见commitlint.config.js
3. 检查「修改的文件」是否存在不正确的LocalStorage/SessionStorage使用 - .husky/general-inspection.js
4. 检查「修改的文件」是否存在中文判断 - .husky/general-inspection.js
5. 检查「修改的文件」中Select组件renderItem、onFilter属性是否一致 - .husky/general-inspection.js
6. 检查「全局文件」是否存在重复声明的接口 - .husky/check-duplicated.js
7. 检查「修改的view.jsx文件」是否包含initLoading属性
8. 检查「新增的文件」是否以ts/tsx结尾 - .husky/scripts/regular-inspection/check-file-suffix.js

### （2）提交时 commit-msg

1. 自动在提交信息前加上当前开发分支号 - .husky/commit-msg

### （2）推送前 pre-push

1. 检查「当前分支」是否存在不规范的合并记录-dev/test/sit

## 二、手动触发：
> 统一放在.husky/regular-inspection目录下

### 手动执行 node + 脚本名

1. 检查「全局文件」是否存在不正确的LocalStorage/SessionStorage使用 - .husky/regular-inspection/general-inspection.js
